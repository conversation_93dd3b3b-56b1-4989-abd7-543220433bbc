# Universal Product Cards Animation System

## Overview

The Universal Product Cards Animation System provides consistent, performant sliding up entrance animations with lazy loading for all product card types throughout the website. This system replaces individual component animations with a unified, optimized approach.

## Features

### ✅ **Universal Coverage**
- Supports all product card types: `custom-salla-product-card`, `.s-product-card`, `.product-card`
- Works with Salla product lists and custom product grids
- Automatically detects and animates dynamically added cards

### ✅ **Performance Optimized**
- Hardware acceleration with `translate3d()` transforms
- CSS containment for better layout performance
- Proper `will-change` management with automatic cleanup
- `requestAnimationFrame` batching for smooth DOM updates

### ✅ **Lazy Loading**
- Intersection Observer API for efficient viewport detection
- 50px margin for smooth entrance timing
- Automatic cleanup to prevent memory leaks

### ✅ **Responsive Design**
- **Desktop**: 0.8s animation, 30px slide distance, 80ms stagger
- **Mobile (≤768px)**: 0.6s animation, 25px slide distance, 60ms stagger  
- **Small Mobile (≤480px)**: 0.5s animation, 20px slide distance, 50ms stagger

### ✅ **Accessibility**
- Full `prefers-reduced-motion` support
- Instant appearance for users who prefer minimal animations
- Maintains functionality when animations are disabled

## File Structure

```
src/assets/styles/04-components/product-cards-animation.scss
src/assets/js/components/universal-product-cards-animation.js
```

## CSS Classes

### **Animation States**
- `.animate-in` - Applied when card enters viewport
- `.animation-complete` - Applied after animation finishes

### **Loading States**
- `.product-cards-loading` - Container class for loading state
- `.product-cards-loaded` - Container class for loaded state

## JavaScript API

### **Global Functions**

```javascript
// Manually animate specific cards
window.animateProductCards(cards);

// Reset animation state for cards
window.resetProductCards(cards);

// Refresh the entire animation system
window.refreshProductCardsAnimation();
```

### **Access to Main Instance**

```javascript
// Get performance statistics
const stats = window.UniversalProductCardsAnimator.getStats();

// Manually trigger refresh
window.UniversalProductCardsAnimator.refresh();
```

## Animation Timing

### **Desktop (>768px)**
- **Duration**: 0.8s
- **Slide Distance**: 30px
- **Stagger Delay**: 80ms between cards
- **Easing**: `cubic-bezier(0.25, 0.46, 0.45, 0.94)`

### **Mobile (≤768px)**
- **Duration**: 0.6s
- **Slide Distance**: 25px
- **Stagger Delay**: 60ms between cards

### **Small Mobile (≤480px)**
- **Duration**: 0.5s
- **Slide Distance**: 20px
- **Stagger Delay**: 50ms between cards

## Browser Support

### **Modern Browsers**
- Full animation support with Intersection Observer
- Hardware acceleration and CSS containment

### **Legacy Browsers**
- Automatic fallback animation without Intersection Observer
- Graceful degradation with basic transitions

## Integration

### **Automatic Integration**
The system automatically:
- Initializes on DOM ready
- Observes existing product cards
- Watches for dynamically added cards via Mutation Observer
- Handles Salla theme events (`theme::ready`)
- Cleans up on page unload

### **Manual Integration**
For custom implementations:

```javascript
// Animate specific cards
const newCards = document.querySelectorAll('.my-product-cards');
window.animateProductCards(newCards);

// Reset cards for re-animation
window.resetProductCards(existingCards);
```

## Performance Benefits

### **Metrics Improved**
- **First Contentful Paint**: Faster initial rendering
- **Cumulative Layout Shift**: Reduced layout shifts
- **Animation Performance**: Consistent 60 FPS
- **Memory Usage**: Efficient cleanup prevents leaks

### **Optimization Features**
- CSS containment reduces reflow/repaint
- Hardware acceleration for smooth transforms
- Intersection Observer reduces unnecessary work
- Automatic cleanup prevents memory leaks

## Migration from Old System

### **Replaced Components**
- `salla-productlist-animation.js` - Now delegates to universal system
- Individual product card bounce animations - Removed
- Component-specific staggered delays - Unified

### **Compatibility**
- Existing product card markup works without changes
- Old animation classes are safely ignored
- Gradual migration path available

## Troubleshooting

### **Cards Not Animating**
1. Check if cards have proper selectors
2. Verify Intersection Observer support
3. Check for JavaScript errors in console

### **Performance Issues**
1. Monitor with `getStats()` function
2. Check for excessive DOM mutations
3. Verify proper cleanup is occurring

### **Animation Conflicts**
1. Remove old bounce animation CSS
2. Check for conflicting `will-change` properties
3. Verify CSS specificity isn't overriding styles

## Best Practices

### **HTML Structure**
```html
<!-- Salla product cards -->
<salla-products-list>
    <custom-salla-product-card></custom-salla-product-card>
    <custom-salla-product-card></custom-salla-product-card>
</salla-products-list>

<!-- Custom product cards -->
<div class="products-grid">
    <div class="product-card"></div>
    <div class="product-card"></div>
</div>
```

### **CSS Customization**
```scss
// Customize animation timing
custom-salla-product-card {
    transition-duration: 1s; // Override default timing
}

// Add custom hover effects
.product-card:hover {
    transform: translate3d(0, -8px, 0); // Use hardware acceleration
}
```

### **JavaScript Integration**
```javascript
// Wait for system to be ready
document.addEventListener('DOMContentLoaded', () => {
    // System is automatically initialized
    console.log('Product cards animation ready');
});

// Handle dynamic content
function loadMoreProducts() {
    // Add new products to DOM
    // System automatically detects and animates them
}
```

## Future Enhancements

- Additional animation types (fade, scale, rotate)
- Custom timing configuration per component
- Advanced staggering patterns
- Integration with loading states
- Performance monitoring dashboard

## Support

For issues or questions about the Universal Product Cards Animation System:
1. Check browser console for errors
2. Verify CSS and JavaScript files are loaded
3. Test with `window.UniversalProductCardsAnimator.getStats()`
4. Review this documentation for proper usage patterns
