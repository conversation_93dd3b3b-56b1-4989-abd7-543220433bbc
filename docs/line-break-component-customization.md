# خط فاصل مميز - دليل التخصيص الشامل

## نظرة عامة
تم تطوير نظام تخصيص شامل لمكون "خط فاصل مميز" يتيح للمستخدمين التحكم الكامل في مظهر وسلوك الخط الفاصل من خلال واجهة إعدادات الثيم.

## الملفات المُحدثة والمُضافة

### 1. إعدادات الثيم (twilight.json)
**المسار:** `twilight.json`
**التحديث:** تم استبدال الإعداد البسيط بنظام إعدادات شامل يتضمن:

#### إعدادات نمط الخط:
- **نمط الخط:** اختيار من 5 أنماط (متدرج مضيء، مستقيم، متقطع، منقط، مزدوج)
- **لون الخط الأساسي:** منتقي لون للون الرئيسي
- **لون الخط الثانوي:** منتقي لون للتدرج (للنمط المضيء)
- **سماكة الخط:** تحكم رقمي من 1-10 بكسل

#### إعدادات المسافات:
- **المسافة العلوية:** تحكم رقمي من 0-100 بكسل
- **المسافة السفلية:** تحكم رقمي من 0-100 بكسل

#### إعدادات التأثيرات:
- **تفعيل تأثير الإضاءة:** مفتاح تشغيل/إيقاف
- **تفعيل تأثير اللمعان:** مفتاح تشغيل/إيقاف
- **تفعيل الجزيئات المتحركة:** مفتاح تشغيل/إيقاف
- **سرعة الحركة:** اختيار من 5 مستويات سرعة

### 2. ملف CSS المخصص
**المسار:** `src/assets/styles/04-components/line-break-enhanced.scss`
**الوصف:** ملف SCSS شامل يحتوي على:

#### الأنماط الأساسية:
- أنماط مختلفة للخط (gaming, solid, dashed, dotted, double)
- دعم CSS Custom Properties للتخصيص الديناميكي
- تحسينات الأداء مع `will-change` و `contain`

#### التأثيرات المتقدمة:
- تأثيرات الإضاءة والتوهج
- حركات اللمعان المتدرجة
- جزيئات متحركة مع ألوان قابلة للتخصيص

#### الاستجابة والوصولية:
- تصميم متجاوب لجميع أحجام الشاشات
- دعم `prefers-reduced-motion`
- تحسينات للشاشات عالية الكثافة
- دعم الوضع الداكن

#### دعم RTL:
- تدرجات معكوسة للغات من اليمين لليسار
- حركات معكوسة للمعان

### 3. ملف JavaScript المخصص
**المسار:** `src/assets/js/components/line-break-enhanced.js`
**الوصف:** وحدة JavaScript متقدمة تتضمن:

#### إدارة الإعدادات:
- قراءة الإعدادات من نظام Salla Theme
- تطبيق الإعدادات ديناميكياً على العناصر
- دعم القيم الافتراضية

#### تحسينات الأداء:
- Intersection Observer لتحسين الأداء
- إيقاف الحركات عند عدم الرؤية
- تحسينات للمتصفحات المختلفة

#### التخصيص الديناميكي:
- تحديث الألوان والتدرجات
- تطبيق أنماط مختلفة حسب الإعدادات
- إدارة سرعات الحركة

### 4. قالب Twig المُحدث
**المسار:** `src/views/components/home/<USER>
**التحديث:** تم تحديث القالب ليشمل:

#### قراءة الإعدادات:
- استخراج جميع الإعدادات من `component`
- تطبيق القيم الافتراضية

#### بناء الفئات الديناميكية:
- إنشاء فئات CSS حسب الإعدادات
- تطبيق الأنماط المشروطة

#### CSS Custom Properties:
- تمرير الألوان والقيم للـ CSS
- دعم التخصيص الفوري

### 5. التكامل مع النظام
**الملفات المُحدثة:**
- `src/assets/styles/app.scss`: إضافة استيراد CSS
- `src/assets/js/app.js`: إضافة استيراد JavaScript

## كيفية الاستخدام

### 1. إضافة المكون
- اذهب إلى محرر الصفحة الرئيسية
- أضف مكون "خط فاصل مميز"
- ستظهر جميع خيارات التخصيص

### 2. تخصيص النمط
- اختر نمط الخط المطلوب
- حدد الألوان الأساسية والثانوية
- اضبط سماكة الخط

### 3. ضبط المسافات
- حدد المسافة العلوية والسفلية
- القيم بالبكسل من 0-100

### 4. تفعيل التأثيرات
- فعّل/أوقف تأثير الإضاءة
- فعّل/أوقف تأثير اللمعان
- فعّل/أوقف الجزيئات المتحركة
- اختر سرعة الحركة

## الميزات التقنية

### الأداء
- استخدام Intersection Observer
- تحسينات GPU مع `transform3d`
- إيقاف الحركات عند عدم الرؤية
- دعم `will-change` للتحسين

### الوصولية
- دعم `prefers-reduced-motion`
- عناصر `aria-hidden` للقارئات
- تحسينات للوضع الداكن

### التوافق
- دعم جميع المتصفحات الحديثة
- تحسينات للأجهزة المحمولة
- دعم الشاشات عالية الكثافة

### الاستجابة
- تصميم متجاوب كامل
- تحسينات للشاشات الصغيرة
- إخفاء التأثيرات المعقدة على الأجهزة الضعيفة

## أنماط الخط المتاحة

1. **متدرج مضيء (Gaming):** النمط الافتراضي مع تدرجات وتأثيرات
2. **خط مستقيم (Solid):** خط بسيط بلون واحد
3. **خط متقطع (Dashed):** خط متقطع كلاسيكي
4. **خط منقط (Dotted):** خط منقط
5. **خط مزدوج (Double):** خطان متوازيان

## التأثيرات المتاحة

1. **تأثير الإضاءة:** توهج حول الخط
2. **تأثير اللمعان:** حركة لمعان تمر عبر الخط
3. **الجزيئات المتحركة:** نقاط متحركة على الخط

## سرعات الحركة

1. **بطيء جداً:** للتأثيرات الهادئة
2. **بطيء:** سرعة مريحة
3. **عادي:** السرعة الافتراضية
4. **سريع:** للتأثيرات النشطة
5. **سريع جداً:** للتأثيرات الديناميكية

## الصيانة والتطوير

### إضافة أنماط جديدة
1. أضف النمط في `twilight.json`
2. أضف CSS في `line-break-enhanced.scss`
3. أضف المعالجة في `line-break-enhanced.js`

### إضافة تأثيرات جديدة
1. أضف المفتاح في `twilight.json`
2. أضف CSS للتأثير
3. أضف المعالجة في JavaScript

### تحسينات الأداء
- راقب استخدام GPU
- اختبر على الأجهزة الضعيفة
- استخدم Chrome DevTools للتحليل

## الدعم والتوافق

### المتصفحات المدعومة
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### الأجهزة المدعومة
- أجهزة سطح المكتب
- الأجهزة اللوحية
- الهواتف الذكية
- الشاشات عالية الكثافة

هذا النظام يوفر مرونة كاملة في تخصيص مكون الخط الفاصل مع الحفاظ على الأداء والوصولية.
