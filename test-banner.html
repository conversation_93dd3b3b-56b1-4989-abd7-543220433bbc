<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Banner with Offer</title>
    <link rel="stylesheet" href="assets/css/components/banner-with-offer.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .s-block__title {
            text-align: center;
            margin-bottom: 20px;
        }
        .s-block__title h2 {
            font-size: 2rem;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار مكون بنر مع عرض</h1>
        
        <!-- Test Banner Component -->
        <section class="s-block s-block--banner-with-offer container">
            <div class="s-block__title">
                <div class="right-side">
                    <h2>عرض خاص محدود الوقت</h2>
                </div>
            </div>

            <div class="banner-offer-wrapper"
                 id="offer-component-test-123"
                 style="--offer-bg-color: #f5f5f5;">
                <div class="offer-container fadeIn"
                     style="background-color: var(--offer-bg-color);">
                    
                    <!-- Banner Image -->
                    <div class="banner-image placeholder-banner">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">🎁</div>
                            <h3>عرض خاص</h3>
                            <p>قم بإضافة صورة العرض من لوحة التحكم</p>
                        </div>
                    </div>

                    <!-- Overlay -->
                    <div class="overlay"></div>

                    <!-- Optimized particles -->
                    <div class="particles">
                        <div class="particle particle-1"></div>
                        <div class="particle particle-2"></div>
                        <div class="particle particle-3"></div>
                    </div>

                    <!-- محتوى العرض -->
                    <div class="offer-content">
                        <h3 class="offer-title">العرض ينتهي خلال:</h3>
                        
                        <!-- Countdown Timer -->
                        <div id="countdown-timer-test-123" class="countdown-timer"
                             style="color: #ffffff;">
                            <div class="countdown-box">
                                <span id="days-test-123" class="countdown-value">05</span>
                                <span class="countdown-label">أيام</span>
                            </div>
                            <div class="countdown-box">
                                <span id="hours-test-123" class="countdown-value">12</span>
                                <span class="countdown-label">ساعات</span>
                            </div>
                            <div class="countdown-box">
                                <span id="minutes-test-123" class="countdown-value">30</span>
                                <span class="countdown-label">دقائق</span>
                            </div>
                            <div class="countdown-box">
                                <span id="seconds-test-123" class="countdown-value">45</span>
                                <span class="countdown-label">ثواني</span>
                            </div>
                        </div>
                        
                        <!-- Offer ended badge -->
                        <div id="offer-ended-badge-test-123" class="offer-ended-badge">
                            <div class="badge-pattern"></div>
                            <span class="badge-text">نهاية<br>العرض</span>
                        </div>

                        <!-- زر العرض -->
                        <a href="#" class="offer-cta-button">
                            <span class="button-text">تسوق الآن</span>
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- JavaScript Configuration -->
    <script>
    (function() {
        'use strict';
        
        // Initialize global banner config if not exists
        if (!window.bannerOfferConfig) {
            window.bannerOfferConfig = {};
        }
        
        // Configuration for this specific banner instance
        window.bannerOfferConfig['test-123'] = {
            // Timer settings
            timer_enabled: true,
            timer_bg_color: '#FF5722',
            timer_text_color: '#ffffff',
            timer_days: 7,
            
            // Offer settings
            offer_button_enabled: true,
            offer_button_text: 'تسوق الآن',
            offer_bg_color: '#f5f5f5',
            offer_animation: 'fadeIn',
            
            // Additional settings
            unique_id: 'test-123',
            offer_img: '',
            offer_url: '#'
        };
        
        // Apply timer background color immediately
        const timerElement = document.getElementById('countdown-timer-test-123');
        if (timerElement) {
            timerElement.style.setProperty('--timer-bg-color', '#FF5722');
            timerElement.style.setProperty('--timer-text-color', '#ffffff');
        }
        
        // Apply offer background color
        const bannerElement = document.getElementById('offer-component-test-123');
        if (bannerElement) {
            bannerElement.style.setProperty('--offer-bg-color', '#f5f5f5');
            bannerElement.style.setProperty('--timer-bg-color', '#FF5722');
            bannerElement.style.setProperty('--timer-text-color', '#ffffff');
        }
        
        console.log('🎯 Test Banner configuration loaded:', window.bannerOfferConfig['test-123']);
    })();
    </script>

    <!-- Load the banner JavaScript -->
    <script src="assets/js/components/banner-with-offer.js"></script>
</body>
</html>
