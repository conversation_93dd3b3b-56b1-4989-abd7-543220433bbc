<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Banner Configuration</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .debug-section { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; }
        .config-item { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .config-key { font-weight: bold; color: #495057; }
        .config-value { color: #28a745; font-family: monospace; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
    </style>
</head>
<body>
    <h1>🔍 Debug Banner Configuration</h1>
    
    <div class="debug-section">
        <h2>Expected Configuration Structure from twilight.json:</h2>
        <div class="config-item">
            <div class="config-key">Timer Collection:</div>
            <div class="config-value">
                component.timer[0]['timer.bg'] = "#00bfe0"<br>
                component.timer[0]['timer.days'] = number
            </div>
        </div>
        
        <div class="config-item">
            <div class="config-key">Offer Collection:</div>
            <div class="config-value">
                component.offer[0]['offer.img'] = "image_url"<br>
                component.offer[0]['offer.url'] = "link_url"
            </div>
        </div>
        
        <div class="config-item">
            <div class="config-key">Direct Fields:</div>
            <div class="config-value">
                component.timer_enabled = boolean<br>
                component.timer_text_color = "#ffffff"<br>
                component.offer_button_enabled = boolean<br>
                component.offer_button_text = "تسوق الآن"<br>
                component.offer_bg_color = "#f5f5f5"<br>
                component.offer_animation = "fadeIn"
            </div>
        </div>
    </div>

    <div class="debug-section">
        <h2>🎯 Test Configuration:</h2>
        <p>This simulates the exact structure that should come from twilight.json:</p>
        
        <script>
        // Simulate the component data structure from twilight.json
        const testComponent = {
            id: 'test-banner-123',
            title: 'عرض خاص محدود الوقت',
            
            // Timer collection (array with nested objects)
            timer: [{
                'timer.bg': '#00bfe0',
                'timer.days': 7
            }],
            
            // Offer collection (array with nested objects)  
            offer: [{
                'offer.img': 'https://example.com/banner.jpg',
                'offer.url': 'https://example.com/offer'
            }],
            
            // Direct fields
            timer_enabled: true,
            timer_text_color: '#ffffff',
            offer_button_enabled: true,
            offer_button_text: 'تسوق الآن',
            offer_bg_color: '#f5f5f5',
            offer_animation: 'fadeIn'
        };

        // Test data extraction (simulating Twig logic)
        console.log('🧪 Testing data extraction...');
        
        // Test timer data extraction
        const timer_data = testComponent.timer[0] || {};
        const timer_bg = timer_data['timer.bg'];
        const timer_days = timer_data['timer.days'];
        
        console.log('Timer data:', {
            timer_bg: timer_bg,
            timer_days: timer_days,
            raw_timer_data: timer_data
        });
        
        // Test offer data extraction
        const offer_data = testComponent.offer[0] || {};
        const offer_img = offer_data['offer.img'];
        const offer_url = offer_data['offer.url'];
        
        console.log('Offer data:', {
            offer_img: offer_img,
            offer_url: offer_url,
            raw_offer_data: offer_data
        });
        
        // Test direct fields
        console.log('Direct fields:', {
            timer_enabled: testComponent.timer_enabled,
            timer_text_color: testComponent.timer_text_color,
            offer_button_enabled: testComponent.offer_button_enabled,
            offer_button_text: testComponent.offer_button_text,
            offer_bg_color: testComponent.offer_bg_color,
            offer_animation: testComponent.offer_animation
        });

        // Display results on page
        document.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = `
                <div class="config-item">
                    <div class="config-key">Timer Background Color:</div>
                    <div class="config-value ${timer_bg ? 'success' : 'error'}">
                        ${timer_bg || 'NOT FOUND'}
                    </div>
                </div>
                
                <div class="config-item">
                    <div class="config-key">Timer Days:</div>
                    <div class="config-value ${timer_days ? 'success' : 'error'}">
                        ${timer_days || 'NOT FOUND'}
                    </div>
                </div>
                
                <div class="config-item">
                    <div class="config-key">Offer Image:</div>
                    <div class="config-value ${offer_img ? 'success' : 'error'}">
                        ${offer_img || 'NOT FOUND'}
                    </div>
                </div>
                
                <div class="config-item">
                    <div class="config-key">Button Text:</div>
                    <div class="config-value ${testComponent.offer_button_text ? 'success' : 'error'}">
                        ${testComponent.offer_button_text || 'NOT FOUND'}
                    </div>
                </div>
            `;
        });
        </script>
    </div>

    <div class="debug-section">
        <h2>📊 Extraction Results:</h2>
        <div id="results">Loading...</div>
    </div>

    <div class="debug-section">
        <h2>✅ What Should Work Now:</h2>
        <ul>
            <li>Timer background color should be <strong style="color: #00bfe0;">#00bfe0</strong></li>
            <li>Timer should count down for <strong>7 days</strong></li>
            <li>Button text should be <strong>"تسوق الآن"</strong></li>
            <li>All CSS variables should be properly set</li>
            <li>JavaScript should receive correct configuration</li>
        </ul>
    </div>
</body>
</html>
