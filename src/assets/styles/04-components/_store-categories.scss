/* Store Categories Component - Gaming Theme */
:root {
  --color-primary-rgb: 29, 233, 182; /* <PERSON><PERSON> */
  --color-primary: #1DE9B6;
  --color-primary-reverse: #121212;
  --color-dark: #121212;
  --glow-shadow: 0 0 10px rgba(29, 233, 182, 0.5), 0 0 20px rgba(29, 233, 182, 0.3);
}

.s-block--store-categories {
  padding: 3rem 0;
  position: relative;

  &.merge-with-top-component {
    padding-top: 1rem;
  }

  &.merged-has-no-title {
    padding-top: 0;
  }
}

.categories-title {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 5;

  h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-primary);
    text-shadow: 0 0 20px rgba(29, 233, 182, 0.5);
    margin-bottom: 1rem;
    opacity: 0;
    animation: titleAppear 1s ease-out forwards 0.2s;

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
}

.categories-divider {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
  margin: 0 auto;
  position: relative;
  box-shadow: var(--glow-shadow);
  opacity: 0;
  animation: expandDivider 1.5s ease-out forwards 0.5s;
}

.category-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(29, 233, 182, 0.2);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  opacity: 0;
  transform: translateY(30px);
  animation: categoryCardAppear 0.8s ease-out forwards;

  @media (max-width: 768px) {
    padding: 1rem;
  }

  // Animation delays will be set by JavaScript for better control
  // Default delays for fallback
  &:nth-child(1) { animation-delay: 0.3s; }
  &:nth-child(2) { animation-delay: 0.5s; }
  &:nth-child(3) { animation-delay: 0.7s; }
  &:nth-child(4) { animation-delay: 0.9s; }
  &:nth-child(5) { animation-delay: 1.1s; }
  &:nth-child(6) { animation-delay: 1.3s; }
  &:nth-child(7) { animation-delay: 1.5s; }
  &:nth-child(8) { animation-delay: 1.7s; }
  &:nth-child(9) { animation-delay: 1.9s; }
  &:nth-child(10) { animation-delay: 2.1s; }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(29, 233, 182, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-10px);
    border-color: var(--color-primary);
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(29, 233, 182, 0.3);

    &::before {
      opacity: 1;
    }

    .category-image {
      border-color: var(--color-primary);
      box-shadow: var(--glow-shadow);

      &::after {
        opacity: 1;
      }

      img {
        transform: scale(1.1);
      }
    }

    .category-name {
      color: var(--color-primary);
      text-shadow: 0 0 10px rgba(29, 233, 182, 0.5);
    }
  }
}

.category-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto 1rem;
  overflow: hidden;
  border: 2px solid rgba(29, 233, 182, 0.3);
  transition: all 0.3s ease;
  position: relative;

  @media (max-width: 768px) {
    width: 60px;
    height: 60px;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: linear-gradient(45deg, transparent, rgba(29, 233, 182, 0.2), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
}

.category-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  transition: color 0.3s ease;
}

/* Grid layout for desktop (5 or less categories) */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 5;
  justify-items: center;

  // Desktop: show up to 5 categories in a row
  @media (min-width: 1024px) {
    grid-template-columns: repeat(5, 1fr);
    gap: 2.5rem;

    &.categories-4 {
      grid-template-columns: repeat(4, 1fr);
      max-width: 960px;
    }

    &.categories-3 {
      grid-template-columns: repeat(3, 1fr);
      max-width: 720px;
    }

    &.categories-2 {
      grid-template-columns: repeat(2, 1fr);
      max-width: 480px;
    }

    &.categories-1 {
      grid-template-columns: 1fr;
      max-width: 240px;
    }
  }

  @media (min-width: 768px) and (max-width: 1023px) {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
    max-width: 800px;
  }

  @media (max-width: 767px) {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    max-width: 100%;
  }

  // Grid-specific category card adjustments
  .category-card {
    width: 100%;
    max-width: 220px;

    @media (min-width: 1024px) {
      max-width: none;
    }
  }
}

/* Slider specific styles (for more than 5 categories) */
.categories-slider {
  position: relative;
  z-index: 5;

  .swiper-slide {
    height: auto;
    display: flex;
    align-items: stretch;
  }

  // Desktop slider adjustments
  @media (min-width: 1024px) {
    .swiper-wrapper {
      align-items: stretch;
    }

    .swiper-slide {
      width: auto;
      flex: 0 0 20%; // Show 5 slides per view on desktop
      padding: 0 10px;
    }

    // Slider controls styling
    .swiper-button-next,
    .swiper-button-prev {
      color: var(--color-primary);
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      width: 50px;
      height: 50px;
      margin-top: -25px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(29, 233, 182, 0.3);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(29, 233, 182, 0.2);
        border-color: var(--color-primary);
        box-shadow: var(--glow-shadow);
      }

      &::after {
        font-size: 18px;
        font-weight: bold;
      }
    }
  }

  @media (min-width: 768px) and (max-width: 1023px) {
    .swiper-slide {
      flex: 0 0 33.333%; // Show 3 slides per view on tablet
      padding: 0 8px;
    }
  }

  @media (max-width: 767px) {
    .swiper-slide {
      flex: 0 0 50%; // Show 2 slides per view on mobile
      padding: 0 5px;
    }
  }

  // Slider-specific category card adjustments
  .category-card {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

/* Animations */
@keyframes categoryCardAppear {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes titleAppear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandDivider {
  from {
    opacity: 0;
    width: 0;
  }
  to {
    opacity: 1;
    width: 80px;
  }
}
