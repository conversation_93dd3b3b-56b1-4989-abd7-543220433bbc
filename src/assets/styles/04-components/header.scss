
/* Top Nav */
.top-navbar {
  @apply flex py-2 min-h-[48px];

  @screen lg {
    @apply py-1.5;
  }

  .topnav-has-bg & {
    @apply bg-[color:var(--topnav-bg)];
  }

  .topnav-has-gradient & {
    @apply bg-gradient-to-r from-[color:var(--topnav-gradient-from)] to-[color:var(--topnav-gradient-to)];
  }

  .topnav-has-text-color & {
    @apply text-[color:var(--topnav-text-color)];
  }

  .s-search-input{
    @apply bg-gray-200/50 hover:bg-gray-200/70 border-none;
  }

  @media (max-width: 640px) {
    .s-search-results{
      @apply w-screen max-w-[100vw] rtl:-left-2.5 ltr:-right-2.5; 
    }
  } 

  .topnav-is-dark & {
    @apply bg-dark text-gray-300;

    .btn--circle-gray,
    .btn--rounded-gray,
    .s-search-input{
      @apply bg-gray-100/10 hover:bg-gray-100/[0.15]
    }

    .topnav-link-item{
      @apply border-gray-300/10;
    }

    .s-search-input{
      @apply text-white;
    }
  }

  .search-btn{
    @apply grow sm:grow-0 justify-start md:justify-center;
  }
}

// contacs menu items - pages menu items
.topnav-link-item{
  @apply inline-block transition duration-300 px-4 rtl:last:pl-0 ltr:last:pr-0 py-px text-sm leading-none ltr:border-r rtl:border-l border-gray-200 ltr:last:border-0 rtl:last:border-0 hover:opacity-80;

  &.right-side{
    @apply rtl:first:pr-0 ltr:first:pl-0
  }
}

/* Main Nav */
.main-nav-container {
  @apply min-h-[68px] lg:min-h-[84px];
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // Gaming Theme Styling - Default State
  background: linear-gradient(135deg,
    var(--gaming-bg-primary, #0f0f23) 0%,
    var(--gaming-bg-secondary, #1a1a2e) 50%,
    var(--gaming-bg-card, #16213e) 100%);
  border-bottom: 2px solid var(--gaming-accent-blue, #00d4ff);
  box-shadow: 0 4px 20px rgba(0, 212, 255, 0.2);

  // Glow effect
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      transparent 0%,
      var(--gaming-accent-blue, #00d4ff) 20%,
      var(--gaming-accent-purple, #8b5cf6) 50%,
      var(--gaming-accent-blue, #00d4ff) 80%,
      transparent 100%);
    box-shadow: 0 0 15px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3));
    transition: all 0.3s ease;
  }

  // Glass/Blur Effect when scrolled
  &.scrolled-glass {
    background: rgba(15, 15, 35, 0.15);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 2px solid rgba(0, 212, 255, 0.3);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(0, 212, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    &::before {
      opacity: 0.7;
      box-shadow: 0 0 20px var(--gaming-glow-blue, rgba(0, 212, 255, 0.4));
    }

    // Enhanced glass border effect with shimmer
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 30%,
        rgba(0, 212, 255, 0.05) 70%,
        transparent 100%);
      background-size: 200% 100%;
      pointer-events: none;
      border-radius: 0 0 15px 15px;
      animation: glassShimmer 4s ease-in-out infinite;
    }

    // Enhanced mobile layout for glass effect
    @media (max-width: 1023px) {
      background: rgba(15, 15, 35, 0.25);
      backdrop-filter: blur(15px) saturate(150%);
      -webkit-backdrop-filter: blur(15px) saturate(150%);
    }
  }

  // Custom background Color
  .has-bg &,
  .has-bg & .sub-menu{
    @apply bg-[color:var(--mainnav-bg)];
  }

  // Custom text color
  .has-text-color & {
    @apply text-[color:var(--mainnav-text-color)];
  }
}

.menu-item {
  @apply flex items-center px-6 py-2.5 sm:text-sm text-gray-500 transition-colors duration-300 hover:bg-gray-200/30;

  &.logout{
    @apply text-red-400;
  }

  &.is-active{
    @apply text-primary bg-gray-200/20;
  }
}

/* Sticky Header */
.main-nav-container {
  /* ضمان عدم قطع القوائم المنسدلة */
  overflow: visible !important;

  &.animated {
    .inner {
      transition: top 0.5s, transform 0.5s, -webkit-transform 0.5s, opacity 0.4s;
    }
  }

  &.fixed-pinned {
    .inner {
      position: fixed;
      top: 0;
      right: 0;
      left: 0;
      width: 100%;
      z-index: var(--z-header);
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
      transform: translate3d(0, -100%, 0);
      overflow: visible !important; /* ضمان عدم قطع القوائم المنسدلة */

      @media (max-width: 1024px){
        transform: none;
        top: -70px;
      }
    }

    .navbar-brand {
      img {
        max-height: 40px;
      }

      h4 {
        line-height: 1;
      }
    }

    .main-menu > li > a {
      padding-top: 20px;
      padding-bottom: 20px;
    }
  }

  &.fixed-header {
    .inner {
      transform: translate3d(0, 0, 0);

      @media (max-width: 1024px){
        transform: none;
        top: 0;
      }
    }
  }
}

.navbar-brand {
  @apply items-center flex my-2 lg:my-0;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 0 15px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3)));
  }

  img {
    @apply w-auto max-h-12 max-w-[100px] xs:max-w-[170px];
    transition: all 0.3s ease;
    border-radius: 8px;

    &:hover {
      box-shadow: 0 0 20px var(--gaming-glow-blue, rgba(0, 212, 255, 0.4));
    }
  }
}

// Mainnav cart icon - Gaming Theme
.header-btn{
  @apply border-none outline-none transition-all duration-300;

  &:hover {
    transform: translateY(-2px);
    filter: drop-shadow(0 4px 8px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3)));
  }

  &__icon{
    @apply text-xl w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300;
    background: linear-gradient(135deg,
      var(--gaming-bg-card, #16213e) 0%,
      var(--gaming-bg-secondary, #1a1a2e) 100%);
    border: 2px solid var(--gaming-accent-blue, #00d4ff);
    color: var(--gaming-text-primary, #ffffff);
    box-shadow: 0 0 10px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3));

    &:hover {
      border-color: var(--gaming-accent-purple, #8b5cf6);
      box-shadow: 0 0 15px var(--gaming-glow-purple, rgba(139, 92, 246, 0.3));
      color: var(--gaming-accent-purple, #8b5cf6);
    }

    &.icon{
      @apply mr-[9px] rtl:ml-[9px] rtl:mr-[unset];
    }
  }
}

salla-user-menu{
  @apply shrink-0;
  position: relative !important; /* إصلاح المشكلة الأساسية - منع static positioning */
  overflow: visible !important; /* ضمان عدم قطع القائمة المنسدلة */
  z-index: 100 !important; /* ضمان ظهور العنصر فوق العناصر الأخرى */
  display: block !important; /* ضمان العرض الصحيح */

  // تصميم الثيم الجيمنج لقائمة المستخدم
  .s-user-menu-avatar {
    border: 2px solid var(--gaming-accent-blue, #00d4ff);
    box-shadow: 0 0 10px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3));
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--gaming-accent-purple, #8b5cf6);
      box-shadow: 0 0 15px var(--gaming-glow-purple, rgba(139, 92, 246, 0.3));
      transform: scale(1.05);
    }
  }

  // ضمان أن القائمة المنسدلة لها z-index صحيح في الثيم الجيمنج
  .dropdown__menu {
    z-index: 99999 !important;
    position: absolute !important;
    display: block !important;
  }

  // إصلاح حالة الفتح
  .dropdown-toggler {
    position: relative !important; /* ضمان relative positioning للـ toggler */

    &.is-opened .dropdown__menu {
      z-index: 99999 !important;
      position: absolute !important;
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
      pointer-events: auto !important;
      transform: translateY(0) scale(1) !important;
    }
  }
}

// cart summary - Gaming Theme
.s-cart-summary-total{
  @apply font-[600];
  color: var(--gaming-text-primary, #ffffff);
  text-shadow: 0 0 5px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3));
}

.s-cart-summary-count{
  @apply -top-0.5 ltr:-left-1.5 rtl:-right-1.5;
  background: linear-gradient(135deg,
    var(--gaming-accent-pink, #ff0080) 0%,
    var(--gaming-accent-purple, #8b5cf6) 100%);
  box-shadow: 0 0 10px var(--gaming-glow-purple, rgba(139, 92, 246, 0.3));
  border: 1px solid var(--gaming-accent-pink, #ff0080);
}

#nav-cart{
  @apply flex items-center rtl:mr-4 ltr:ml-4 relative whitespace-nowrap;

  .icon{
    @apply rtl:ml-2 ltr:mr-2;
  }

  span{
    @apply absolute top-1 rtl:-right-2 ltr:-left-2;
  }
}


// Mobile Menu Burger - Gaming Theme - Performance Optimized
.mburger {
  .sicon-menu {
    color: var(--gaming-accent-blue, #00d4ff);
    transition: transform 0.3s ease, color 0.3s ease, filter 0.3s ease;
    filter: drop-shadow(0 0 5px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3)));
    will-change: transform;
    transform: translate3d(0, 0, 0);

    &:hover {
      color: var(--gaming-accent-purple, #8b5cf6);
      filter: drop-shadow(0 0 10px var(--gaming-glow-purple, rgba(139, 92, 246, 0.3)));
      transform: scale3d(1.1, 1.1, 1);
    }
  }
}

// Search
.header-search{
  .s-search-results{
    @apply z-10;
  }
}

/* Gaming Theme Header Enhancements - Performance Optimized */
.store-header {
  position: relative;
  contain: layout style;
  overflow: visible !important; /* ضمان عدم قطع القوائم المنسدلة */

  // Add subtle animation to the entire header with hardware acceleration
  .main-nav-container {
    animation: headerGlow 3s ease-in-out infinite alternate;
    will-change: box-shadow;
    transform: translate3d(0, 0, 0);
    contain: layout style paint;
  }
}

// Gaming Header Navigation specific styling
.gaming-header-nav {
  background: linear-gradient(135deg,
    var(--gaming-bg-primary, #0f0f23) 0%,
    var(--gaming-bg-secondary, #1a1a2e) 50%,
    var(--gaming-bg-card, #16213e) 100%) !important;
  border-bottom: 2px solid var(--gaming-accent-blue, #00d4ff) !important;
  box-shadow: 0 4px 20px rgba(0, 212, 255, 0.2) !important;
  overflow: visible !important; /* ضمان عدم قطع القوائم المنسدلة */

  .inner {
    background: transparent !important;
    overflow: visible !important; /* ضمان عدم قطع القوائم المنسدلة */
  }

  // Desktop layout specific styling
  @media (min-width: 1024px) {
    min-height: 120px; // Increase height for two-row layout
    overflow: visible !important; // ضمان عدم قطع القوائم المنسدلة

    .navbar-brand {
      img {
        max-height: 60px; // Larger logo for center position
        max-width: 200px;
      }
    }

    // Border for menu separator
    .border-gaming-border {
      border-color: var(--gaming-border, #2d3748);
    }

    // Center the main menu
    custom-main-menu {
      display: flex;
      justify-content: center;
      width: 100%;
    }

    // Enhanced spacing for icons layout
    .flex.items-center.justify-between {
      overflow: visible !important; // ضمان عدم قطع القوائم المنسدلة

      .flex.items-center {
        min-width: 120px; // Ensure consistent spacing for both sides
        overflow: visible !important; // ضمان عدم قطع القوائم المنسدلة

        // Cart icon on left side styling
        &:first-child {
          justify-content: flex-start;
        }

        // Profile icon on right side styling
        &:last-child {
          justify-content: flex-end;
          overflow: visible !important; // ضمان عدم قطع قائمة البروفايل
        }
      }
    }
  }
}

/* إصلاحات شاملة للحاويات لضمان ظهور قائمة البروفايل */
.store-header,
.main-nav-container,
.gaming-header-nav,
.container,
.inner {
  overflow: visible !important;
  position: relative;
}

/* إصلاح خاص للنافبار الجيمنج */
.gaming-header-nav {
  overflow: visible !important;

  .container {
    overflow: visible !important;
    position: relative;
  }

  .inner {
    overflow: visible !important;
    position: relative;
  }

  /* ضمان أن عناصر النافبار لا تقطع القوائم المنسدلة */
  .flex {
    overflow: visible !important;
  }

  .flex.items-center {
    overflow: visible !important;
  }

  .flex.items-stretch {
    overflow: visible !important;
  }
}

/* إصلاح نهائي لمشكلة static positioning في قائمة البروفايل */
salla-user-menu,
salla-user-menu .dropdown-toggler {
  position: relative !important; /* منع static positioning نهائياً */
}

salla-user-menu .dropdown__menu {
  position: absolute !important; /* القائمة المنسدلة يجب أن تكون absolute */
  z-index: 99999 !important;
}

// Gaming header glow animation
@keyframes headerGlow {
  0% {
    box-shadow: 0 4px 20px rgba(0, 212, 255, 0.2);
  }
  100% {
    box-shadow: 0 4px 25px rgba(0, 212, 255, 0.3), 0 0 30px rgba(139, 92, 246, 0.1);
  }
}

// Glass effect animation
@keyframes glassShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Enhanced glass effect for scrolled state
.main-nav-container.scrolled-glass {
  // Add subtle shimmer effect
  &::after {
    animation: glassShimmer 3s ease-in-out infinite;
  }

  // Enhanced backdrop blur support for different browsers
  @supports (backdrop-filter: blur(20px)) {
    backdrop-filter: blur(20px) saturate(180%);
  }

  @supports (-webkit-backdrop-filter: blur(20px)) {
    -webkit-backdrop-filter: blur(20px) saturate(180%);
  }

  // Fallback for browsers without backdrop-filter support
  @supports not (backdrop-filter: blur(20px)) {
    background: rgba(15, 15, 35, 0.85);
  }

  // Smooth transition for all child elements
  .container {
    transition: all 0.3s ease;
  }

  // Enhanced icon effects when scrolled
  .header-btn__icon {
    box-shadow: 0 0 15px var(--gaming-glow-blue, rgba(0, 212, 255, 0.4));
  }

  .navbar-brand img {
    filter: drop-shadow(0 0 10px var(--gaming-glow-blue, rgba(0, 212, 255, 0.2)));
  }
}

// Enhanced container styling for gaming theme
.main-nav-container .container {
  position: relative;
  z-index: 2;
}

// Gaming theme text colors
.main-nav-container {
  color: var(--gaming-text-primary, #ffffff);

  a {
    color: var(--gaming-text-primary, #ffffff);
    transition: all 0.3s ease;

    &:hover {
      color: var(--gaming-accent-blue, #00d4ff);
      text-shadow: 0 0 5px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3));
    }
  }
}

/* Desktop Header Layout Enhancements */
@media (min-width: 1024px) {
  .gaming-header-nav {
    // Two-row layout styling
    .container > div:not(.lg\\:hidden) {
      // Top row (Profile - Logo - Cart)
      > div:first-child {
        border-bottom: 1px solid rgba(0, 212, 255, 0.2);
        padding-bottom: 1rem;
        margin-bottom: 1rem;
      }

      // Bottom row (Menu)
      > div:last-child {
        padding-top: 0.5rem;
      }
    }

    // Enhanced logo styling for center position
    .navbar-brand {
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        background: linear-gradient(45deg,
          transparent,
          rgba(0, 212, 255, 0.1),
          transparent);
        border-radius: 12px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover::before {
        opacity: 1;
      }
    }
  }
}