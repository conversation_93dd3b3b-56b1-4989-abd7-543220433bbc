/* Categories Dropdown Component */
.categories-dropdown-container {
  position: relative;
  display: inline-block;
}

/* زر التصنيفات */
.categories-trigger-btn {
  @apply flex items-center gap-2 px-4 py-2 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-all duration-200;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  
  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
  
  &:focus {
    outline: none;
    ring: 2px;
    ring-color: #3b82f6;
    ring-opacity: 0.5;
  }
  
  .categories-text {
    @apply hidden sm:inline;
  }
  
  .categories-arrow {
    transition: transform 0.2s ease;
    
    &.rotated {
      transform: rotate(180deg);
    }
  }
}

/* القائمة المنسدلة */
.categories-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  min-width: 320px;
  max-width: 600px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: var(--z-categories-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  max-height: 70vh;
  overflow-y: auto;
  
  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
  
  /* تحسين التمرير */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    
    &:hover {
      background: #94a3b8;
    }
  }
}

/* شاشة التحميل */
.categories-loader {
  @apply flex flex-col items-center justify-center py-8 px-4;
  
  .loader-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
  }
  
  span {
    color: #6b7280;
    font-size: 14px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* حالة فارغة */
.categories-empty {
  @apply flex flex-col items-center justify-center py-8 px-4 text-gray-500;
  
  i {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.5;
  }
  
  span {
    font-size: 14px;
  }
}

/* شبكة التصنيفات */
.categories-grid {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
  
  @media (max-width: 640px) {
    grid-template-columns: 1fr;
    padding: 12px;
  }
}

/* عنصر التصنيف */
.category-item {
  position: relative;

  .category-main {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }
  
  .category-link {
    @apply flex items-center p-3 rounded-lg hover:bg-gray-50 transition-all duration-200;
    border: 1px solid transparent;
    text-decoration: none;
    flex: 1;
    min-width: 0;

    &:hover {
      border-color: #e5e7eb;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      text-decoration: none;
    }
  }

  /* زر عرض المنتجات */
  .show-products-btn {
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    background: #dbeafe;
    color: #3b82f6;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
    position: relative;
    z-index: 10;

    &:hover {
      background: #bfdbfe;
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    &:focus {
      outline: 2px solid #3b82f6;
      outline-offset: 2px;
    }

    i {
      font-size: 14px;
      pointer-events: none;
    }
  }
  
  .category-icon {
    @apply flex items-center justify-center w-12 h-12 rounded-lg mr-3;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    flex-shrink: 0;
    
    i {
      font-size: 20px;
    }
  }
  
  .category-info {
    flex: 1;
    min-width: 0;
    
    .category-name {
      @apply font-semibold text-gray-900 mb-1;
      font-size: 15px;
      line-height: 1.3;
      margin: 0;
    }
    
    .products-count {
      @apply text-sm text-gray-500;
    }
  }
  
  .sub-arrow {
    @apply text-gray-400 ml-2;
    font-size: 16px;
    flex-shrink: 0;
  }
}

/* قائمة المنتجات */
.category-products-list {
  position: absolute !important;
  top: 0;
  left: 100%;
  width: 350px;
  max-height: 400px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: var(--z-products-list) !important;
  margin-left: 8px;
  overflow: hidden;
  opacity: 1 !important;
  visibility: visible !important;

  &[style*="display: block"] {
    display: block !important;
  }

  .products-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #f3f4f6;
    background: #f9fafb;

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }

    .close-products-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border: none;
      border-radius: 6px;
      background: #f3f4f6;
      color: #6b7280;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #e5e7eb;
        color: #374151;
      }

      i {
        font-size: 12px;
      }
    }
  }

  .products-content {
    max-height: 280px;
    overflow-y: auto;
    padding: 16px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
    }

    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 2px;
    }
  }

  .products-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;

    i {
      font-size: 24px;
      color: #3b82f6;
      margin-bottom: 8px;

      &.animate-spin {
        animation: spin 1s linear infinite;
      }
    }

    span {
      color: #6b7280;
      font-size: 14px;
    }
  }

  .products-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;

    i {
      font-size: 32px;
      color: #9ca3af;
      margin-bottom: 8px;
    }

    span {
      color: #6b7280;
      font-size: 14px;
      text-align: center;
    }
  }

  .products-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .product-item {
    border: 1px solid #f3f4f6;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;

    &:hover {
      border-color: #e5e7eb;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .product-link {
      display: block;
      padding: 12px;
      text-decoration: none;
      color: inherit;

      &:hover {
        text-decoration: none;
      }
    }

    .product-info {
      .product-name {
        font-size: 14px;
        font-weight: 500;
        color: #1f2937;
        margin: 0 0 8px 0;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .product-price {
        display: flex;
        align-items: center;
        gap: 6px;

        .price,
        .sale-price {
          font-size: 14px;
          font-weight: 600;
          color: #059669;
        }

        .regular-price {
          font-size: 12px;
          color: #9ca3af;
          text-decoration: line-through;
        }
      }
    }
  }

  .products-footer {
    padding: 12px 16px;
    border-top: 1px solid #f3f4f6;
    background: #f9fafb;

    .view-all-products {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      width: 100%;
      padding: 8px 16px;
      background: #3b82f6;
      color: white;
      text-decoration: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      transition: background-color 0.2s ease;

      &:hover {
        background: #2563eb;
        text-decoration: none;
      }

      i {
        font-size: 12px;
      }
    }
  }
}

/* التصنيفات الفرعية */
.sub-categories {
  position: absolute;
  top: 0;
  left: 100%;
  width: 280px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateX(-10px);
  transition: all 0.2s ease;
  z-index: var(--z-submenu);
  
  .category-item:hover & {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
  }
  
  .sub-categories-header {
    @apply px-4 py-3 border-b border-gray-100 bg-gray-50;
    border-radius: 8px 8px 0 0;
    
    span {
      @apply font-semibold text-sm text-gray-700;
    }
  }
  
  .sub-categories-list {
    padding: 8px;
    max-height: 300px;
    overflow-y: auto;
  }
  
  .sub-category-link {
    @apply flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors duration-150;
    text-decoration: none;
    
    i {
      @apply w-6 h-6 flex items-center justify-center rounded mr-2 text-sm;
      background: #f3f4f6;
      color: #6b7280;
      flex-shrink: 0;
    }
    
    span {
      @apply text-sm text-gray-700;
      line-height: 1.4;
    }
    
    &:hover {
      text-decoration: none;
      
      i {
        background: #e5e7eb;
        color: #374151;
      }
    }
  }
  
  .sub-category-more {
    @apply text-blue-600 hover:text-blue-700 font-medium;
    
    i {
      background: #dbeafe !important;
      color: #3b82f6 !important;
    }
  }
}

/* الخلفية الشفافة */
.categories-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: var(--z-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  
  &.show {
    opacity: 1;
    visibility: visible;
  }
}

/* منع التمرير عند فتح القائمة على الموبايل */
body.categories-dropdown-open {
  @media (max-width: 768px) {
    overflow: hidden;
  }
}

/* تحسينات للموبايل */
@media (max-width: 640px) {
  .categories-dropdown-menu {
    position: fixed;
    top: auto;
    bottom: 0;
    left: 0;
    right: 0;
    min-width: auto;
    max-width: none;
    border-radius: 16px 16px 0 0;
    max-height: 80vh;
    transform: translateY(100%);
    
    &.show {
      transform: translateY(0);
    }
  }
  
  .sub-categories {
    position: static;
    width: auto;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    border: none;
    border-top: 1px solid #e5e7eb;
    border-radius: 0;
    margin-top: 8px;
  }

  .category-products-list {
    position: static;
    width: auto;
    max-height: none;
    margin: 8px 0 0 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/* تحسينات RTL */
[dir="rtl"] {
  .categories-trigger-btn {
    .categories-arrow {
      &.rotated {
        transform: rotate(-180deg);
      }
    }
  }
  
  .category-item {
    .category-icon {
      @apply ml-3 mr-0;
    }
    
    .sub-arrow {
      @apply mr-2 ml-0;
      transform: rotate(180deg);
    }
  }
  
  .sub-categories {
    left: auto;
    right: 100%;
    transform: translateX(10px);
    
    .category-item:hover & {
      transform: translateX(0);
    }
  }
  
  .sub-category-link {
    i {
      @apply ml-2 mr-0;
    }
  }
}
