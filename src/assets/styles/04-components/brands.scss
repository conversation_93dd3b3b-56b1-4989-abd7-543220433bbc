.brands-nav {
  @apply flex flex-wrap justify-center rtl:space-x-reverse space-x-2 space-y-2;

  &__item {
    @apply flex items-center justify-center rounded-md hover:shadow-sm focus:border-primary text-sm w-10 h-10 bg-white text-gray-500 p-1 transition;

    &.is-selected{
      @apply bg-primary text-primary-reverse #{!important};
    }

    span{
      @apply pointer-events-none;
    }
  }

  &.is-sticky{
    @apply flex-col flex-wrap h-full fixed z-0 top-9 rtl:right-2 ltr:left-2 space-y-0.5 pt-16 pb-10;

    .brands-nav__item{
      @apply bg-white;

      @media (max-width: 1280px) {
        @apply w-6 h-6;
      }
    }
  }
}

.brand-char {
  @apply rtl:ml-5 ltr:mr-5 rtl:md:ml-12 ltr:md:mr-12 bg-white rounded-md w-10 h-10 flex items-center transition justify-center hover:shadow-sm border border-transparent focus:border-primary text-sm;
}

.brand-item {
  @apply flex justify-center items-center bg-white transition hover:opacity-95 bg-cover h-20 sm:h-24 md:h-32 text-white text-center p-3 sm:p-4 md:p-8 rounded-md overflow-hidden relative;

  .index & {
    @apply p-4;

    img{
      @apply xs:max-w-[150px] w-auto;
    }
  }
}
