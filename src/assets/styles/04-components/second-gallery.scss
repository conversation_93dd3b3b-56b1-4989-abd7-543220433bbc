/* Second Gallery Component with Scale-In-Center Animation */

/* Scale-In-Center Animation Keyframes */
@-webkit-keyframes scale-in-center {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0);
        opacity: 1;
    }
    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes scale-in-center {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0);
        opacity: 1;
    }
    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1;
    }
}

/* Enhanced Scale-In-Center with <PERSON>unce */
@keyframes scale-in-center-bounce {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    60% {
        transform: scale(1.1);
        opacity: 0.9;
    }
    80% {
        transform: scale(0.95);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Main Gallery Section with Lazy Loading Animation */
.gaming-gallery-section {
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 255, 136, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 212, 255, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #0a0a0a 0%, #0d0d0d 50%, #1a1a1a 100%);
    position: relative;
    overflow: hidden;
    padding: 80px 0;
    width: 100%;
    min-height: auto;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    transition: opacity 1.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 1.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;

    &.animate-in {
        opacity: 1;
        transform: translateY(0) scale(1);
        animation: sectionBounceIn 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 200' width='200' height='200'%3E%3Ccircle cx='50' cy='50' r='2' fill='%2300ff88' opacity='0.6'/%3E%3Ccircle cx='150' cy='50' r='1.5' fill='%23ff0080' opacity='0.4'/%3E%3Ccircle cx='100' cy='100' r='1' fill='%2300d4ff' opacity='0.5'/%3E%3Ccircle cx='50' cy='150' r='1.5' fill='%2300ff88' opacity='0.3'/%3E%3Ccircle cx='150' cy='150' r='2' fill='%23ff0080' opacity='0.4'/%3E%3C/svg%3E");
        background-size: 200px 200px;
        opacity: 0.3;
        pointer-events: none;
        animation: backgroundFloat 20s ease-in-out infinite;
    }
}

/* Section Bouncing Animation */
@keyframes sectionBounceIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-5px) scale(1.02);
    }
    85% {
        transform: translateY(2px) scale(0.99);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Background Float Animation */
@keyframes backgroundFloat {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(-10px, -10px) rotate(1deg); }
    50% { transform: translate(10px, -5px) rotate(-1deg); }
    75% { transform: translate(-5px, 10px) rotate(0.5deg); }
}

/* Container Animation */
.gaming-gallery-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: none;
    margin: 0;
    padding: 0 20px;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Title Wrapper Animation */
.gaming-gallery-title-wrapper {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    padding: 0 20px;
    opacity: 0;
    transform: translateY(25px);
    transition: opacity 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Title with Scale-In-Center Animation */
.gaming-gallery-title {
    font-size: clamp(2rem, 5vw, 4rem);
    font-weight: 900;
    color: #ffffff;
    background: linear-gradient(45deg, #00ff88, #00d4ff, #ff0080);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-transform: uppercase;
    letter-spacing: clamp(1px, 0.5vw, 4px);
    margin: 0 0 30px 0;
    position: relative;
    display: inline-block;
    filter: drop-shadow(0 0 20px rgba(0, 255, 136, 0.5));
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    line-height: 1.2;
    opacity: 0;
    transform: scale(0);
    transition: opacity 0.3s ease;

    &.animate-in {
        animation: scale-in-center-bounce 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards,
                   gradientText 3s ease-in-out 1.5s infinite;
    }

    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, #00ff88, #00d4ff, #ff0080);
        background-size: 200% 200%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: gradientText 3s ease-in-out infinite reverse;
        opacity: 0.3;
        z-index: -1;
    }
}

/* Gradient Text Animation */
@keyframes gradientText {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Fallback for browsers that don't support background-clip */
@supports not (-webkit-background-clip: text) {
    .gaming-gallery-title {
        color: #00ff88 !important;
        -webkit-text-fill-color: #00ff88 !important;
        background: none !important;
    }
}

/* Title Underline Animation */
.gaming-title-underline {
    width: 120px;
    height: 6px;
    background: linear-gradient(90deg, transparent, #ff0080, #00ff88, #00d4ff, transparent);
    background-size: 200% 100%;
    margin: 0 auto;
    border-radius: 3px;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.6), 0 0 40px rgba(0, 255, 136, 0.3);
    opacity: 0;
    transform: scaleX(0);
    transition: opacity 1s ease, transform 1s ease;

    &.animate-in {
        opacity: 1;
        transform: scaleX(1);
        animation: underlineFlow 2s ease-in-out infinite;
    }
}

@keyframes underlineFlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Slider Animation */
.gaming-gallery-slider {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 50%, rgba(0, 0, 0, 0.1) 100%);
    backdrop-filter: blur(20px);
    border: 2px solid rgba(0, 255, 136, 0.3);
    padding: clamp(20px, 4vw, 50px);
    margin: 0 20px;
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 0 50px rgba(0, 255, 136, 0.15);
    width: calc(100% - 40px);
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    transition: opacity 2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0) scale(1);
        animation: sliderBounceIn 2.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
}

/* Slider Bouncing Animation */
@keyframes sliderBounceIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-5px) scale(1.02);
    }
    85% {
        transform: translateY(2px) scale(0.99);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Track Animation */
.gaming-gallery-track {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: clamp(20px, 3vw, 40px);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 2.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 2.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Gallery Items with Scale-In-Center Animation */
.gaming-gallery-item {
    position: relative;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
    transform-style: preserve-3d;
    opacity: 0;
    transform: scale(0);

    &.animate-in {
        animation: scale-in-center-bounce 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    /* Staggered animation delays */
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.1s; }
    &:nth-child(3) { animation-delay: 0.2s; }
    &:nth-child(4) { animation-delay: 0.3s; }
    &:nth-child(5) { animation-delay: 0.4s; }
    &:nth-child(6) { animation-delay: 0.5s; }
    &:nth-child(7) { animation-delay: 0.6s; }
    &:nth-child(8) { animation-delay: 0.7s; }
    &:nth-child(9) { animation-delay: 0.8s; }

    &:hover {
        transform: translateY(-15px) scale(1.02);
    }
}

/* Image Wrapper Styles */
.gaming-gallery-image-wrapper {
    position: relative;
    margin-bottom: 25px;
    border-radius: 20px;
    overflow: hidden;
    background: linear-gradient(145deg, #1a1a1a 0%, #0d0d0d 100%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(0, 255, 136, 0.2);

    &:hover {
        box-shadow: 0 8px 40px rgba(0, 0, 0, 0.5), 0 0 50px rgba(0, 255, 136, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border-color: rgba(0, 255, 136, 0.6);
        transform: scale(1.02);
    }
}

.gaming-gallery-image-container {
    position: relative;
    aspect-ratio: 16/10;
    overflow: hidden;
    border-radius: 15px;
}

.gaming-gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    filter: brightness(0.85) contrast(1.15) saturate(1.1);
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

.gaming-gallery-image-wrapper:hover .gaming-gallery-image {
    transform: scale(1.08);
    filter: brightness(1) contrast(1.3) saturate(1.3);
}

/* Item Title Styles */
.gaming-gallery-item-title {
    font-size: clamp(1rem, 2.5vw, 1.4rem);
    font-weight: 700;
    color: #ffffff;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    letter-spacing: clamp(0.5px, 0.2vw, 1px);
    text-transform: uppercase;
    position: relative;
    padding: 15px 0;
    line-height: 1.3;
}

.gaming-gallery-item:hover .gaming-gallery-item-title {
    background: linear-gradient(45deg, #00ff88, #00d4ff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: none;
    filter: drop-shadow(0 0 10px rgba(0, 255, 136, 0.5));
    transform: translateY(-2px);
}

/* Overlay Styles */
.gaming-gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 3;
    border-radius: 15px;
}

.gaming-gallery-item:hover .gaming-gallery-overlay {
    opacity: 1;
}

.gaming-overlay-content {
    text-align: center;
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gaming-gallery-item:hover .gaming-overlay-content {
    transform: translateY(0);
}

.gaming-gallery-link-icon {
    font-size: 2.5rem;
    background: linear-gradient(45deg, #00ff88, #00d4ff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 0 15px #00ff88);
    animation: iconFloat 3s ease-in-out infinite;
    display: block;
    margin-bottom: 10px;
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0) scale(1);
        filter: drop-shadow(0 0 15px #00ff88);
    }
    50% {
        transform: translateY(-5px) scale(1.1);
        filter: drop-shadow(0 0 25px #00ff88);
    }
}

.gaming-overlay-text {
    color: #ffffff;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
}

/* Navigation Styles */
.gaming-gallery-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 50px;
    gap: 30px;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 2.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 2.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
}

.gaming-gallery-prev-btn,
.gaming-gallery-next-btn {
    background: linear-gradient(145deg, rgba(0, 255, 136, 0.15) 0%, rgba(0, 255, 136, 0.1) 100%);
    border: 2px solid rgba(0, 255, 136, 0.4);
    color: #00ff88;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    backdrop-filter: blur(15px);
    font-size: 1.2rem;

    &:hover {
        background: #00ff88;
        color: #0a0a0a;
        border-color: #00ff88;
        box-shadow: 0 0 30px rgba(0, 255, 136, 0.4), 0 0 40px rgba(0, 255, 136, 0.4);
        transform: scale(1.15) translateY(-2px);
    }
}

.gaming-gallery-dots {
    display: flex;
    gap: 15px;
    align-items: center;
}

.gaming-gallery-dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 2px solid rgba(0, 255, 136, 0.4);
    background: transparent;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    position: relative;

    &.active,
    &:hover {
        border-color: #00ff88;
        box-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
        transform: scale(1.2);
        background: radial-gradient(circle, #00ff88 0%, #00d4ff 100%);
    }

    &.active {
        animation: dotPulse 2s ease-in-out infinite;
    }
}

@keyframes dotPulse {
    0%, 100% { box-shadow: 0 0 15px rgba(0, 255, 136, 0.6); }
    50% { box-shadow: 0 0 25px rgba(0, 255, 136, 0.8); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .gaming-gallery-container {
        padding: 0 15px;
    }

    .gaming-gallery-slider {
        margin: 0 15px;
        width: calc(100% - 30px);
    }

    .gaming-gallery-track {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .gaming-gallery-section {
        padding: 60px 0;
    }

    .gaming-gallery-container {
        padding: 0 10px;
    }

    .gaming-gallery-title-wrapper {
        margin-bottom: 40px;
        padding: 0 10px;
    }

    .gaming-gallery-slider {
        margin: 0 10px;
        width: calc(100% - 20px);
        border-radius: 15px;
    }

    .gaming-gallery-track {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }

    .gaming-gallery-nav {
        margin-top: 40px;
        gap: 20px;
    }

    .gaming-gallery-prev-btn,
    .gaming-gallery-next-btn {
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .gaming-gallery-section {
        padding: 40px 0;
    }

    .gaming-gallery-container {
        padding: 0 5px;
    }

    .gaming-gallery-title-wrapper {
        margin-bottom: 30px;
        padding: 0 5px;
    }

    .gaming-gallery-slider {
        margin: 0 5px;
        width: calc(100% - 10px);
        border-radius: 12px;
    }

    .gaming-gallery-track {
        grid-template-columns: 1fr;
    }

    .gaming-gallery-nav {
        margin-top: 30px;
        gap: 15px;
    }

    .gaming-gallery-prev-btn,
    .gaming-gallery-next-btn {
        width: 45px;
        height: 45px;
        font-size: 0.9rem;
    }

    .gaming-gallery-dots {
        gap: 10px;
    }

    .gaming-gallery-dot {
        width: 12px;
        height: 12px;
    }
}

@media (max-width: 320px) {
    .gaming-gallery-section {
        padding: 30px 0;
    }

    .gaming-gallery-container {
        padding: 0;
    }

    .gaming-gallery-slider {
        margin: 0;
        width: 100%;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }

    .gaming-gallery-track {
        gap: 15px;
    }
}

/* Accessibility - Disable animations for reduced motion */
@media (prefers-reduced-motion: reduce) {
    .gaming-gallery-section,
    .gaming-gallery-container,
    .gaming-gallery-title-wrapper,
    .gaming-gallery-title,
    .gaming-title-underline,
    .gaming-gallery-slider,
    .gaming-gallery-track,
    .gaming-gallery-item,
    .gaming-gallery-nav {
        animation: none !important;
        transition: opacity 0.3s ease !important;
        transform: none !important;
        opacity: 1 !important;
    }

    .gaming-gallery-image {
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }
}
