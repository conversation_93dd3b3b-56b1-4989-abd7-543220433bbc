// Dropdown toggler
.dropdown__trigger {
  @apply rounded-full overflow-hidden w-10 h-10 font-medium focus:ring-offset-transparent;

  &.filter{
    @apply rounded-none w-auto h-auto overflow-visible;
  }
}

.dropdown__menu {
  @apply origin-top-right duration-200 transition-all scale-y-90 absolute opacity-0 -translate-y-4 invisible rtl:left-0 ltr:right-0 z-30 w-80 lg:w-60 rounded-t-md lg:rounded-t-none rounded-b-md shadow-default bg-white top-full lg:border-t lg:border-gray-300/30;
  outline: none;
}

.dropdown-toggler {
  @apply inline-flex items-center lg:h-full w-10 mx-0 text-gray-500;

  &.cat-filter{
    @apply static w-auto;
    
    .dropdown__trigger {
      @apply rounded-none w-auto h-auto overflow-visible;
    }
  }

  &:before {
    content: "";
    background: rgba(113, 113, 122, 0.75);
    @apply fixed w-screen h-screen left-0 top-0 opacity-0 pointer-events-none invisible duration-300 z-10;
  }

  &.is-opened {
    .dropdown__menu {
      @apply opacity-100 visible translate-y-0 scale-100;
    }
  }

  @media (max-width: 1024px) {
    .dropdown__menu {
      left: 0 !important;
      @apply fixed bottom-0 top-auto w-full opacity-0 translate-y-10 origin-center duration-300 rounded-b-none;

      .menu-item{
        @apply rtl:pl-2.5 ltr:pr-2.5;
      }
    }

    &.is-opened {
      .dropdown__menu {
        @apply opacity-100 translate-y-0;
      }

      &:before {
        @apply opacity-100 visible pointer-events-auto;
      }
    }
  }
}

/* User Menu Sidebar - حل أنيق لمشكلة القائمة المحصورة */
.user-menu-sidebar {
  position: fixed !important;
  top: 0 !important;
  right: -400px !important; /* مخفي في البداية */
  width: 400px !important;
  height: 100vh !important;
  background: #1a1a2e !important;
  border-left: 2px solid #00d4ff !important;
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.5) !important;
  z-index: 9999 !important;
  transition: right 0.3s ease !important;
  overflow-y: auto !important;

  &.open {
    right: 0 !important;
  }

  /* Header */
  .sidebar-header {
    padding: 20px !important;
    border-bottom: 1px solid #00d4ff !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;

    .user-info {
      display: flex !important;
      align-items: center !important;
      gap: 12px !important;

      .user-details {
        h3 {
          color: white !important;
          font-size: 16px !important;
          margin: 0 !important;
        }

        p {
          color: #a0a9c0 !important;
          font-size: 14px !important;
          margin: 0 !important;
        }
      }
    }

    .close-btn {
      background: none !important;
      border: none !important;
      color: white !important;
      font-size: 24px !important;
      cursor: pointer !important;
      padding: 8px !important;

      &:hover {
        color: #00d4ff !important;
      }
    }
  }

  /* Menu Items */
  .sidebar-menu {
    padding: 20px 0 !important;

    .menu-item {
      display: flex !important;
      align-items: center !important;
      padding: 15px 20px !important;
      color: white !important;
      text-decoration: none !important;
      transition: all 0.3s ease !important;

      &:hover {
        background: rgba(0, 212, 255, 0.1) !important;
        color: #00d4ff !important;
      }

      .menu-icon {
        font-size: 20px !important;
        margin-right: 15px !important;
      }

      .menu-text {
        font-size: 16px !important;
      }

      &.logout {
        color: #ff6b6b !important;
        border-top: 1px solid #ff6b6b !important;
        margin-top: 20px !important;

        &:hover {
          background: rgba(255, 107, 107, 0.1) !important;
        }
      }
    }
  }

  /* Tablet Responsive */
  @media (max-width: 1024px) {
    width: 350px;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    width: 100vw;
    right: -100vw;

    [dir="rtl"] & {
      left: -100vw;
    }

    &.open {
      right: 0;

      [dir="rtl"] & {
        left: 0;
      }
    }

    .sidebar-header {
      padding: 15px;

      .user-info {
        .user-avatar {
          width: 45px;
          height: 45px;
        }

        .user-details h3 {
          font-size: 15px;
        }

        .user-details p {
          font-size: 13px;
        }
      }
    }

    .sidebar-menu .menu-item {
      padding: 12px 15px;

      .menu-icon {
        font-size: 18px;
        margin-right: 12px;

        [dir="rtl"] & {
          margin-right: 0;
          margin-left: 12px;
        }
      }

      .menu-text {
        font-size: 15px;
      }
    }
  }

  /* Small Mobile */
  @media (max-width: 480px) {
    .sidebar-header {
      padding: 12px;
    }

    .sidebar-menu .menu-item {
      padding: 10px 12px;
    }
  }
}

/* Backdrop */
.user-menu-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.5) !important;
  z-index: 9998 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: all 0.3s ease !important;

  &.open {
    opacity: 1 !important;
    visibility: visible !important;
  }
}

/* إخفاء القائمة المنسدلة الأصلية واستبدالها بالسايدبار */
salla-user-menu {
  .s-user-menu-dropdown,
  .dropdown__menu {
    display: none !important;
  }
}