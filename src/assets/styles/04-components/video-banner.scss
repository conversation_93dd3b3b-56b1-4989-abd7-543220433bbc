/* Video Banner Component Styles - LCP Optimized */

/* Critical CSS for LCP Optimization */
.video-banner-section[data-lcp-optimized="true"] {
    /* Ensure immediate visibility for LCP element */
    .video-title {
        opacity: 1 !important;
        transform: translateY(0) !important;
        visibility: visible !important;
        /* Prevent layout shift */
        min-height: 1.2em;
        /* Optimize font rendering */
        text-rendering: optimizeSpeed;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* Preload critical content */
    .video-text-content {
        contain: layout style;
    }
}
.video-banner-section {
    position: relative;
    width: 100%;
    margin-top: 0 !important; /* Remove inherited top margin from s-block */
    margin-bottom: 2rem;
    /* LCP Optimization: Start visible to avoid layout shift */
    opacity: 1;
    transform: translateY(0);
    transition: transform 0.6s ease;
    will-change: transform;

    &.animate-in {
        transform: translateY(0);
        animation: sectionSlideInUp 0.8s ease-out forwards;
    }
}

/* Section Slide In Up Animation - No Bounce */
@keyframes sectionSlideInUp {
    0% {
        transform: translateY(30px);
        opacity: 0.8;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.video-banner-container {
    position: relative;
    width: 100%;
    height: 70vh;
    min-height: 500px;
    max-height: 800px;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    contain: layout style;
}

/* Media Background */
.media-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background: #000;
}

.iframe-container,
.image-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Banner Images (GIF and Static) - Performance Optimized */
.banner-image {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    min-width: 100%;
    min-height: 100%;
    transform: translate3d(-50%, -50%, 0);
    object-fit: cover;
    transition: transform 0.3s ease, opacity 0.5s ease;
    opacity: 1;
    will-change: transform, opacity;
    contain: layout style paint;

    &.gif-image {
        image-rendering: auto;
        image-rendering: crisp-edges;
        image-rendering: -webkit-optimize-contrast;
    }

    &.static-image {
        image-rendering: auto;
    }

    // Hide image while loading
    &.loading {
        opacity: 0;
    }

    // Clean up will-change after load
    &.loaded {
        will-change: auto;
    }
}

// Show image when loaded
.video-banner-section.image-loaded .banner-image,
.video-banner-section.gif-loaded .banner-image {
    opacity: 1;
}

.video-banner-container:hover .banner-image {
    transform: translate(-50%, -50%) scale(1.05);
}

/* Video Elements */
.video-element,
.video-iframe {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    min-width: 100%;
    min-height: 100%;
    transform: translate(-50%, -50%);
    object-fit: cover;
    border: none;
    outline: none;
}

/* Loading Indicators */
.video-loading,
.image-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 5;
    color: #fff;
    transition: opacity 0.3s ease;

    p {
        font-size: 1rem;
        margin: 0;
        opacity: 0.9;
    }
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--color-primary, #1DE9B6);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* Fallback States */
.iframe-fallback,
.image-fallback {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 6;
}

.fallback-content {
    text-align: center;
    color: #fff;
    padding: 2rem;

    h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: #fff;
    }

    p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
        opacity: 0.8;
    }
}

.fallback-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
    color: var(--color-primary, #1DE9B6);
}

.fallback-link {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: var(--color-primary, #1DE9B6);
    color: #fff;
    text-decoration: none;
    border-radius: 6px;
    transition: background-color 0.3s ease;

    &:hover {
        background: var(--color-primary-dark, #17c1a3);
        color: #fff;
    }
}

/* Video Overlay */
.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.4) 0%,
        rgba(0, 0, 0, 0.2) 50%,
        rgba(0, 0, 0, 0.6) 100%
    );
    z-index: 2;
}

/* Content */
.video-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
    padding: 2rem;
}

.video-text-content {
    text-align: center;
    color: #fff;
    max-width: 800px;
}

/* Text Animation Elements - LCP Optimized */
.animate-element {
    /* LCP Optimization: Start visible for immediate rendering */
    opacity: 1;
    transform: translateY(0);
    transition: transform 0.6s ease;
    will-change: transform;

    &.animate-in {
        animation: slideInUp 0.8s ease-out forwards;
    }
}

/* Slide In Up Animation - No Bounce */
@keyframes slideInUp {
    0% {
        transform: translateY(20px);
        opacity: 0.9;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Title Styles - LCP Optimized for Immediate Rendering */
.video-title {
    font-family: var(--font-main), 'DINNextLTArabic', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.8);
    line-height: 1.2;
    position: relative;
    display: inline-block;
    /* LCP Critical: Ensure immediate visibility */
    opacity: 1;
    transform: translateY(0);
    /* Use font-display: swap for better font loading */
    font-display: swap;

    &.animate-in {
        animation: titleSlideInUp 0.8s ease-out forwards;
    }

    &::after {
        content: '';
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 4px;
        background: var(--color-primary, #1DE9B6);
        box-shadow: 0 0 15px var(--color-primary, #1DE9B6);
        transition: width 0.8s ease 0.3s;
    }

    &.animate-in::after {
        width: 80px;
        animation: underlineSlideIn 0.8s ease-out 0.4s forwards;
    }
}

/* Title Slide In Up Animation - No Bounce */
@keyframes titleSlideInUp {
    0% {
        transform: translateY(20px);
        opacity: 0.9;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Underline Slide In Animation - No Bounce */
@keyframes underlineSlideIn {
    0% {
        width: 0;
        transform: translateX(-50%);
    }
    100% {
        width: 80px;
        transform: translateX(-50%);
    }
}

/* Subtitle Styles */
.video-subtitle {
    font-size: 1.25rem;
    font-weight: 300;
    line-height: 1.6;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);
    opacity: 0.95;
    max-width: 600px;
    margin: 0 auto 2rem auto;
}

/* Button Styles - Slide In Up */
.video-button-container {
    margin-top: 2rem;

    &.animate-in {
        animation: buttonContainerSlideInUp 0.8s ease-out 0.6s forwards;
    }

    &.animate-in .video-banner-button {
        animation: buttonSlideInUp 0.8s ease-out 0.8s forwards,
                   buttonPulse 3s ease-in-out 2s infinite;
    }
}

/* Button Container Slide In Up Animation */
@keyframes buttonContainerSlideInUp {
    0% {
        transform: translateY(20px);
        opacity: 0.9;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Button Slide In Up Animation */
@keyframes buttonSlideInUp {
    0% {
        transform: translateY(15px);
        opacity: 0.9;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.video-banner-button {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: var(--color-primary, #1DE9B6);
    color: #fff;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(29, 233, 182, 0.3);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    &:hover {
        background: var(--color-primary-dark, #17c1a3);
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(29, 233, 182, 0.4);

        &::before {
            left: 100%;
        }

        .button-icon {
            transform: translateX(5px);
        }
    }
}

.button-text {
    position: relative;
    z-index: 1;
}

.button-icon {
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
    font-size: 1rem;
}

@keyframes buttonPulse {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(29, 233, 182, 0.3);
    }
    50% {
        box-shadow: 0 4px 20px rgba(29, 233, 182, 0.5);
    }
}

/* Placeholder Styles */
.video-banner-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.placeholder-content {
    text-align: center;
    color: #6c757d;

    h3 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        color: #495057;
    }

    p {
        font-size: 1rem;
        opacity: 0.8;
    }
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Media States */
.video-banner-section.video-loaded .video-loading,
.video-banner-section.image-loaded .image-loading,
.video-banner-section.gif-loaded .image-loading {
    display: none;
}

.video-banner-section.video-error .video-loading,
.video-banner-section.video-error .image-loading {
    background: rgba(0, 0, 0, 0.9);
}

/* Hide video loading for image/gif content */
.video-banner-section .image-container ~ .video-loading {
    display: none;
}

/* RTL Support */
[dir="rtl"] .video-banner-button:hover .button-icon {
    transform: translateX(-5px);
}

/* Performance optimization for large GIFs */
@media (max-width: 768px) {
    .gif-image {
        image-rendering: -webkit-optimize-contrast;
    }

    .video-banner-container {
        height: 60vh;
        min-height: 400px;
        border-radius: 8px;
    }

    .video-content {
        padding: 1rem;
    }

    .video-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;

        &::after {
            width: 60px;
            height: 3px;
        }
    }

    .video-subtitle {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }

    .video-banner-button {
        padding: 0.875rem 1.75rem;
        font-size: 1rem;
        gap: 0.5rem;
    }

    .video-button-container {
        margin-top: 1.5rem;
    }
}

@media (max-width: 480px) {
    .video-banner-container {
        height: 50vh;
        min-height: 350px;
    }

    .video-title {
        font-size: 2rem;
    }

    .video-subtitle {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .video-banner-button {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
        gap: 0.4rem;
    }

    .video-button-container {
        margin-top: 1rem;
    }
}

/* Accessibility - Disable animations for reduced motion */
@media (prefers-reduced-motion: reduce) {
    .video-banner-section,
    .animate-element,
    .video-title,
    .video-title::after,
    .video-banner-button,
    .video-button-container {
        transition: opacity 0.3s ease !important;
        animation: none !important;
    }

    .video-banner-section {
        opacity: 1;
        transform: none;
    }

    .animate-element,
    .video-title,
    .video-button-container {
        opacity: 1;
        transform: none;
    }

    .video-banner-button::before {
        display: none;
    }

    .video-banner-button:hover {
        transform: none;
    }

    /* Immediate visibility for reduced motion users */
    .video-banner-section.animate-in,
    .animate-element.animate-in,
    .video-title.animate-in,
    .video-button-container.animate-in {
        opacity: 1;
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .video-overlay {
        background: rgba(0, 0, 0, 0.8);
    }

    .video-title,
    .video-subtitle {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
    }
}
