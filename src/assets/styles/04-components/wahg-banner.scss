/* WahgBanner Component - Modern Banner with Light Sweep Effect */

// Variables
:root {
    --wahg-banner-transition: all 0.3s ease;
    --wahg-banner-gaming-blue: #00d4ff;
    --wahg-banner-gaming-red: #ff0080;
    --wahg-banner-gaming-purple: #8b5cf6;
    --wahg-banner-border-radius: 12px;
    --wahg-banner-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --wahg-banner-hover-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

// Main Banner Section with Optimized Fast Animation
.s-block--wahg-banner {
    margin: 40px auto;
    padding: 0 20px;
    opacity: 0;
    transform: translate3d(0, 20px, 0) scale(0.95);
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;
    contain: layout style paint;

    &.animate-in {
        opacity: 1;
        transform: translate3d(0, 0, 0) scale(1);
    }

    .s-block__title {
        margin-bottom: 20px;
        opacity: 0;
        transform: translate3d(0, 15px, 0);
        transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        will-change: opacity, transform;

        h2 {
            color: var(--color-primary, #1DE9B6);
            font-size: 28px;
            font-weight: bold;
            text-align: center;
            margin: 0;
        }

        &.animate-in {
            opacity: 1;
            transform: translate3d(0, 0, 0);
        }
    }
}

// Banner Wrapper with Smooth Fast Animation - No Bounce
.wahg-banner-wrapper {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    opacity: 0;
    transform: translate3d(0, 15px, 0) scale(0.97);
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;
    contain: layout style paint;

    &.animate-in {
        opacity: 1;
        transform: translate3d(0, 0, 0) scale(1);
    }
}

// Main Banner Container
.wahg-banner {
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: var(--wahg-banner-border-radius);
    box-shadow: var(--wahg-banner-shadow);
    transition: var(--wahg-banner-transition);
    cursor: pointer;
    display: block;
    text-decoration: none;
    border: 2px solid transparent;

    &:hover {
        transform: translate3d(0, -2px, 0);
        box-shadow:
            var(--wahg-banner-hover-shadow),
            0 0 20px rgba(0, 212, 255, 0.3),
            0 0 40px rgba(255, 0, 128, 0.2);
        border: 2px solid rgba(0, 212, 255, 0.5);
        will-change: transform;
    }

    // No link variant
    &--no-link {
        cursor: default;
    }

    // Placeholder variant
    &--placeholder {
        cursor: default;
        
        &:hover {
            transform: none;
            box-shadow: var(--wahg-banner-shadow);
        }
    }
}

// Banner Image
.wahg-banner__image {
    width: 100%;
    height: auto;
    min-height: 200px;
    object-fit: cover;
    display: block;
    transition: var(--wahg-banner-transition);
}

// Gaming Light Sweep Effect
.wahg-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(0, 212, 255, 0.4),
        rgba(255, 0, 128, 0.4),
        rgba(139, 92, 246, 0.4),
        transparent
    );
    transition: left 0.8s ease;
    z-index: 1;
    pointer-events: none;
}

.wahg-banner:hover::before {
    left: 100%;
}

// Additional gaming glow effect
.wahg-banner::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        rgba(0, 212, 255, 0.1) 0%,
        transparent 30%,
        transparent 70%,
        rgba(255, 0, 128, 0.1) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
    pointer-events: none;
}

.wahg-banner:hover::after {
    opacity: 1;
}

// Placeholder styles
.wahg-banner__placeholder {
    width: 100%;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

// Responsive Design with Optimized Mobile Animations
@media (max-width: 768px) {
    .s-block--wahg-banner {
        margin: 20px auto;
        padding: 0 15px;

        // Faster animations on mobile for better performance
        transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        // No bounce animation needed - smooth transition only

        .s-block__title {
            transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
    }

    .wahg-banner-wrapper {
        transition: opacity 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        // No bounce animation needed - smooth transition only
    }

    .wahg-banner {
        border-radius: 8px;

        &:hover {
            transform: none;
        }
    }

    .wahg-banner__image {
        min-height: 150px;
    }

    .wahg-banner__placeholder {
        height: 150px;
    }
}

@media (max-width: 480px) {
    .s-block--wahg-banner {
        margin: 15px auto;
        padding: 0 10px;

        // Even faster animations on small mobile devices
        transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        // No bounce animation needed - smooth transition only

        .s-block__title {
            h2 {
                font-size: 24px;
            }

            transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
    }

    .wahg-banner-wrapper {
        transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        // No bounce animation needed - smooth transition only
    }

    .wahg-banner__image {
        min-height: 120px;
    }

    .wahg-banner__placeholder {
        height: 120px;
    }
}

// Loading State
.wahg-banner__image[loading] {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: wahg-banner-loading 1.5s infinite;
}

@keyframes wahg-banner-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

// Accessibility
.wahg-banner:focus {
    outline: 2px solid var(--color-primary, #1DE9B6);
    outline-offset: 2px;
}

.wahg-banner:focus:not(:focus-visible) {
    outline: none;
}

// Reduced motion support for accessibility
@media (prefers-reduced-motion: reduce) {
    .s-block--wahg-banner,
    .wahg-banner-wrapper,
    .s-block__title {
        transition: opacity 0.2s ease !important;
        animation: none !important;
        transform: none !important;
    }

    .s-block--wahg-banner.animate-in,
    .wahg-banner-wrapper.animate-in,
    .s-block__title.animate-in {
        opacity: 1 !important;
        transform: none !important;
    }

    // No keyframes needed for reduced motion - just simple opacity transition
}

// High contrast mode support
@media (prefers-contrast: high) {
    .wahg-banner {
        border: 2px solid currentColor;
    }

    .wahg-banner::before {
        background: linear-gradient(
            90deg,
            transparent,
            rgba(0, 212, 255, 0.9),
            rgba(255, 0, 128, 0.9),
            transparent
        );
    }

    .wahg-banner:hover {
        border: 2px solid var(--wahg-banner-gaming-blue);
    }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
    .s-block--wahg-banner,
    .s-block--wahg-banner .s-block__title,
    .wahg-banner-wrapper,
    .wahg-banner,
    .wahg-banner__image,
    .wahg-banner::before {
        transition: opacity 0.3s ease !important;
        animation: none !important;
        transform: none !important;
    }

    .s-block--wahg-banner,
    .s-block--wahg-banner .s-block__title,
    .wahg-banner-wrapper {
        opacity: 1;
    }

    .wahg-banner:hover {
        transform: none;
    }

    .wahg-banner:hover::before {
        left: -100%;
    }

    .wahg-banner__image[loading] {
        animation: none;
    }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
    .wahg-banner {
        --wahg-banner-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        --wahg-banner-hover-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);

        &:hover {
            box-shadow:
                var(--wahg-banner-hover-shadow),
                0 0 25px rgba(0, 212, 255, 0.4),
                0 0 50px rgba(255, 0, 128, 0.3);
        }
    }

    .wahg-banner::before {
        background: linear-gradient(
            90deg,
            transparent,
            rgba(0, 212, 255, 0.5),
            rgba(255, 0, 128, 0.5),
            rgba(139, 92, 246, 0.5),
            transparent
        );
    }
}
