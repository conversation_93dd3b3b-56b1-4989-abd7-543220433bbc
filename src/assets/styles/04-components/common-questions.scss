/* Common Questions (FAQ) Component - Optimized Performance */
.whatsapp-faq-section {
    background: rgba(10, 10, 15, 0.05);
    border-radius: 20px;
    padding: 2rem 1rem;
    margin: 2rem auto;
    position: relative;
    contain: layout style;
    will-change: auto;
    max-width: 1200px;
    width: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;

    @media (max-width: 640px) {
        padding: 1.5rem 0.5rem;
        margin: 1rem auto;
        border-radius: 15px;
        width: 95%;
    }

    @media (min-width: 1024px) {
        width: 80%;
    }

    @media (min-width: 1280px) {
        width: 70%;
    }
}

.whatsapp-faq-header {
    position: relative;
    z-index: 2;
    margin-bottom: 2rem;
    text-align: center;

    @media (min-width: 640px) {
        margin-bottom: 3rem;
    }
}

.whatsapp-title {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    font-weight: 700;
    letter-spacing: 0.5px;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #ffffff !important;
    text-align: center;

    @media (min-width: 640px) {
        font-size: 1.875rem;
    }

    @media (min-width: 1024px) {
        font-size: 2.25rem;
    }
}

.whatsapp-description {
    color: #128C7E;
    font-weight: 500;
    text-shadow: 0 0 5px rgba(18, 140, 126, 0.2);
    font-size: 1rem;
    max-width: 32rem;
    margin: 0 auto;
    text-align: center;

    @media (min-width: 640px) {
        font-size: 1.125rem;
    }
}

/* FAQ Accordion Container */
.faq-accordion {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

/* WhatsApp Chat Bubble Style FAQ Items */
.whatsapp-faq-item {
    background: linear-gradient(135deg, rgba(37, 211, 102, 0.1), rgba(18, 140, 126, 0.05));
    border: 2px solid rgba(37, 211, 102, 0.2);
    border-radius: 18px;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.1);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    will-change: transform, box-shadow;
    backface-visibility: hidden;
    width: 100%;
    max-width: 800px;

    // Staggered animation delays
    @for $i from 1 through 10 {
        &:nth-child(#{$i}) {
            animation-delay: #{$i * 0.1}s;
        }
    }

    &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.2),
                    0 0 20px rgba(37, 211, 102, 0.1);
        border-color: rgba(37, 211, 102, 0.4);
    }

    // Gaming-style glow effect
    &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #25D366, #128C7E, #25D366);
        border-radius: 20px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    &:hover::before {
        opacity: 0.3;
        animation: whatsapp-glow 2s infinite alternate;
    }

    @media (max-width: 640px) {
        border-radius: 12px;

        &::before {
            border-radius: 14px;
        }
    }
}

/* FAQ Question Button */
.whatsapp-faq-question {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px 16px 0 0;
    position: relative;
    z-index: 2;
    width: 100%;
    text-align: left;
    padding: 1rem 1.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:hover {
        background: rgba(37, 211, 102, 0.05);
    }

    &:focus {
        box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.3);
        outline: none;
    }

    &:focus-visible {
        outline: 2px solid #25D366;
        outline-offset: 2px;
        box-shadow: 0 0 0 4px rgba(37, 211, 102, 0.2);
    }

    @media (min-width: 640px) {
        padding: 1.5rem;
    }

    @media (max-width: 640px) {
        padding: 1rem !important;
        border-radius: 12px 12px 0 0;
    }
}

.whatsapp-question-text {
    font-weight: 600;
    text-shadow: 0 0 5px rgba(37, 211, 102, 0.2);
    padding-right: 1rem;
    font-size: 1rem;

    @media (min-width: 640px) {
        font-size: 1.125rem;
    }
}

.whatsapp-faq-icon {
    color: #25D366;
    filter: drop-shadow(0 0 3px rgba(37, 211, 102, 0.3));
    transition: all 0.3s ease;
    flex-shrink: 0;
    font-size: 1.25rem;

    @media (min-width: 640px) {
        font-size: 1.5rem;
    }

    i {
        transition: transform 0.3s ease;
        will-change: transform;

        &.rotate-180 {
            transform: rotate(180deg);
        }
    }
}

/* FAQ Answer */
.whatsapp-faq-answer {
    background: rgba(18, 140, 126, 0.05);
    border-top: 1px solid rgba(37, 211, 102, 0.2);
    border-radius: 0 0 16px 16px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.show {
        max-height: 500px; // Fallback for browsers without scrollHeight
    }

    > div {
        padding: 1rem 1.5rem;

        @media (min-width: 640px) {
            padding: 1.5rem;
        }

        @media (max-width: 640px) {
            padding: 1rem !important;
        }
    }
}

.whatsapp-answer-text {
    color: #128C7E;
    font-weight: 500;
    line-height: 1.6;
    font-size: 0.875rem;

    @media (min-width: 640px) {
        font-size: 1rem;
    }
}

/* Empty State */
.whatsapp-no-content {
    background: rgba(37, 211, 102, 0.05);
    border: 2px dashed rgba(37, 211, 102, 0.3);
    border-radius: 18px;
    padding: 3rem 2rem;
    text-align: center;
    width: 100%;
    max-width: 800px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    @media (max-width: 640px) {
        padding: 2rem 1rem;
        border-radius: 12px;
    }
}

.whatsapp-empty-icon {
    color: #25D366;
    filter: drop-shadow(0 0 5px rgba(37, 211, 102, 0.3));
    margin-bottom: 1rem;

    i {
        font-size: 2.5rem;
    }
}

.whatsapp-empty-text {
    color: #128C7E;
    font-weight: 500;
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes whatsapp-glow {
    0% {
        filter: blur(5px);
    }
    100% {
        filter: blur(10px);
    }
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
    animation: slideInUp 0.3s ease-out;
}

/* RTL Support */
[dir="rtl"] {
    .whatsapp-faq-question {
        text-align: right;
    }

    .whatsapp-question-text {
        padding-left: 1rem;
        padding-right: 0;
    }
}

/* Accessibility and Performance */
@media (prefers-reduced-motion: reduce) {
    .whatsapp-faq-item,
    .whatsapp-faq-answer,
    .whatsapp-faq-icon i,
    .whatsapp-faq-item::before {
        animation: none !important;
        transition: none !important;
    }

    .whatsapp-faq-item:hover {
        transform: none !important;
    }

    .whatsapp-faq-icon i.rotate-180 {
        transform: none !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .whatsapp-faq-section {
        background: rgba(255, 255, 255, 0.05);
    }

    .whatsapp-faq-question {
        background: rgba(0, 0, 0, 0.8);
        color: #fff;

        &:hover {
            background: rgba(37, 211, 102, 0.1);
        }
    }

    .whatsapp-faq-answer {
        background: rgba(0, 0, 0, 0.6);
    }

    .whatsapp-answer-text {
        color: #a0d4cc;
    }

    .whatsapp-description {
        color: #a0d4cc;
    }
}
