/**
 * Enhanced First Gallery Component Styles
 * Comprehensive customizable gallery with grid, masonry, and carousel layouts
 */

/* Base gallery component */
.first-gallery-enhanced {
    position: relative;
    contain: layout style paint;
    isolation: isolate;
    
    /* Performance optimizations */
    will-change: auto;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Enhanced gallery container */
.enhanced-gallery-container {
    position: relative;
    width: 100%;
    overflow: hidden;
}

/* Gallery grid layouts */
.gallery-grid {
    display: grid;
    gap: var(--image-spacing, 15px);
    width: 100%;
    grid-template-columns: repeat(var(--columns-count, 4), 1fr);
    will-change: transform;
    transform: translateZ(0);
}

/* Grid layout */
.layout-grid .gallery-grid {
    display: grid;
    align-items: start;
}

/* Masonry layout */
.layout-masonry .gallery-grid {
    display: grid;
    grid-template-rows: masonry;
    align-items: start;
}

/* Fallback for browsers without masonry support */
@supports not (grid-template-rows: masonry) {
    .layout-masonry .gallery-grid {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
    }
    
    .layout-masonry .gallery-item {
        flex: 0 0 calc((100% - (var(--columns-count, 4) - 1) * var(--image-spacing, 15px)) / var(--columns-count, 4));
        margin-bottom: var(--image-spacing, 15px);
    }
}

/* Carousel layout */
.layout-carousel .gallery-grid {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    scrollbar-width: none;
    -ms-overflow-style: none;
    gap: var(--image-spacing, 15px);
    padding-bottom: 10px;
}

.layout-carousel .gallery-grid::-webkit-scrollbar {
    display: none;
}

.layout-carousel .gallery-item {
    flex: 0 0 auto;
    scroll-snap-align: start;
    width: calc((100% - (var(--columns-count, 4) - 1) * var(--image-spacing, 15px)) / var(--columns-count, 4));
}

/* Gallery item */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--image-border-radius, 8px);
    transition: all 0.3s ease;
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Image container */
.gallery-image-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: var(--image-border-radius, 8px);
    background: #f8f9fa;
}

/* Enhanced gallery image */
.enhanced-gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    border-radius: var(--image-border-radius, 8px);
}

/* Aspect ratio settings */
.aspect-ratio-square .gallery-image-container {
    aspect-ratio: 1 / 1;
}

.aspect-ratio-landscape .gallery-image-container {
    aspect-ratio: 16 / 9;
}

.aspect-ratio-portrait .gallery-image-container {
    aspect-ratio: 9 / 16;
}

.aspect-ratio-auto .gallery-image-container {
    aspect-ratio: auto;
    height: auto;
}

/* Image shadows */
[data-enable-image-shadows="true"] .gallery-image-container {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

[data-enable-image-shadows="false"] .gallery-image-container {
    box-shadow: none;
}

/* Hover effects */
.hover-effects-enabled .gallery-item:hover .enhanced-gallery-image {
    transform: scale(1.05);
}

/* Hover overlay */
.hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--hover-overlay-color, #000000);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    z-index: 1;
    pointer-events: none;
    will-change: opacity;
    transform: translateZ(0);
}

.hover-effects-enabled .gallery-item:hover .hover-overlay {
    opacity: var(--hover-overlay-opacity, 0.3);
}

/* Gallery link */
.gallery-link {
    display: block;
    text-decoration: none;
    color: inherit;
    border-radius: var(--image-border-radius, 8px);
}

.gallery-link:focus {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* Gallery placeholder */
.gallery-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f3f4f6;
    color: #6b7280;
    font-size: 24px;
    min-height: 150px;
    border-radius: var(--image-border-radius, 8px);
}

/* Animation classes */
.animations-enabled .gallery-item {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.animations-enabled .gallery-item.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Animation types */
.animation-fadeIn .gallery-item {
    opacity: 0;
    transition: opacity 0.6s ease;
}

.animation-fadeIn .gallery-item.animate-in {
    opacity: 1;
}

.animation-slideUp .gallery-item {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animation-slideUp .gallery-item.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.animation-scaleIn .gallery-item {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s ease;
}

.animation-scaleIn .gallery-item.animate-in {
    opacity: 1;
    transform: scale(1);
}

.animation-bounceIn .gallery-item {
    opacity: 0;
    transform: scale(0.3);
    transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animation-bounceIn .gallery-item.animate-in {
    opacity: 1;
    transform: scale(1);
}

/* Animation delays */
.delay-none .gallery-item {
    transition-delay: 0ms;
}

.delay-short .gallery-item {
    transition-delay: 100ms;
}

.delay-medium .gallery-item {
    transition-delay: 200ms;
}

.delay-long .gallery-item {
    transition-delay: 300ms;
}

/* Stagger animations */
.stagger-enabled .gallery-item {
    transition-delay: var(--animation-delay, 0ms);
}

/* Responsive settings */
@media (max-width: 768px) {
    .gallery-grid {
        grid-template-columns: repeat(var(--mobile-columns, 2), 1fr);
    }
    
    .layout-carousel .gallery-item {
        width: calc((100% - (var(--mobile-columns, 2) - 1) * var(--image-spacing, 15px)) / var(--mobile-columns, 2));
    }
    
    /* Hide on mobile when enabled */
    .hide-mobile {
        display: none !important;
    }
    
    /* Reduce spacing on mobile */
    .gallery-grid {
        gap: calc(var(--image-spacing, 15px) * 0.75);
    }
    
    /* Reduce border radius on mobile */
    .gallery-item,
    .gallery-image-container,
    .enhanced-gallery-image,
    .gallery-link {
        border-radius: calc(var(--image-border-radius, 8px) * 0.75);
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .gallery-grid {
        grid-template-columns: repeat(var(--tablet-columns, 3), 1fr);
    }
    
    .layout-carousel .gallery-item {
        width: calc((100% - (var(--tablet-columns, 3) - 1) * var(--image-spacing, 15px)) / var(--tablet-columns, 3));
    }
}

/* RTL support */
[dir="rtl"] .layout-carousel .gallery-grid {
    direction: rtl;
}

[dir="rtl"] .hover-overlay {
    direction: ltr;
}

/* Accessibility and performance */
@media (prefers-reduced-motion: reduce) {
    .gallery-item,
    .enhanced-gallery-image,
    .hover-overlay {
        transition: none !important;
        animation: none !important;
    }
    
    .hover-effects-enabled .gallery-item:hover .enhanced-gallery-image {
        transform: none !important;
    }
    
    .animations-enabled .gallery-item {
        opacity: 1 !important;
        transform: none !important;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .enhanced-gallery-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .gallery-placeholder {
        background: #374151;
        color: #9ca3af;
    }
    
    [data-enable-image-shadows="true"] .gallery-image-container {
        box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);
    }
}

/* Loading states */
.enhanced-gallery-image[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.enhanced-gallery-image[loading="lazy"].loaded {
    opacity: 1;
}

/* Error states */
.enhanced-gallery-image:not([src]),
.enhanced-gallery-image[src=""] {
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.enhanced-gallery-image:not([src])::before,
.enhanced-gallery-image[src=""]::before {
    content: "صورة غير متوفرة";
    color: #6b7280;
    font-size: 14px;
}

/* Print styles */
@media print {
    .hover-overlay {
        display: none !important;
    }
    
    .gallery-item {
        break-inside: avoid;
    }
    
    .layout-carousel .gallery-grid {
        display: grid !important;
        overflow: visible !important;
    }
}
