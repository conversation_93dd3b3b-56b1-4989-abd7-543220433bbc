/* Gaming Theme Special Gallery - Enhanced Performance & Gaming Aesthetics */
.special-gallery {
    padding: 0;
    width: 100%;
    max-width: 100%;
    margin: 0;
    contain: layout style;
    position: relative;
    background: linear-gradient(135deg, rgba(10, 10, 24, 0.9) 0%, rgba(20, 20, 40, 0.8) 100%);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 0 30px rgba(111, 76, 255, 0.2);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(29, 233, 182, 0.05) 50%, transparent 70%);
        pointer-events: none;
        z-index: 1;
    }
}

.special-gallery-flex {
    display: flex;
    flex-direction: row;
    height: 450px;
    width: 100%;
    overflow: hidden;
    margin: 0;
    padding: 8px;
    will-change: contents;
    contain: layout;
    position: relative;
    z-index: 2;
    gap: 4px;
}

/* Gaming-styled gallery item with neon effects - Performance Optimized */
.special-gallery-item {
    position: relative;
    overflow: hidden;
    flex: 1;
    transition: flex 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    margin: 0;
    padding: 0;
    will-change: flex;
    transform: translateZ(0);
    backface-visibility: hidden;
    border-radius: 8px;
    border: 1px solid rgba(29, 233, 182, 0.3);
    background: rgba(10, 15, 25, 0.6);
    box-shadow: 0 0 15px rgba(111, 76, 255, 0.1);
    contain: layout style paint;

    &:hover {
        flex: 3;
        z-index: 2;
        border-color: rgba(29, 233, 182, 0.8);
        box-shadow:
            0 0 25px rgba(111, 76, 255, 0.4),
            0 0 50px rgba(29, 233, 182, 0.2),
            inset 0 0 20px rgba(29, 233, 182, 0.1);
        transform: translate3d(0, -2px, 0);
    }
}

.special-gallery-flex:hover .special-gallery-item:not(:hover) {
    flex: 0.5;
    opacity: 0.7;
    border-color: rgba(29, 233, 182, 0.1);
    box-shadow: 0 0 10px rgba(111, 76, 255, 0.05);
}

/* Gaming-enhanced image container */
.special-gallery-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    contain: strict;
    border-radius: 6px;
    background: linear-gradient(45deg, rgba(10, 15, 25, 0.8), rgba(20, 25, 35, 0.6));

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(29, 233, 182, 0.1) 50%, transparent 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1;
        pointer-events: none;
    }
}

.special-gallery-item:hover .special-gallery-image-container::before {
    opacity: 1;
}

.special-gallery-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform;
    transform: translate3d(0, 0, 0);
    background-color: rgba(15, 20, 30, 0.8);
    filter: brightness(0.9) contrast(1.1);
    contain: layout style paint;

    &.lazy {
        opacity: 0;
        transition: opacity 0.6s ease;
        filter: blur(2px);
    }

    &.loaded {
        opacity: 1;
        filter: blur(0);
    }
}

.special-gallery-item:hover .special-gallery-image {
    transform: scale3d(1.08, 1.08, 1);
    filter: brightness(1.1) contrast(1.2) saturate(1.1);
}

/* Gaming-enhanced content styling */
.special-gallery-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px;
    z-index: 3;
    background: linear-gradient(
        to top,
        rgba(10, 10, 24, 0.95) 0%,
        rgba(20, 20, 40, 0.8) 40%,
        rgba(10, 15, 25, 0.4) 70%,
        transparent 100%
    );
    pointer-events: none;
    border-radius: 0 0 6px 6px;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, transparent, rgba(29, 233, 182, 0.6), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
}

.special-gallery-item:hover .special-gallery-content::before {
    opacity: 1;
}

.special-gallery-text {
    text-align: center;
    color: #fff;
    opacity: 0.9;
    transition: all 0.3s ease;
    pointer-events: auto;
    position: relative;
}

.special-gallery-item:hover .special-gallery-text {
    opacity: 1;
    transform: translateY(-2px);
}

.special-gallery-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow:
        0 0 10px rgba(29, 233, 182, 0.5),
        0 2px 4px rgba(0, 0, 0, 0.3);
    text-align: center;
    will-change: opacity, transform;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    background: linear-gradient(135deg, #fff 0%, rgba(29, 233, 182, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.special-gallery-description {
    font-size: 14px;
    margin-bottom: 12px;
    opacity: 0.85;
    transition: all 0.3s ease;
    will-change: opacity, transform;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    line-height: 1.4;
}

.special-gallery-item:hover .special-gallery-description {
    opacity: 1;
    color: rgba(255, 255, 255, 1);
}

.special-gallery-flex:hover .special-gallery-item:not(:hover) .special-gallery-description {
    opacity: 0;
    transform: translateY(10px);
}

/* Gaming-styled button with neon effects */
.special-gallery-button {
    display: inline-block;
    padding: 8px 18px;
    background: linear-gradient(135deg, transparent 0%, rgba(29, 233, 182, 0.1) 100%);
    color: #fff;
    border: 1px solid rgba(29, 233, 182, 0.6);
    border-radius: 25px;
    font-size: 13px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: background, color, border-color, opacity, transform, box-shadow;
    pointer-events: auto;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    text-shadow: 0 0 8px rgba(29, 233, 182, 0.3);
    box-shadow: 0 0 15px rgba(29, 233, 182, 0.2);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(29, 233, 182, 0.3), transparent);
        transition: left 0.5s ease;
    }
}

.special-gallery-item:hover .special-gallery-button {
    background: linear-gradient(135deg, rgba(29, 233, 182, 0.8) 0%, rgba(111, 76, 255, 0.6) 100%);
    color: #0a0a18;
    border-color: rgba(29, 233, 182, 1);
    box-shadow:
        0 0 20px rgba(29, 233, 182, 0.6),
        0 0 40px rgba(111, 76, 255, 0.3);
    transform: translateY(-2px);
    text-shadow: none;

    &::before {
        left: 100%;
    }
}

.special-gallery-flex:hover .special-gallery-item:not(:hover) .special-gallery-button {
    opacity: 0;
    transform: translateY(10px);
    box-shadow: none;
}

/* Gaming loader styles */
.gaming-loader-ring {
    width: 30px;
    height: 30px;
    border: 2px solid rgba(29, 233, 182, 0.2);
    border-top: 2px solid rgba(29, 233, 182, 0.8);
    border-radius: 50%;
    animation: gaming-spin 1s linear infinite;
    margin: 0 auto 8px;
    box-shadow: 0 0 10px rgba(29, 233, 182, 0.3);
}

@keyframes gaming-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.gaming-loader-text {
    font-family: var(--font-main, 'Arial');
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    text-shadow: 0 0 8px rgba(29, 233, 182, 0.5);
}

/* Gaming enhanced class additional effects */
.special-gallery.gaming-enhanced {
    position: relative;
    overflow: hidden;

    &::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, transparent 20%, rgba(29, 233, 182, 0.02) 50%, transparent 80%);
        animation: gaming-pulse 4s ease-in-out infinite;
        pointer-events: none;
        z-index: 0;
    }
}

@keyframes gaming-pulse {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.1); }
}

@keyframes gaming-ripple {
    0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
    100% { transform: translate(-50%, -50%) scale(4); opacity: 0; }
}

.gaming-item-loaded {
    animation: gaming-fade-in 0.8s ease-out forwards;
}

@keyframes gaming-fade-in {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* Gaming-responsive design optimizations */
@media (max-width: 991px) {
    .special-gallery {
        box-shadow: 0 0 20px rgba(111, 76, 255, 0.15);
    }

    .special-gallery-flex {
        height: 400px;
        padding: 6px;
    }

    .special-gallery-item {
        transition: flex 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
        border-width: 1px;

        &:hover {
            box-shadow:
                0 0 20px rgba(111, 76, 255, 0.3),
                0 0 35px rgba(29, 233, 182, 0.15);
        }
    }

    .special-gallery-image {
        transition: transform 0.4s ease, filter 0.4s ease;
    }
}

@media (max-width: 767px) {
    .special-gallery {
        border-radius: 8px;
        box-shadow: 0 0 15px rgba(111, 76, 255, 0.1);
    }

    .special-gallery-flex {
        height: 300px;
        padding: 4px;
        gap: 2px;
    }

    .special-gallery-title {
        font-size: 16px;
        letter-spacing: 0.3px;
    }

    .special-gallery-description {
        font-size: 13px;
        line-height: 1.3;
    }

    .special-gallery-item {
        transition: flex 0.25s ease, transform 0.25s ease;
        border-radius: 6px;

        &:hover {
            flex: 2;
            transform: translateY(-1px);
        }
    }

    .special-gallery-image {
        transition: transform 0.3s ease, filter 0.3s ease;
    }

    .special-gallery-item:hover .special-gallery-image {
        transform: scale(1.05);
    }

    .special-gallery-button {
        padding: 6px 14px;
        font-size: 12px;
        border-radius: 20px;
    }
}

@media (max-width: 575px) {
    .special-gallery {
        border-radius: 6px;
        margin: 0 4px;
    }

    .special-gallery-flex {
        height: 250px;
        padding: 3px;
        gap: 1px;
    }

    .special-gallery-title {
        font-size: 14px;
        letter-spacing: 0.2px;
        margin-bottom: 6px;
    }

    .special-gallery-description {
        font-size: 12px;
        line-height: 1.2;
        margin-bottom: 8px;
    }

    .special-gallery-button {
        padding: 4px 12px;
        font-size: 11px;
        border-radius: 15px;
        letter-spacing: 0.3px;
    }

    .special-gallery-item {
        transition: flex 0.2s ease, transform 0.2s ease;
        border-radius: 4px;

        &:hover {
            box-shadow:
                0 0 15px rgba(111, 76, 255, 0.2),
                0 0 25px rgba(29, 233, 182, 0.1);
        }
    }

    .special-gallery-image {
        transition: transform 0.2s ease, filter 0.2s ease;
    }

    .special-gallery-item:hover .special-gallery-image {
        transform: scale(1.03);
    }
}

/* Gaming accessibility and reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .special-gallery-item,
    .special-gallery-image,
    .special-gallery-description,
    .special-gallery-button,
    .special-gallery-content::before,
    .special-gallery-button::before {
        transition-duration: 0.1s !important;
    }

    .special-gallery-item:hover .special-gallery-image {
        transform: scale(1.02) !important;
    }

    .special-gallery-item:hover {
        box-shadow: 0 0 10px rgba(111, 76, 255, 0.1) !important;
    }

    .special-gallery::before {
        display: none !important;
    }
}

/* Gaming theme dark mode enhancements */
@media (prefers-color-scheme: dark) {
    .special-gallery {
        background: linear-gradient(135deg, rgba(5, 5, 15, 0.95) 0%, rgba(10, 10, 25, 0.9) 100%);
        box-shadow: 0 0 35px rgba(111, 76, 255, 0.25);
    }

    .special-gallery-item {
        background: rgba(5, 10, 20, 0.8);
        border-color: rgba(29, 233, 182, 0.4);

        &:hover {
            border-color: rgba(29, 233, 182, 0.9);
            box-shadow:
                0 0 30px rgba(111, 76, 255, 0.5),
                0 0 60px rgba(29, 233, 182, 0.25);
        }
    }
}
