/* First Gallery Component - Gaming Theme with Gentle Bouncing Animation */

/* Ensure all gallery images are visible */
.gaming-gallery-showcase {
    img {
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }

    .gallery-image {
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }
}

/* Main Gallery Section with Fast Smooth Animation - No Bounce */
.gaming-gallery-showcase {
    margin: 0;
    padding: 0;
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    overflow: hidden;
    opacity: 0;
    transform: translate3d(0, 30px, 0) scale(0.95);
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;
    contain: layout style paint;

    &.animate-in {
        opacity: 1;
        transform: translate3d(0, 0, 0) scale(1);
    }
}

/* Gallery Bouncing Animation */
@keyframes galleryBounceIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-5px) scale(1.02);
    }
    85% {
        transform: translateY(2px) scale(0.99);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Banner with Animation */
.game-gallery-banner {
    position: relative;
    background-color: #0c1824;
    background-size: cover;
    background-position: center;
    overflow: hidden;
    padding: 80px 20px 140px;
    min-height: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    box-shadow: 0 10px 30px -8px rgba(0, 0, 0, 0.5);
    width: 100%;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
        animation: bannerGentleBounce 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s forwards;
    }
}

/* Banner Gentle Bouncing Animation */
@keyframes bannerGentleBounce {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-3px);
    }
    85% {
        transform: translateY(1px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Content Animation */
.game-gallery-content {
    position: relative;
    z-index: 3;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
    background-color: rgba(8, 8, 24, 0.4);
    border-radius: 15px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(29, 233, 182, 0.2);
    box-shadow: 0 0 30px rgba(29, 233, 182, 0.1);
    opacity: 0;
    transform: translate3d(0, 25px, 0) scale(0.95);
    transition: opacity 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;
    contain: layout style paint;

    &.animate-in {
        opacity: 1;
        transform: translate3d(0, 0, 0) scale(1);
    }
}

/* Content bounce animation removed - using smooth transitions only */

/* Title Animation - Fast and Smooth */
.game-gallery-title {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
    transition: opacity 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;

    &.animate-in {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }

    h2 {
        color: #1DE9B6;
        font-weight: 700;
        font-size: 2.5rem;
        margin-bottom: 20px;
        text-shadow: 0 0 15px rgba(29, 233, 182, 0.7);
        letter-spacing: 0.03em;
        position: relative;
        display: inline-block;

        &::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, #1DE9B6, transparent);
            box-shadow: 0 0 10px rgba(29, 233, 182, 0.8);
            transition: width 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1.2s;
        }
    }

    &.animate-in h2::after {
        width: 80px;
    }
}

/* Title bounce animation removed - using smooth transitions only */

/* Description Animation - Fast and Smooth */
.game-gallery-description {
    opacity: 0;
    transform: translate3d(0, 15px, 0);
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;

    &.animate-in {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }

    p {
        color: #ffffff;
        font-size: 1.2rem;
        line-height: 1.7;
        margin: 25px 0;
        text-shadow: 0 0 8px rgba(0, 0, 0, 0.8);
    }
}

/* Button Animation - Fast and Smooth */
.game-gallery-button {
    opacity: 0;
    transform: translate3d(0, 20px, 0) scale(0.9);
    transition: opacity 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;

    &.animate-in {
        opacity: 1;
        transform: translate3d(0, 0, 0) scale(1);
    }
}

/* Button bounce animation removed - using smooth transitions only */

/* Icons Container Animation */
.game-gallery-icons {
    position: absolute;
    bottom: 40px;
    left: 0;
    right: 0;
    z-index: 3;
    width: 100%;
    text-align: center;
    opacity: 0;
    transform: translate3d(0, 30px, 0);
    transition: opacity 1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;
    contain: layout style paint;

    &.animate-in {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

/* Individual Icon Cards Animation - Clean Sliding Up Effect */
.game-card {
    flex: 0 0 150px;
    max-width: 150px;
    perspective: 1000px;
    margin-bottom: 10px;
    transform-style: preserve-3d;
    opacity: 0;
    transform: translate3d(0, 40px, 0);
    transition: opacity 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;
    contain: layout style paint;

    /* Ensure images inside cards are always visible */
    .gallery-image {
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }

    &.animate-in {
        opacity: 1;
        transform: translate3d(0, 0, 0);

        /* Add subtle slide-up animation with keyframes for enhanced effect */
        animation: cardSlideUp 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    /* Enhanced staggered animation delays for smooth sliding effect */
    &:nth-child(1) {
        transition-delay: 0s;
        animation-delay: 0s;
    }
    &:nth-child(2) {
        transition-delay: 0.08s;
        animation-delay: 0.08s;
    }
    &:nth-child(3) {
        transition-delay: 0.16s;
        animation-delay: 0.16s;
    }
    &:nth-child(4) {
        transition-delay: 0.24s;
        animation-delay: 0.24s;
    }
    &:nth-child(5) {
        transition-delay: 0.32s;
        animation-delay: 0.32s;
    }
    &:nth-child(6) {
        transition-delay: 0.40s;
        animation-delay: 0.40s;
    }
    &:nth-child(7) {
        transition-delay: 0.48s;
        animation-delay: 0.48s;
    }
    &:nth-child(8) {
        transition-delay: 0.56s;
        animation-delay: 0.56s;
    }
}

/* Clean Card Slide Up Animation - Performance Optimized
 * Creates a smooth sliding up effect where cards start 40px below their final position
 * and slide smoothly upward with hardware acceleration using translate3d()
 * Includes proper will-change cleanup for optimal performance
 */
@keyframes cardSlideUp {
    0% {
        opacity: 0;
        transform: translate3d(0, 40px, 0);
    }
    100% {
        opacity: 1;
        transform: translate3d(0, 0, 0);
        will-change: auto;
    }
}

/* Full width banner container */
.full-width-banner {
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin: 0;
}

/* Banner overlays and effects */
.game-gallery-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
        rgba(12, 24, 36, 0.7) 0%,
        rgba(8, 8, 24, 0.85) 100%);
    z-index: 1;
}

.game-gallery-banner::after {
    content: '';
    position: absolute;
    inset: -1px;
    background: linear-gradient(130deg,
        rgba(0, 191, 165, 0) 20%,
        rgba(29, 233, 182, 0.1) 30%,
        rgba(29, 233, 182, 0.1) 50%,
        rgba(0, 191, 165, 0.1) 70%,
        rgba(29, 233, 182, 0) 80%);
    border-radius: 0;
    pointer-events: none;
    z-index: 2;
    background-size: 300% 300%;
    animation: border-glow 3s linear infinite;
}

/* Gaming particle overlay */
.gaming-particle-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(29, 233, 182, 0.05) 0%, transparent 20%),
        radial-gradient(circle at 75% 50%, rgba(76, 201, 240, 0.05) 0%, transparent 20%),
        radial-gradient(circle at 50% 80%, rgba(123, 97, 255, 0.05) 0%, transparent 20%);
    background-size: 200% 200%, 150% 150%, 250% 250%;
    animation: particle-shift 20s ease infinite;
    opacity: 0.6;
    pointer-events: none;
}

/* Button styles */
.game-cta-button {
    display: inline-block;
    background: linear-gradient(90deg, #00BFA5, #1DE9B6);
    border: none;
    padding: 12px 30px;
    border-radius: 50px;
    color: #0c1824;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 5px 20px rgba(29, 233, 182, 0.5);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
    text-decoration: none;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, #1DE9B6, #4CC9F0);
        z-index: -1;
        transition: transform 0.6s cubic-bezier(0.65, 0, 0.35, 1);
        transform: scaleX(0);
        transform-origin: right;
    }

    &:hover {
        transform: translate3d(0, -5px, 0);
        box-shadow: 0 10px 25px rgba(29, 233, 182, 0.7);
        color: #0c1824;
        text-decoration: none;
        will-change: transform;

        &::before {
            transform: scaleX(1);
            transform-origin: left;
        }
    }
}

/* Cards container */
.game-cards-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Gallery links */
.game-gallery-link {
    display: block;
    text-decoration: none;
    color: inherit;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    outline: none;

    &:hover,
    &:focus {
        text-decoration: none;
        color: inherit;
        transform: translate3d(0, -2px, 0);
        will-change: transform;
    }

    &:focus {
        box-shadow: 0 0 0 3px rgba(29, 233, 182, 0.5);
    }
}

/* Gallery icons */
.game-gallery-icon {
    background-color: rgba(26, 46, 63, 0.9);
    border: 2px solid #1DE9B6;
    border-radius: 12px;
    padding: 20px;
    height: 100%;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateZ(0);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 10px rgba(29, 233, 182, 0.3);
    opacity: 1;
    width: 100%;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    will-change: transform, box-shadow;
    backface-visibility: hidden;

    /* Ensure content inside is visible */
    img, .icon-placeholder {
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(29, 233, 182, 0.2) 0%, transparent 80%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    &:hover {
        transform: translateY(-8px) translateZ(20px);
        box-shadow: 0 15px 30px rgba(29, 233, 182, 0.5), 0 0 15px rgba(29, 233, 182, 0.5);
        border-color: #4CC9F0;

        &::before {
            opacity: 1;
        }
    }
}

.game-card:hover {
    transform: rotateY(10deg) rotateX(10deg);
}

/* Gallery images */
.gallery-image {
    max-width: 100%;
    max-height: 60px;
    object-fit: contain;
    display: block;
    opacity: 1; /* Make sure images are visible */
    filter: drop-shadow(0 0 5px rgba(29, 233, 182, 0.5));
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), filter 0.3s ease, opacity 0.3s ease;
    will-change: transform, filter;

    &.loaded {
        opacity: 1;
    }

    /* Ensure images are visible during loading */
    &[src] {
        opacity: 1;
    }
}

.game-gallery-icon:hover .gallery-image {
    transform: scale(1.1);
    filter: drop-shadow(0 0 10px rgba(29, 233, 182, 0.8));
}

/* Icon placeholder */
.icon-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1DE9B6;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(29, 233, 182, 0.7);
    font-size: 1.5rem;
    flex-direction: column;
    gap: 5px;

    i {
        font-size: 2rem;
        opacity: 0.7;
    }
}

/* Link overlay */
.gallery-link-overlay {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    background: rgba(29, 233, 182, 0.9);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 2;

    i {
        font-size: 12px;
        color: #0c1824;
    }
}

.game-gallery-link:hover .gallery-link-overlay,
.game-gallery-icon:hover .gallery-link-overlay {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
}

/* Keyframe animations */
@keyframes particle-shift {
    0%, 100% { background-position: 0% 0%, 0% 0%, 0% 0%; }
    25% { background-position: 50% 25%, 25% 75%, 75% 50%; }
    50% { background-position: 100% 50%, 50% 100%, 100% 25%; }
    75% { background-position: 50% 75%, 75% 25%, 25% 100%; }
}

@keyframes border-glow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Responsive design with faster mobile animations */
@media (max-width: 768px) {
    .gaming-gallery-showcase {
        transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .game-gallery-content {
        transition: opacity 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        padding: 30px 15px;
    }

    .game-gallery-title {
        transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        h2 {
            font-size: 1.8rem;
        }
    }

    .game-gallery-description {
        transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        p {
            font-size: 1rem;
        }
    }

    .game-gallery-button {
        transition: opacity 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .game-gallery-icons {
        transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .game-card {
        flex: 0 0 120px;
        max-width: 120px;
        transition: opacity 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        /* Faster sliding animation on mobile */
        &.animate-in {
            animation: cardSlideUpMobile 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        /* Faster staggered delays for mobile */
        &:nth-child(1) { animation-delay: 0s; }
        &:nth-child(2) { animation-delay: 0.06s; }
        &:nth-child(3) { animation-delay: 0.12s; }
        &:nth-child(4) { animation-delay: 0.18s; }
        &:nth-child(5) { animation-delay: 0.24s; }
        &:nth-child(6) { animation-delay: 0.30s; }
        &:nth-child(7) { animation-delay: 0.36s; }
        &:nth-child(8) { animation-delay: 0.42s; }
    }

    .game-gallery-banner {
        padding: 60px 15px 120px;
    }

    .game-gallery-icon {
        padding: 15px;
        min-height: 80px;
    }

    .gallery-link-overlay {
        width: 20px;
        height: 20px;
        right: 8px;

        i {
            font-size: 10px;
        }
    }

    /* Mobile-optimized slide up animation */
    @keyframes cardSlideUpMobile {
        0% {
            opacity: 0;
            transform: translate3d(0, 30px, 0);
        }
        100% {
            opacity: 1;
            transform: translate3d(0, 0, 0);
            will-change: auto;
        }
    }
}

@media (max-width: 480px) {
    .gaming-gallery-showcase {
        transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .game-gallery-content {
        transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .game-gallery-title {
        transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .game-gallery-description {
        transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .game-gallery-button {
        transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .game-gallery-icons {
        transition: opacity 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .game-card {
        flex: 0 0 100px;
        max-width: 100px;
        transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        /* Even faster sliding animation on small mobile */
        &.animate-in {
            animation: cardSlideUpSmall 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        /* Even faster staggered delays for small mobile */
        &:nth-child(1) { animation-delay: 0s; }
        &:nth-child(2) { animation-delay: 0.05s; }
        &:nth-child(3) { animation-delay: 0.10s; }
        &:nth-child(4) { animation-delay: 0.15s; }
        &:nth-child(5) { animation-delay: 0.20s; }
        &:nth-child(6) { animation-delay: 0.25s; }
        &:nth-child(7) { animation-delay: 0.30s; }
        &:nth-child(8) { animation-delay: 0.35s; }
    }

    .game-cards-container {
        gap: 15px;
    }

    .game-gallery-icon {
        padding: 12px;
        min-height: 70px;
    }

    .gallery-image {
        max-height: 45px;
    }

    /* Small mobile-optimized slide up animation */
    @keyframes cardSlideUpSmall {
        0% {
            opacity: 0;
            transform: translate3d(0, 25px, 0);
        }
        100% {
            opacity: 1;
            transform: translate3d(0, 0, 0);
            will-change: auto;
        }
    }
}

/* Reduced motion support for accessibility */
@media (prefers-reduced-motion: reduce) {
    .gaming-gallery-showcase,
    .game-gallery-content,
    .game-gallery-title,
    .game-gallery-description,
    .game-gallery-button,
    .game-gallery-icons,
    .game-card {
        transition: opacity 0.2s ease !important;
        animation: none !important;
        transform: none !important;
    }

    .gaming-gallery-showcase.animate-in,
    .game-gallery-content.animate-in,
    .game-gallery-title.animate-in,
    .game-gallery-description.animate-in,
    .game-gallery-button.animate-in,
    .game-gallery-icons.animate-in,
    .game-card.animate-in {
        opacity: 1 !important;
        transform: none !important;
    }

    /* Disable card slide animations for reduced motion */
    @keyframes cardSlideUp {
        0%, 100% {
            opacity: 1;
            transform: none;
        }
    }

    @keyframes cardSlideUpMobile {
        0%, 100% {
            opacity: 1;
            transform: none;
        }
    }

    @keyframes cardSlideUpSmall {
        0%, 100% {
            opacity: 1;
            transform: none;
        }
    }
}

/* Force images to be visible in all cases */
.gaming-gallery-showcase img,
.gaming-gallery-showcase .gallery-image,
.game-gallery-icon img,
.game-gallery-icon .gallery-image {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    max-width: 100% !important;
    max-height: 60px !important;
    object-fit: contain !important;
}

/* Accessibility - Disable animations for reduced motion */
@media (prefers-reduced-motion: reduce) {
    .gaming-gallery-showcase,
    .game-gallery-banner,
    .game-gallery-content,
    .game-gallery-title,
    .game-gallery-description,
    .game-gallery-button,
    .game-gallery-icons,
    .game-card,
    .game-cta-button,
    .gaming-particle-overlay {
        animation: none !important;
        transition: opacity 0.3s ease !important;
        transform: none !important;
        opacity: 1 !important;
    }

    /* Ensure images are visible in reduced motion mode */
    img, .gallery-image {
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }
}
