/*
  Block Title
*/
.s-block{
  &__title{
    @apply center-between mb-4 md:mb-8;

    .right-side{
      @apply rtl:pl-8 ltr:pr-8;
    }

    h2{ 
      @apply text-lg font-bold relative leading-[1.2];
    }

    p{
      @apply text-sm opacity-60 mt-2 line-clamp-1 sm:line-clamp-2 rtl:md:pl-16 ltr:md:pr-16;
    }

    &-nav{
      @apply hidden sm:flex ltr:flex-row-reverse rtl:space-x-reverse space-x-2.5 rtl:mr-6 ltr:ml-6;
    }
  }

  &__display-all{
    @apply inline-flex items-center gap-2 text-sm font-bold opacity-80 hover:opacity-100;

    i{
      @apply ltr:rotate-180 inline-block;
    }
  }
}

/*
  Main slider
*/
.s-block--hero-slider{
  .swiper-slide{
    @apply h-80 sm:h-96 lg:h-116 xl:h-132 relative bg-gray-800;

    .swiper-lazy{
      &:after{
        @apply content-[''] absolute transition top-0 bg-black/60 w-full h-full;
      }
    }

	.overlay-bg{
		&:after{
		  @apply content-[''] absolute top-0 bg-black/60 w-full h-full;
		}
	  }
  }
}

/* Banner Block */
.banner {
  @media screen and (min-width: 768px) {
    &:hover {
      .banner__title {
        animation: toTopFromBottom 0.9s;
      }

      .banner__description {
        animation: delayKeyframe 0.3s, toTopFromBottom 0.9s 0.3s;
      }
    }
  }

  &--fixed{
    img{
      @apply w-full object-cover rounded-md border-none block bg-gray-100;
    }
  }
}

/* Square Banner Block */
.banner-entry{
  @apply h-[200px] bg-gray-100 text-white text-center p-3 xs:p-4 rounded-md overflow-hidden relative bg-cover bg-no-repeat bg-center;

  article{
    @apply flex flex-col items-center justify-center h-full p-2.5 
  }
  
  &.square-photos h3{
    @apply h-full px-3;
  }

  h3{
    @apply flex flex-col justify-center xs:px-1;
   
    &.text-with-border{
      @apply xs:border border-dashed border-gray-200 rounded-md;

      span{
        @apply line-clamp-2;
      }
    }
  }

  p{
    @apply line-clamp-2
  }

  h3,p{
    @apply opacity-0 hover:opacity-100 transition-all duration-300 relative z-1 translate-y-6;
  }
  
  &:hover{
    h3{
      @apply opacity-100 translate-y-0;
    }

    p{
      @apply opacity-80 translate-y-0;
    }
  }

  .two-row &{
    &:first-child{
      @apply md:h-full sm:col-span-1 sm:row-span-2 p-5;

      > a{
        @apply opacity-100;
      }

      h3{
        @apply text-2xl leading-12;
      }
    }
  }
}

.has-overlay{ 
  &:after{
    @apply content-[''] h-full w-full bg-black/60 absolute top-0 left-0 transition-colors duration-300;
  }

  &.with-hover{
    &:after{
      transition: opacity .3s;
      opacity: 0;
    }
    &:hover{
      &:after{
      opacity: 1;
    }
    }
  }

  &:hover:after{
    @apply bg-black/70
  }
}


/* Full banner block */
.full-banner-entry {
  @apply row-span-2 h-80 md:h-96 lg:h-116 xl:h-132 bg-stone-200 transition-opacity hover:opacity-95 bg-center bg-cover text-white text-center p-5 overflow-hidden relative bg-fixed;

  background-attachment: initial;

  &::after,
  &::before {
    height: 100%;
    width: 100%;
    top: 0;
    right: -93%;
    content: "";
    background: var(--color-primary);
    position: absolute;
    transition: all 0.3s ease-in-out;
    transform: rotate(220deg) translateY(-50%) translateX(33%);
    opacity: 0.25;
  }

  &::before {
    background: #fff;
    right: auto;
    transform: rotate(220deg) translateY(-40%) translateX(0);

    [dir="ltr"] & {
      left: -52%;
    }
  }

  @media (max-width: 1330px) {
    &::after {
      top: -50%;
    }
  }

  @media (max-width: 480px) {
    &::before {
      top: 20%;
    }

    &::after {
      top: -80%;
    }
  }
}

/*
  Tabs Block
*/
.tabs-wrapper{
  .tabs__item{
    @apply hidden transition-all duration-300;

    &.is-active{
      @apply block;
    }
  }
}

.s-block--tabs-produtcs{
  .tabs{
    @apply text-center pb-6 sm:pb-10 px-5 -mx-2.5 sm:-mx-5 lg:mx-0 rtl:space-x-reverse space-x-1 md:space-x-4 whitespace-nowrap overflow-x-auto;
  }

  .tab-trigger{
    &.is-active button{
      @apply bg-primary text-primary-reverse border-primary;
    }
  }
}

.s-block--special-products{
  .tabs{
    @apply mb-4 sm:mb-0 rtl:space-x-reverse space-x-2 md:space-x-8 whitespace-nowrap overflow-x-auto;

    &__item{
      @apply flex-1 gap-2.5 sm:gap-8;

      &.is-active{
        display: grid;
      }
    }
  }

  .tab-trigger{
    @apply text-gray-500 font-bold leading-7;

    &.is-active button{
      @apply text-primary border-primary;
    }
  }
}

.s-block--tabs-produtcs,
.s-block--special-products {
  &:not(.tabs-initialized) {
    .tabs-wrapper > div:not(:first-child) {
      display: none;
    }
  }
}

/* feature products*/
.s-block--features-products {
  &.two-cols {
    .inner {
      display: grid;
      @apply lg:grid-cols-2 gap-5;
    }
  }
}

/* slider with bg block */
.s-block--slider-with-bg{
  @apply bg-gray-100 pb-8 sm:pb-16 mt-8 sm:mt-16 first-of-type:mt-0;

  .slider-bg{
    @apply h-96 sm:h-116 transition-opacity bg-center bg-cover text-white relative;

    &:before{
      content: '';
      @apply h-full w-full bg-black opacity-60 absolute top-0 left-0;
    }
  }

  .s-slider-block{
    &__title-right{
      @apply hidden;
    }

    &__title-left{
      @apply w-full flex justify-between;
    }

    &__display-all{
      @apply block btn btn--outline light btn--rounded-full;
      @apply grow-0 bg-transparent text-white #{!important};
    }
  }
}

/*
  Main Links Block
*/
.slide--cat-entry {
  @apply bg-white rounded-md h-36 p-3 text-center flex flex-col items-center justify-center transition duration-500 hover:shadow-default hover:text-gray-500 border border-gray-100;

  i {
    @apply inline-block text-primary text-icon-lg mb-4;
  }

  h2 {
    @apply font-bold text-sm;
  }
}

/*
  offers slider
*/
.slide--offer-entry {
  @apply bg-white rounded-md h-36 text-center flex flex-col items-center justify-between text-gray-600 transition duration-500 hover:shadow-default border border-gray-200;
}


.s-block {
  @apply mt-8 sm:mt-16;

  &--full-bg{
    + footer,
    + .s-block--full-bg,
    &:first-of-type{
      @apply mt-0;
    }

    + .merge-with-top-component{
      @apply -mt-28 md:-mt-32 z-1;

      .s-slider-block__title {
        color: #fff !important;
        @apply text-center sm:text-start sm:mb-4;
      }
    }

    + .merged-has-no-title{
      @apply -mt-16 md:-mt-[74px];

      .s-slider-block__title {
        @apply sm:mb-0;
      }
    }
  }

  &--categories{
    @apply relative overflow-hidden;

    &__title{
      @apply font-bold text-base sm:text-lg sm:mb-4 relative z-1 min-h-[28px];
    }
  }

  &--features{
    &__item{
      @apply cursor-pointer bg-white transition-shadow duration-500 hover:shadow-default rounded-md flex-grow flex flex-col items-center px-4 py-8 md:px-8 text-center;

      &:first-child{
        @apply col-span-2 md:col-span-1;
      }

      .feature-icon{
        @apply mb-3 bg-primary w-16 h-16 rounded-full flex items-center justify-center overflow-hidden;

        i{
          @apply text-primary-reverse text-3xl;
        }
      }

      h2{
        @apply font-bold text-sm text-gray-800 mb-1;
      }

      p{
        @apply text-sm text-gray-500;
      }

      &:hover {
        .feature-icon i {
          animation: toRightFromLeft 0.3s forwards;
        }
      }
    }
  }
}

// repeated block with narrow top spacing
.s-block--features + .s-block--features,
.s-block--fixed-banner + .s-block--fixed-banner,
.s-block--banners + .s-block--banners {
  margin-top: 2em;
}

.s-block--tabs-produtcs + .s-block--tabs-produtcs {
  padding-top: 2em;
}