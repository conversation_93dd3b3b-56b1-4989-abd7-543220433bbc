/**
 * Performance Optimizations CSS
 * Global performance improvements for theme components
 */

/* CSS Containment for better performance */
.s-block {
    contain: layout style;
}

/* Component-specific containment */
.gaming-moving-text-section,
.video-banner-section,
.special-gallery,
.gaming-banners-section,
.gaming-gallery-showcase,
.banner-with-offer-section,
.store-header,
.store-footer,
.main-nav-container,
.categories-dropdown-container,
.tooltip-content,
custom-salla-product-card,
.s-product-card,
.product-card,
salla-products-list {
    contain: layout style paint;
}

/* Optimize will-change usage */
.gaming-moving-text,
.special-gallery-image,
.gaming-banner-image,
.banner-image,
.game-gallery-image,
.mburger .sicon-menu,
.img-magnifier-glass,
.categories-arrow,
.footer a {
    will-change: transform;
}

/* Clean up will-change after animations complete */
.animation-complete,
.loaded {
    will-change: auto !important;
}

/* Hardware acceleration for transforms */
.gaming-moving-text,
.special-gallery-item,
.gaming-banner-slide,
.video-banner-section,
.product-card,
.s-product-card,
custom-salla-product-card,
.store-header,
.main-nav-container,
.mburger .sicon-menu,
.tooltip-content,
.img-magnifier-glass {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
}

/* Optimize expensive properties */
.gaming-particle,
.gaming-glow,
.circuit-animation {
    will-change: transform, opacity;
    contain: layout style paint;
}

/* Reduce repaints for hover effects */
@media (hover: hover) {
    .special-gallery-item:hover,
    .gaming-banner-slide:hover,
    .category-card:hover,
    .mburger .sicon-menu:hover,
    .footer a:hover,
    .sub-menu li a:hover {
        will-change: transform, box-shadow;
    }
}

/* Performance optimizations for low-end devices */
@media (max-width: 768px) and (max-resolution: 1.5dppx) {
    /* Disable expensive effects on low-end mobile devices */
    .gaming-particle,
    .gaming-glow,
    .complex-shadow {
        display: none;
    }

    /* Simplify animations */
    .gaming-moving-text,
    .special-gallery-image {
        transition-duration: 0.2s;
        animation-duration: 0.2s;
    }

    /* Reduce box-shadow complexity */
    .gaming-banner-slide,
    .special-gallery-item {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
}

/* Accessibility - Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    /* Disable complex animations */
    .gaming-particle,
    .gaming-glow,
    .circuit-animation,
    .gaming-moving-text::before,
    .gaming-moving-text::after {
        animation: none !important;
        opacity: 0 !important;
    }

    /* Keep essential functionality */
    .gaming-moving-text {
        animation: none !important;
        position: static !important;
        transform: none !important;
    }
}

/* Optimize for print */
@media print {
    .gaming-particle,
    .gaming-glow,
    .gaming-banner-effects,
    .video-banner-section,
    .gaming-moving-text-section {
        display: none !important;
    }

    .special-gallery,
    .gaming-banners-section {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }
}

/* Battery optimization */
@media (prefers-reduced-data: reduce) {
    /* Disable data-heavy animations */
    .gaming-particle,
    .gaming-glow {
        display: none;
    }

    /* Reduce animation frequency */
    .gaming-moving-text {
        animation-duration: 60s !important;
    }

    /* Disable autoplay videos */
    video {
        autoplay: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .special-gallery-item,
    .gaming-banner-slide {
        border: 2px solid currentColor !important;
        box-shadow: none !important;
    }

    .gaming-moving-text {
        text-shadow: none !important;
        background: black !important;
        color: white !important;
    }
}

/* Optimize for slow connections */
@media (prefers-reduced-data: reduce) {
    /* Disable background images on slow connections */
    .special-gallery-image,
    .gaming-banner-image {
        background-image: none !important;
    }

    /* Show fallback content */
    .special-gallery-item::after {
        content: "Image loading...";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        background: rgba(0, 0, 0, 0.7);
        padding: 10px;
        border-radius: 4px;
    }
}

/* Optimize scrolling performance */
.s-block,
.gaming-moving-text-section,
.video-banner-section {
    scroll-behavior: smooth;
    overscroll-behavior: contain;
}

/* Optimize image loading */
img {
    content-visibility: auto;
    contain-intrinsic-size: 300px 200px;
}

/* Lazy loading optimization */
.lazy {
    content-visibility: auto;
    contain-intrinsic-size: 300px 200px;
}

/* Performance monitoring styles */
.performance-warning {
    position: fixed;
    top: 10px;
    right: 10px;
    background: #ff4444;
    color: white;
    padding: 10px;
    border-radius: 4px;
    z-index: 9999;
    font-size: 12px;
    max-width: 200px;
    display: none;
}

.performance-warning.show {
    display: block;
}

/* Critical rendering path optimization */
.above-fold {
    content-visibility: visible;
}

.below-fold {
    content-visibility: auto;
    contain-intrinsic-size: 0 500px;
}

/* Optimize font loading */
@font-face {
    font-display: swap;
}

/* Reduce layout shifts */
.gaming-moving-text-container {
    min-height: 30px;
}

.video-banner-container {
    min-height: 500px;
}

.special-gallery {
    min-height: 400px;
}

/* GPU layer optimization */
.gpu-optimized {
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Memory leak prevention */
.cleanup-complete {
    will-change: auto;
    transform: none;
    transition: none;
    animation: none;
}

/* Header and Footer specific optimizations */
.store-header {
    content-visibility: auto;
    contain-intrinsic-size: 0 120px;
}

.store-footer {
    content-visibility: auto;
    contain-intrinsic-size: 0 400px;
}

/* Navigation menu optimizations */
.main-menu {
    contain: layout style;
}

.sub-menu {
    contain: layout style paint;
    content-visibility: auto;
    contain-intrinsic-size: 0 200px;
}

/* Tooltip optimizations */
.tooltip-content {
    content-visibility: auto;
    contain-intrinsic-size: 200px 100px;
}

/* Categories dropdown optimizations */
.categories-dropdown-menu {
    contain: layout style paint;
    content-visibility: auto;
    contain-intrinsic-size: 300px 400px;
}
