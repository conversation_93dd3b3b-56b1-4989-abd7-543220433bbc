/* Gaming WhatsApp Float Button Styles - Optimized */
.gaming-whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #25D366, #128C7E);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    border: 2px solid rgba(255, 0, 255, 0.3);
    overflow: visible;
    will-change: transform;
}

.gaming-whatsapp-float:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.6),
                0 0 20px rgba(255, 0, 255, 0.5);
    border-color: rgba(0, 255, 255, 0.6);
}

.gaming-whatsapp-icon {
    width: 30px;
    height: 30px;
    position: relative;
    z-index: 2;
}

.gaming-whatsapp-icon svg {
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
}

/* Pulse animation */
.gaming-whatsapp-pulse {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    border: 2px solid rgba(255, 0, 255, 0.6);
    animation: gaming-pulse 2s infinite;
    z-index: 1;
}

/* Glow effect */
.gaming-whatsapp-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.2) 0%, transparent 70%);
    animation: gaming-glow 3s infinite alternate;
    z-index: 0;
}

/* Tooltip styling */
.gaming-tooltip {
    position: absolute !important;
    bottom: 70px !important;
    left: 50% !important;
    right: auto !important;
    top: auto !important;
    transform: translateX(-50%) !important;
    background: rgba(20, 20, 35, 0.95) !important;
    color: white !important;
    padding: 10px 15px !important;
    border-radius: 12px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    white-space: nowrap !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    border: 1px solid rgba(255, 0, 255, 0.4) !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4),
                0 0 15px rgba(255, 0, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3) !important;
    min-width: 140px !important;
    max-width: 200px !important;
    text-align: center !important;
    z-index: 9999 !important;
}

.gaming-tooltip:before {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 50% !important;
    right: auto !important;
    transform: translateX(-50%) !important;
    border: 8px solid transparent !important;
    border-top-color: rgba(20, 20, 35, 0.95) !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) !important;
}

.gaming-tooltip:after {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 50% !important;
    right: auto !important;
    transform: translateX(-50%) !important;
    border: 6px solid transparent !important;
    border-top-color: rgba(255, 0, 255, 0.4) !important;
}

.gaming-whatsapp-float:hover .gaming-tooltip {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateX(-50%) translateY(-5px) !important;
}

/* Animations */
@keyframes gaming-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes gaming-glow {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }
    100% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .gaming-whatsapp-float {
        width: 55px;
        height: 55px;
        bottom: 15px;
        right: 15px;
    }

    .gaming-whatsapp-icon {
        width: 25px;
        height: 25px;
    }

    .gaming-tooltip {
        font-size: 11px !important;
        padding: 8px 12px !important;
        bottom: 65px !important;
        min-width: 120px !important;
    }

    .gaming-tooltip:before {
        border-width: 6px !important;
    }

    .gaming-tooltip:after {
        border-width: 4px !important;
    }
}

/* RTL support */
[dir="rtl"] .gaming-whatsapp-float {
    right: auto;
    left: 20px;
}

@media (max-width: 768px) {
    [dir="rtl"] .gaming-whatsapp-float {
        left: 15px;
    }
}

/* Performance optimizations */
.gaming-whatsapp-float,
.gaming-whatsapp-pulse,
.gaming-whatsapp-glow {
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .gaming-whatsapp-pulse,
    .gaming-whatsapp-glow {
        animation: none;
    }
    
    .gaming-whatsapp-float {
        transition: none;
    }
    
    .gaming-whatsapp-float:hover {
        transform: none;
    }
}
