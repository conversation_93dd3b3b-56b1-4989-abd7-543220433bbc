class CategoriesDropdown extends HTMLElement {
    constructor() {
        super();
        this.categories = [];
        this.isOpen = false;
        this.isLoading = false;
    }

    connectedCallback() {
        if (window.app?.status === 'ready') {
            this.onReady();
        } else {
            document.addEventListener('theme::ready', () => this.onReady());
        }
    }

    async onReady() {
        await this.loadCategories();
        this.render();
        this.attachEvents();

        setTimeout(() => {
            this.attachProductsEvents();
            console.log('تم ربط أحداث المنتجات بعد التحديث');
        }, 500);
    }

    async loadCategories() {
        if (this.isLoading) return;

        this.isLoading = true;
        try {
            const { data } = await salla.api.component.getMenus();
            console.log('جميع البيانات من API:', data);

            // استخدام جميع البيانات كما هي من API
            this.categories = data || [];

            console.log('التصنيفات النهائية:', this.categories);

        } catch (error) {
            console.error('خطأ في جلب التصنيفات:', error);
            salla.notify.error('فشل في تحميل التصنيفات');
        } finally {
            this.isLoading = false;
        }
    }

    render() {
        // Use requestAnimationFrame to batch DOM updates
        requestAnimationFrame(() => {
            this.innerHTML = `
                <div class="categories-dropdown-container">
                    <!-- زر التصنيفات -->
                    <button class="categories-trigger-btn" aria-label="عرض التصنيفات">
                        <i class="sicon-list"></i>
                        <span class="categories-text">التصنيفات</span>
                        <i class="sicon-keyboard_arrow_down categories-arrow ${this.isOpen ? 'rotated' : ''}"></i>
                    </button>

                    <!-- قائمة التصنيفات المنسدلة -->
                    <div class="categories-dropdown-menu ${this.isOpen ? 'show' : ''}">
                        ${this.isLoading ? this.renderLoader() : this.renderCategories()}
                    </div>

                    <!-- خلفية شفافة للإغلاق -->
                    <div class="categories-backdrop ${this.isOpen ? 'show' : ''}"></div>
                </div>
            `;
        });
    }

    renderLoader() {
        return `
            <div class="categories-loader">
                <div class="loader-spinner"></div>
                <span>جاري تحميل التصنيفات...</span>
            </div>
        `;
    }

    renderCategories() {
        if (!this.categories.length) {
            return `
                <div class="categories-empty">
                    <i class="sicon-inbox"></i>
                    <span>لا توجد تصنيفات متاحة</span>
                </div>
            `;
        }

        return `
            <div class="categories-grid">
                ${this.categories.map(category => this.renderCategoryItem(category)).join('')}
            </div>
        `;
    }

    renderCategoryItem(category) {
        const categoryName = category.title || category.name || 'بدون اسم';
        const icon = this.getCategoryIcon(categoryName);

        return `
            <div class="category-item" data-category-id="${category.id || ''}" data-category-name="${categoryName}">
                <div class="category-main">
                    <a href="${category.url || '#'}" class="category-link">
                        <div class="category-icon">
                            <i class="${icon}"></i>
                        </div>
                        <div class="category-info">
                            <h3 class="category-name">${categoryName}</h3>
                            ${category.products_count ? `<span class="products-count">${salla.helpers.number(category.products_count)} منتج</span>` : ''}
                        </div>
                        ${category.children?.length ? '<i class="sicon-keyboard_arrow_left sub-arrow"></i>' : ''}
                    </a>

                    <!-- زر عرض المنتجات -->
                    <button class="show-products-btn" data-category-id="${category.id}" aria-label="عرض منتجات ${categoryName}" type="button">
                        <i class="sicon-list-play"></i>
                    </button>
                </div>

                <!-- قائمة المنتجات -->
                <div class="category-products-list" style="display: none;">
                    <div class="products-header">
                        <h4>منتجات ${categoryName}</h4>
                        <button class="close-products-btn" type="button">
                            <i class="sicon-close"></i>
                        </button>
                    </div>
                    <div class="products-content">
                        <div class="products-loading">
                            <i class="sicon-refresh animate-spin"></i>
                            <span>جاري تحميل المنتجات...</span>
                        </div>
                        <div class="products-list" style="display: none;"></div>
                        <div class="products-empty" style="display: none;">
                            <i class="sicon-inbox"></i>
                            <span>لا توجد منتجات في هذا التصنيف</span>
                        </div>
                    </div>
                    <div class="products-footer">
                        <a href="${category.url || '#'}" class="view-all-products">
                            عرض جميع المنتجات
                            <i class="sicon-arrow-left"></i>
                        </a>
                    </div>
                </div>

                ${category.children?.length ? this.renderSubCategories(category.children) : ''}
            </div>
        `;
    }

    renderSubCategories(children) {
        if (!children || !Array.isArray(children)) return '';

        return `
            <div class="sub-categories">
                <div class="sub-categories-header">
                    <span>التصنيفات الفرعية</span>
                </div>
                <div class="sub-categories-list">
                    ${children.slice(0, 6).map(child => {
                        const childName = child.title || child.name || 'بدون اسم';
                        return `
                            <a href="${child.url || '#'}" class="sub-category-link">
                                <i class="${this.getCategoryIcon(childName)}"></i>
                                <span>${childName}</span>
                            </a>
                        `;
                    }).join('')}
                    ${children.length > 6 ? `
                        <a href="#" class="sub-category-more">
                            <i class="sicon-ellipsis-h"></i>
                            <span>عرض المزيد (${children.length - 6})</span>
                        </a>
                    ` : ''}
                </div>
            </div>
        `;
    }

    getCategoryIcon(categoryName) {
        // التأكد من وجود اسم التصنيف
        if (!categoryName || typeof categoryName !== 'string') {
            return 'sicon-tag-price'; // أيقونة افتراضية
        }

        // قاموس الأيقونات بناءً على أسماء التصنيفات الشائعة
        const iconMap = {
            // ملابس
            'ملابس': 'sicon-tshirt',
            'أزياء': 'sicon-tshirt',
            'موضة': 'sicon-tshirt',
            'clothes': 'sicon-tshirt',
            'fashion': 'sicon-tshirt',
            
            // إلكترونيات
            'إلكترونيات': 'sicon-mobile',
            'جوالات': 'sicon-mobile',
            'كمبيوتر': 'sicon-desktop',
            'electronics': 'sicon-mobile',
            'phones': 'sicon-mobile',
            
            // منزل
            'منزل': 'sicon-home',
            'أثاث': 'sicon-sofa',
            'ديكور': 'sicon-home-2',
            'home': 'sicon-home',
            'furniture': 'sicon-sofa',
            
            // رياضة
            'رياضة': 'sicon-dumbbell',
            'لياقة': 'sicon-dumbbell',
            'sports': 'sicon-dumbbell',
            'fitness': 'sicon-dumbbell',
            
            // جمال
            'جمال': 'sicon-beauty',
            'مكياج': 'sicon-beauty',
            'عناية': 'sicon-beauty',
            'beauty': 'sicon-beauty',
            'makeup': 'sicon-beauty',
            
            // طعام
            'طعام': 'sicon-restaurant',
            'مأكولات': 'sicon-restaurant',
            'food': 'sicon-restaurant',
            'restaurant': 'sicon-restaurant',
            
            // كتب
            'كتب': 'sicon-book-open',
            'تعليم': 'sicon-book-open',
            'books': 'sicon-book-open',
            'education': 'sicon-book-open',
            
            // ألعاب
            'ألعاب': 'sicon-game-controller-alt',
            'العاب': 'sicon-game-controller-alt',
            'اكشن': 'sicon-game-controller-alt',
            'العاب اكشن': 'sicon-game-controller-alt',
            'games': 'sicon-game-controller-alt',
            'action': 'sicon-game-controller-alt',
            'toys': 'sicon-game-controller-alt',
            
            // سيارات
            'سيارات': 'sicon-car',
            'مركبات': 'sicon-car',
            'cars': 'sicon-car',
            'automotive': 'sicon-car'
        };

        // البحث عن أيقونة مناسبة
        const lowerName = categoryName.toLowerCase();
        for (const [key, icon] of Object.entries(iconMap)) {
            if (lowerName.includes(key.toLowerCase())) {
                return icon;
            }
        }

        // أيقونة افتراضية
        return 'sicon-tag-price';
    }

    attachEvents() {
        const trigger = this.querySelector('.categories-trigger-btn');
        const backdrop = this.querySelector('.categories-backdrop');
        const dropdown = this.querySelector('.categories-dropdown-menu');

        // فتح/إغلاق القائمة
        trigger?.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggle();
        });

        backdrop?.addEventListener('click', () => {
            this.close();
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });

        dropdown?.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        document.addEventListener('click', (e) => {
            if (this.isOpen && !this.contains(e.target)) {
                this.close();
            }
        });

        this.addEventListener('click', (e) => {
            const categoryLink = e.target.closest('.category-link');
            if (categoryLink) {
                const categoryItem = categoryLink.closest('.category-item');
                const categoryId = categoryItem?.dataset.categoryId;
                const categoryName = categoryLink.querySelector('.category-name')?.textContent;

                console.log('تم النقر على التصنيف:', { id: categoryId, name: categoryName });

                this.close();

            }
        });

        this.attachProductsEvents();
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        this.isOpen = true;
        this.updateUI();

        document.body.classList.add('categories-dropdown-open');

        const trigger = this.querySelector('.categories-trigger-btn');
        trigger?.setAttribute('aria-expanded', 'true');
    }

    close() {
        this.isOpen = false;
        this.updateUI();

        document.body.classList.remove('categories-dropdown-open');

        const trigger = this.querySelector('.categories-trigger-btn');
        trigger?.setAttribute('aria-expanded', 'false');
    }

    updateUI() {
        const dropdown = this.querySelector('.categories-dropdown-menu');
        const backdrop = this.querySelector('.categories-backdrop');
        const arrow = this.querySelector('.categories-arrow');

        if (dropdown) {
            dropdown.classList.toggle('show', this.isOpen);
        }

        if (backdrop) {
            backdrop.classList.toggle('show', this.isOpen);
        }

        if (arrow) {
            arrow.classList.toggle('rotated', this.isOpen);
        }
    }

    async refresh() {
        await this.loadCategories();
        this.render();
        this.attachEvents();
    }

    attachProductsEvents() {
        console.log('ربط أحداث المنتجات...');

        this.addEventListener('click', (e) => {
            console.log('تم النقر في المكون:', e.target);

            const showBtn = e.target.closest('.show-products-btn');
            const closeBtn = e.target.closest('.close-products-btn');

            console.log('زر عرض المنتجات:', showBtn);
            console.log('زر إغلاق المنتجات:', closeBtn);

            if (showBtn) {
                console.log('✅ تم العثور على زر عرض المنتجات');
                e.preventDefault();
                e.stopPropagation();
                const categoryId = showBtn.dataset.categoryId;
                const categoryItem = showBtn.closest('.category-item');
                console.log('معرف التصنيف:', categoryId);
                console.log('عنصر التصنيف:', categoryItem);
                this.showCategoryProducts(categoryId, categoryItem);
            }

            if (closeBtn) {
                console.log('✅ تم العثور على زر إغلاق المنتجات');
                e.preventDefault();
                e.stopPropagation();
                const categoryItem = closeBtn.closest('.category-item');
                this.hideCategoryProducts(categoryItem);
            }
        });

        setTimeout(() => {
            const buttons = this.querySelectorAll('.show-products-btn');
            console.log('عدد أزرار المنتجات الموجودة:', buttons.length);

            buttons.forEach((btn, index) => {
                console.log(`الزر ${index + 1}:`, btn);
                console.log(`معرف التصنيف للزر ${index + 1}:`, btn.dataset.categoryId);

                btn.addEventListener('click', (e) => {
                    console.log('تم النقر على الزر مباشرة!', btn);
                    e.preventDefault();
                    e.stopPropagation();
                    const categoryId = btn.dataset.categoryId;
                    const categoryItem = btn.closest('.category-item');
                    this.showCategoryProducts(categoryId, categoryItem);
                });
            });
        }, 1000);
    }

    async showCategoryProducts(categoryId, categoryItem) {
        console.log('🚀 بدء عرض منتجات التصنيف:', categoryId);
        console.log('📦 عنصر التصنيف:', categoryItem);

        const productsList = categoryItem.querySelector('.category-products-list');
        const productsListContent = categoryItem.querySelector('.products-list');
        const productsLoading = categoryItem.querySelector('.products-loading');
        const productsEmpty = categoryItem.querySelector('.products-empty');

        console.log('🔍 العناصر الموجودة:', {
            productsList: !!productsList,
            productsListContent: !!productsListContent,
            productsLoading: !!productsLoading,
            productsEmpty: !!productsEmpty
        });

        if (!productsList) {
            console.error('❌ لم يتم العثور على قائمة المنتجات');
            return;
        }

        console.log('✅ إظهار قائمة المنتجات');
        productsList.style.cssText = `
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            z-index: 9999 !important;
            background: white !important;
            border: 3px solid red !important;
            border-radius: 12px !important;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5) !important;
            width: 350px !important;
            max-height: 400px !important;
            padding: 20px !important;
        `;

        console.log('📍 أنماط قائمة المنتجات بعد التطبيق:', {
            display: getComputedStyle(productsList).display,
            visibility: getComputedStyle(productsList).visibility,
            opacity: getComputedStyle(productsList).opacity,
            position: getComputedStyle(productsList).position,
            zIndex: getComputedStyle(productsList).zIndex,
            width: getComputedStyle(productsList).width,
            height: getComputedStyle(productsList).height
        });

        if (productsLoading) {
            productsLoading.style.display = 'flex';
            console.log('⏳ إظهار حالة التحميل');
        }
        if (productsListContent) {
            productsListContent.style.display = 'none';
        }
        if (productsEmpty) {
            productsEmpty.style.display = 'none';
        }

        try {
            console.log('📡 جلب منتجات التصنيف...');

            const response = await salla.api.product.fetch({
                source: "categories",
                source_value: [categoryId.toString()],
                per_page: 10 // عرض 10 منتجات فقط
            });

            console.log('📦 استجابة API:', response);
            console.log('📋 بيانات المنتجات:', response.data);

            const products = response.data?.data || response.data || [];
            console.log('🛍️ المنتجات المستخرجة:', products);
            console.log('📊 عدد المنتجات:', products.length);

            if (productsLoading) {
                productsLoading.style.display = 'none';
                console.log('✅ إخفاء حالة التحميل');
            }

            if (products.length > 0) {
                console.log('✅ عرض المنتجات');
                const productsHTML = `
                    <div style="padding: 20px; background: white;">
                        <h3 style="margin: 0 0 15px 0; color: #333;">منتجات التصنيف:</h3>
                        ${products.map(product => `
                            <div style="padding: 10px; border: 1px solid #ddd; margin: 5px 0; border-radius: 5px;">
                                <h4 style="margin: 0 0 5px 0; color: #333;">${product.name}</h4>
                                <p style="margin: 0; color: #666; font-weight: bold;">${salla.money(product.price)}</p>
                                <a href="${product.url}" style="color: #007cba; text-decoration: none;">عرض المنتج</a>
                            </div>
                        `).join('')}
                        <button onclick="this.parentElement.parentElement.style.display='none'"
                                style="margin-top: 10px; padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">
                            إغلاق
                        </button>
                    </div>
                `;

                productsList.innerHTML = productsHTML;
                console.log('✅ تم عرض المنتجات مباشرة في القائمة');
            } else {
                console.log('⚠️ لا توجد منتجات');
                if (productsEmpty) {
                    productsEmpty.style.display = 'flex';
                }
            }

        } catch (error) {
            console.error('❌ خطأ في جلب المنتجات:', error);
            if (productsLoading) {
                productsLoading.style.display = 'none';
            }
            if (productsEmpty) {
                const span = productsEmpty.querySelector('span');
                if (span) {
                    span.textContent = 'خطأ في تحميل المنتجات';
                }
                productsEmpty.style.display = 'flex';
            }
        }
    }

    hideCategoryProducts(categoryItem) {
        const productsList = categoryItem.querySelector('.category-products-list');
        if (productsList) {
            productsList.style.display = 'none';
        }
    }

    renderProductItem(product) {
        return `
            <div class="product-item">
                <a href="${product.url}" class="product-link">
                    <div class="product-info">
                        <h5 class="product-name">${product.name}</h5>
                        <div class="product-price">
                            ${product.is_on_sale ? `
                                <span class="sale-price">${salla.money(product.sale_price)}</span>
                                <span class="regular-price">${salla.money(product.regular_price)}</span>
                            ` : `
                                <span class="price">${salla.money(product.price)}</span>
                            `}
                        </div>
                    </div>
                </a>
            </div>
        `;
    }
}

customElements.define('custom-categories-dropdown', CategoriesDropdown);
