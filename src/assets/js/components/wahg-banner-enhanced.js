/**
 * Enhanced Wahg Banner Component - Theme Settings Integration
 * Handles customizable banner based on theme settings from twilight.json
 */
(function() {
    'use strict';

    // Default settings
    const DEFAULT_SETTINGS = {
        full_width: false,
        banner_height: 400,
        border_radius: 12,
        enable_hover_effects: true,
        hover_overlay_color: '#000000',
        hover_overlay_opacity: 20,
        animation_type: 'zoom',
        animation_duration: 'normal',
        enable_shadow: true,
        shadow_color: '#000000',
        shadow_intensity: 10,
        enable_border: false,
        border_color: '#e5e7eb',
        mobile_height: 250,
        hide_on_mobile: false,
        lazy_loading: true,
        image_optimization: true
    };

    let isInitialized = false;

    /**
     * Initialize wahg banner components
     */
    function initWahgBannerComponents() {
        if (isInitialized) return;

        const banners = document.querySelectorAll('[data-component="wahg-banner"]');
        if (banners.length === 0) return;

        console.log('🎯 Initializing Wahg Banner Enhanced:', banners.length, 'banner(s) found');

        // Check browser support for enhanced features
        const supportsWillChange = CSS.supports('will-change', 'transform');
        const supportsBackfaceVisibility = CSS.supports('backface-visibility', 'hidden');

        // Setup intersection observer for lazy loading and performance
        const observerOptions = {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
        };

        const bannerObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const banner = entry.target;
                const bannerElement = banner.querySelector('.wahg-banner');
                const imageElement = banner.querySelector('.wahg-banner__image');

                if (entry.isIntersecting) {
                    // Enable animations when element is visible
                    if (bannerElement) {
                        bannerElement.style.animationPlayState = 'running';
                    }

                    // Handle lazy loading
                    if (imageElement && imageElement.loading === 'lazy') {
                        imageElement.addEventListener('load', () => {
                            imageElement.classList.add('loaded');
                        });
                    }

                    banner.classList.add('banner-visible');
                } else {
                    // Pause animations when element is not visible
                    if (bannerElement) {
                        bannerElement.style.animationPlayState = 'paused';
                    }

                    banner.classList.remove('banner-visible');
                }
            });
        }, observerOptions);

        // Process each banner
        banners.forEach(banner => {
            applyThemeSettings(banner);
            bannerObserver.observe(banner);

            // Performance optimizations
            if (supportsWillChange) {
                const bannerElement = banner.querySelector('.wahg-banner');
                const imageElement = banner.querySelector('.wahg-banner__image');

                if (bannerElement) {
                    bannerElement.style.willChange = 'transform, filter';
                }
                if (imageElement) {
                    imageElement.style.willChange = 'transform';
                }
            }

            if (supportsBackfaceVisibility) {
                const allElements = banner.querySelectorAll('*');
                allElements.forEach(el => {
                    el.style.backfaceVisibility = 'hidden';
                });
            }
        });

        // Cleanup observer on page unload
        window.addEventListener('beforeunload', () => {
            bannerObserver.disconnect();
        });

        isInitialized = true;

        // Log initialization (development only)
        console.log('✅ Enhanced Wahg Banner initialized:', banners.length, 'banner(s) processed');
    }

    /**
     * Apply theme settings to a banner element
     */
    function applyThemeSettings(banner) {
        console.log('🔧 Applying theme settings to banner element');
        
        const settings = getThemeSettings(banner);
        
        console.log('📋 Final settings to apply:', settings);
        
        // Apply CSS custom properties for dynamic styling
        banner.style.setProperty('--banner-height', `${settings.banner_height}px`);
        banner.style.setProperty('--border-radius', `${settings.border_radius}px`);
        banner.style.setProperty('--hover-overlay-color', settings.hover_overlay_color);
        banner.style.setProperty('--hover-overlay-opacity', settings.hover_overlay_opacity / 100);
        banner.style.setProperty('--shadow-color', hexToRgba(settings.shadow_color, 0.15));
        banner.style.setProperty('--shadow-intensity', `${settings.shadow_intensity}px`);
        banner.style.setProperty('--border-color', settings.border_color);
        banner.style.setProperty('--mobile-height', `${settings.mobile_height}px`);

        const bannerElement = banner.querySelector('.wahg-banner');
        const wrapperElement = banner.querySelector('.wahg-banner-wrapper');

        console.log('🔍 Found banner element:', !!bannerElement);
        console.log('🔍 Found wrapper element:', !!wrapperElement);

        if (bannerElement) {
            // Clear existing animation classes
            bannerElement.className = bannerElement.className.replace(/animation-type-\w+/g, '');
            bannerElement.className = bannerElement.className.replace(/animation-duration-\w+/g, '');
            
            // Apply animation classes
            bannerElement.classList.add(`animation-type-${settings.animation_type}`);
            bannerElement.classList.add(`animation-duration-${settings.animation_duration}`);
            
            // Apply hover effects class
            if (settings.enable_hover_effects) {
                bannerElement.classList.add('wahg-banner--hover-enabled');
            } else {
                bannerElement.classList.remove('wahg-banner--hover-enabled');
            }

            // Apply shadow settings - Simple direct approach
            console.log('🔧 Applying shadow settings:', settings.enable_shadow, settings.shadow_color, settings.shadow_intensity);

            if (settings.enable_shadow) {
                const shadowColor = hexToRgba(settings.shadow_color, 0.2);
                const shadowStyle = `0 4px ${settings.shadow_intensity}px ${shadowColor}`;

                // Apply shadow directly with inline style (most reliable)
                bannerElement.style.boxShadow = shadowStyle;
                bannerElement.style.setProperty('box-shadow', shadowStyle, 'important');

                console.log('✅ Applied shadow:', shadowStyle);
            } else {
                bannerElement.style.boxShadow = 'none';
                bannerElement.style.setProperty('box-shadow', 'none', 'important');

                console.log('❌ Removed shadow');
            }

            // Apply border settings - Simple direct approach
            if (settings.enable_border) {
                const borderStyle = `2px solid ${settings.border_color}`;
                bannerElement.style.border = borderStyle;
                bannerElement.style.setProperty('border', borderStyle, 'important');

                console.log('✅ Applied border:', borderStyle);
            } else {
                bannerElement.style.border = 'none';
                bannerElement.style.setProperty('border', 'none', 'important');

                console.log('❌ Removed border');
            }

            console.log('🎨 Applied animation classes:', settings.animation_type, settings.animation_duration);
            console.log('🌟 Applied shadow:', {
                enabled: settings.enable_shadow,
                intensity: settings.shadow_intensity,
                color: settings.shadow_color,
                computed_shadow: getComputedStyle(bannerElement).boxShadow,
                inline_shadow: bannerElement.style.boxShadow,
                classes: bannerElement.className
            });
        }

        // Handle full-width setting - Simple approach
        if (settings.full_width) {
            banner.classList.add('s-block--full-width');
            banner.classList.remove('container');
            banner.setAttribute('data-full-width', 'true');

            console.log('🔄 Applied full-width class to banner');
        } else {
            banner.classList.remove('s-block--full-width');
            banner.setAttribute('data-full-width', 'false');
            if (!banner.classList.contains('container')) {
                banner.classList.add('container');
            }

            console.log('🔄 Removed full-width class from banner');
        }

        // Handle mobile visibility
        if (settings.hide_on_mobile) {
            banner.classList.add('wahg-banner--hide-mobile');
        } else {
            banner.classList.remove('wahg-banner--hide-mobile');
        }

        console.log('✅ Settings applied successfully to banner:', {
            full_width: settings.full_width,
            height: settings.banner_height,
            hover_effects: settings.enable_hover_effects,
            animation: settings.animation_type,
            duration: settings.animation_duration,
            shadow: settings.enable_shadow,
            border: settings.enable_border,
            classes: banner.className,
            data_full_width: banner.getAttribute('data-full-width'),
            computed_width: getComputedStyle(banner).width,
            computed_transform: getComputedStyle(banner).transform,
            computed_margin_left: getComputedStyle(banner).marginLeft
        });

        // Additional debug for full-width
        if (settings.full_width) {
            console.log('🔍 Full-width debug info:', {
                has_full_width_class: banner.classList.contains('s-block--full-width'),
                has_container_class: banner.classList.contains('container'),
                data_attribute: banner.dataset.fullWidth,
                viewport_width: window.innerWidth + 'px',
                element_width: banner.offsetWidth + 'px'
            });
        }
    }

    /**
     * Get theme settings from data attributes or defaults
     */
    function getThemeSettings(banner) {
        const settings = { ...DEFAULT_SETTINGS };

        try {
            // Get settings from data attributes (primary method)
            if (banner && banner.dataset) {
                const dataset = banner.dataset;
                
                // Read each setting from data attributes
                if (dataset.fullWidth) settings.full_width = dataset.fullWidth === 'true';
                if (dataset.bannerHeight) settings.banner_height = parseInt(dataset.bannerHeight);
                if (dataset.borderRadius) settings.border_radius = parseInt(dataset.borderRadius);
                if (dataset.enableHoverEffects) settings.enable_hover_effects = dataset.enableHoverEffects === 'true';
                if (dataset.hoverOverlayColor) settings.hover_overlay_color = dataset.hoverOverlayColor;
                if (dataset.hoverOverlayOpacity) settings.hover_overlay_opacity = parseInt(dataset.hoverOverlayOpacity);
                if (dataset.animationType) settings.animation_type = dataset.animationType;
                if (dataset.animationDuration) settings.animation_duration = dataset.animationDuration;
                if (dataset.enableShadow) settings.enable_shadow = dataset.enableShadow === 'true';
                if (dataset.shadowColor) settings.shadow_color = dataset.shadowColor;
                if (dataset.shadowIntensity) settings.shadow_intensity = parseInt(dataset.shadowIntensity);
                if (dataset.enableBorder) settings.enable_border = dataset.enableBorder === 'true';
                if (dataset.borderColor) settings.border_color = dataset.borderColor;
                if (dataset.mobileHeight) settings.mobile_height = parseInt(dataset.mobileHeight);
                if (dataset.hideOnMobile) settings.hide_on_mobile = dataset.hideOnMobile === 'true';
                if (dataset.lazyLoading) settings.lazy_loading = dataset.lazyLoading === 'true';
                if (dataset.imageOptimization) settings.image_optimization = dataset.imageOptimization === 'true';
                
                console.log('📊 Wahg Banner settings loaded from data attributes:', settings);
            }
            // Fallback: Try to get settings from Salla theme system
            else if (typeof salla !== 'undefined' && salla.config && salla.config.get) {
                const componentSettings = salla.config.get('components.WahgBanner') || {};
                
                Object.keys(DEFAULT_SETTINGS).forEach(key => {
                    if (componentSettings[key] !== undefined) {
                        settings[key] = componentSettings[key];
                    }
                });
                
                console.log('📊 Wahg Banner settings loaded from salla.config:', settings);
            }
            else {
                console.log('📊 Wahg Banner settings: Using defaults');
            }
        } catch (error) {
            console.warn('⚠️ Could not load Wahg Banner theme settings:', error);
        }

        return settings;
    }

    /**
     * Convert hex color to rgba
     */
    function hexToRgba(hex, alpha = 1) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        if (!result) return `rgba(0, 0, 0, ${alpha})`;

        const r = parseInt(result[1], 16);
        const g = parseInt(result[2], 16);
        const b = parseInt(result[3], 16);

        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    /**
     * Refresh all wahg banner components with new settings
     */
    function refreshWahgBannerComponents() {
        const banners = document.querySelectorAll('[data-component="wahg-banner"]');
        banners.forEach(banner => {
            applyThemeSettings(banner);
        });
        console.log('🔄 Wahg Banner components refreshed');
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initWahgBannerComponents);
    } else {
        initWahgBannerComponents();
    }

    // Listen for theme changes
    document.addEventListener('salla:theme:updated', refreshWahgBannerComponents);

    // Expose refresh function globally for theme customizer
    window.refreshWahgBannerComponents = refreshWahgBannerComponents;

    // Add body class to indicate component is loaded
    document.body.classList.add('wahg-banner-enhanced');
    
    // Dispatch event to indicate component is loaded
    document.dispatchEvent(new CustomEvent('wahg-banner-enhanced-loaded'));

})();
