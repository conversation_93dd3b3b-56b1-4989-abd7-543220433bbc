/**
 * إعدادات حجم اللوجو في شريط التنقل
 * Navbar Logo Size Settings
 */

(function() {
    'use strict';

    /**
     * إزالة أي inline styles مطبقة مسبقاً على اللوجو
     * Remove any previously applied inline styles from logo
     */
    function clearLogoInlineStyles() {
        const logos = document.querySelectorAll('.navbar-brand img');

        logos.forEach(logo => {
            // إزالة جميع inline styles المتعلقة بالحجم
            logo.style.removeProperty('max-width');
            logo.style.removeProperty('max-height');
            logo.style.removeProperty('width');
            logo.style.removeProperty('height');

            // إبقاء transition فقط
            logo.setAttribute('style', 'transition: all 0.3s ease !important;');
        });

        console.log(`تم مسح inline styles من ${logos.length} صورة لوجو`);
    }

    /**
     * تطبيق حجم اللوجو المحدد من الإعدادات
     * Apply the selected logo size from settings
     */
    function applyNavbarLogoSize() {
        // محاولة الحصول على قيمة الإعداد من مصادر مختلفة
        let logoSize = 'medium'; // القيمة الافتراضية

        // محاولة الحصول على القيمة من salla.config
        if (typeof salla !== 'undefined' && salla.config && salla.config.get) {
            logoSize = salla.config.get('navbar_logo_size', 'medium');
        }
        // محاولة الحصول على القيمة من window
        else if (typeof window.navbar_logo_size !== 'undefined') {
            logoSize = window.navbar_logo_size;
        }
        // محاولة الحصول على القيمة من data attribute
        else {
            const bodyElement = document.body;
            if (bodyElement && bodyElement.dataset.navbarLogoSize) {
                logoSize = bodyElement.dataset.navbarLogoSize;
            }
        }

        // إزالة جميع كلاسات الحجم السابقة
        const body = document.body;
        const logoSizeClasses = [
            'navbar-logo-small',
            'navbar-logo-medium',
            'navbar-logo-large',
            'navbar-logo-extra-large'
        ];

        logoSizeClasses.forEach(className => {
            body.classList.remove(className);
        });

        // إضافة الكلاس المناسب للحجم المحدد
        const sizeClassMap = {
            'small': 'navbar-logo-small',
            'medium': 'navbar-logo-medium',
            'large': 'navbar-logo-large',
            'extra-large': 'navbar-logo-extra-large'
        };

        const targetClass = sizeClassMap[logoSize] || 'navbar-logo-medium';
        body.classList.add(targetClass);

        // مسح أي inline styles مطبقة مسبقاً
        clearLogoInlineStyles();

        console.log(`تم تطبيق حجم اللوجو: ${logoSize} (${targetClass})`);

        return logoSize;
    }

    /**
     * تهيئة الإعدادات عند تحميل الصفحة
     * Initialize settings on page load
     */
    function initNavbarLogoSettings() {
        console.log('بدء تهيئة إعدادات حجم اللوجو...');

        // تطبيق الحجم فور التحميل
        const appliedSize = applyNavbarLogoSize();
        console.log('تم تطبيق الحجم:', appliedSize);

        // إعادة تطبيق الحجم كل ثانية للتأكد (للاختبار)
        const intervalId = setInterval(() => {
            const logos = document.querySelectorAll('.navbar-brand img');
            if (logos.length > 0) {
                applyNavbarLogoSize();
                // إيقاف المراقبة بعد 10 ثوان
                setTimeout(() => clearInterval(intervalId), 10000);
            }
        }, 1000);

        // مراقبة تغييرات حجم الشاشة
        window.addEventListener('resize', function() {
            setTimeout(applyNavbarLogoSize, 100);
        });

        // مراقبة تغييرات الإعدادات (في حالة التحديث المباشر)
        if (typeof salla !== 'undefined' && salla.event) {
            salla.event.on('settings.updated', function(data) {
                console.log('تم تحديث الإعدادات:', data);
                if (data.key === 'navbar_logo_size') {
                    applyNavbarLogoSize();
                }
            });
        }

        // إضافة مراقب للتغييرات في data attributes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' &&
                    mutation.attributeName === 'data-navbar-logo-size') {
                    console.log('تم اكتشاف تغيير في data attribute');
                    applyNavbarLogoSize();
                }
            });
        });

        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['data-navbar-logo-size']
        });

        // مراقب لإضافة عناصر جديدة (في حالة تحميل ديناميكي)
        const logoObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    const addedNodes = Array.from(mutation.addedNodes);
                    addedNodes.forEach(node => {
                        if (node.nodeType === 1) { // Element node
                            const logos = node.querySelectorAll ? node.querySelectorAll('.navbar-brand img') : [];
                            if (logos.length > 0 || (node.matches && node.matches('.navbar-brand img'))) {
                                console.log('تم اكتشاف إضافة لوجو جديد');
                                setTimeout(applyNavbarLogoSize, 100);
                            }
                        }
                    });
                }
            });
        });

        logoObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * تشغيل الإعدادات عند جاهزية DOM
     * Run settings when DOM is ready
     */
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initNavbarLogoSettings);
    } else {
        initNavbarLogoSettings();
    }

    /**
     * تشغيل الإعدادات عند جاهزية Salla
     * Run settings when Salla is ready
     */
    if (typeof salla !== 'undefined' && salla.onReady) {
        salla.onReady(function() {
            console.log('Salla جاهز - تهيئة إعدادات اللوجو');
            initNavbarLogoSettings();
        });
    } else {
        // في حالة عدم وجود salla، تشغيل الإعدادات مباشرة
        setTimeout(initNavbarLogoSettings, 100);
    }

    /**
     * إضافة دعم للتحديث المباشر في معاينة الثيم
     * Add support for live preview updates
     */
    window.updateNavbarLogoSize = function(size) {
        console.log('تحديث حجم اللوجو من الخارج:', size);
        const body = document.body;
        const logoSizeClasses = [
            'navbar-logo-small',
            'navbar-logo-medium',
            'navbar-logo-large',
            'navbar-logo-extra-large'
        ];

        logoSizeClasses.forEach(className => {
            body.classList.remove(className);
        });

        const sizeClassMap = {
            'small': 'navbar-logo-small',
            'medium': 'navbar-logo-medium',
            'large': 'navbar-logo-large',
            'extra-large': 'navbar-logo-extra-large'
        };

        const targetClass = sizeClassMap[size] || 'navbar-logo-medium';
        body.classList.add(targetClass);
        console.log('تم تطبيق الكلاس:', targetClass);
    };

    // تصدير الوظائف للاستخدام العام
    window.navbarLogoSettings = {
        apply: applyNavbarLogoSize,
        update: window.updateNavbarLogoSize,
        init: initNavbarLogoSettings
    };

    console.log('تم تحميل إعدادات حجم اللوجو بنجاح');

})();
