/**
 * Special Title Component - Performance Optimized
 * Handles lazy loading of animations and intersection observer
 */

class SpecialTitleComponent {
    constructor() {
        this.components = document.querySelectorAll('.special-title-component');
        this.observer = null;
        this.init();
    }

    init() {
        if (this.components.length === 0) return;

        // Check if user prefers reduced motion
        this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        
        if (!this.prefersReducedMotion) {
            this.setupIntersectionObserver();
        }

        this.bindEvents();
    }

    setupIntersectionObserver() {
        // Only animate when component is in viewport
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.activateAnimations(entry.target);
                    // Stop observing once activated
                    this.observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.3,
            rootMargin: '50px'
        });

        this.components.forEach(component => {
            this.observer.observe(component);
        });
    }

    activateAnimations(component) {
        // Add animation class to trigger CSS animations
        component.classList.add('animate-active');
        
        // Stagger particle animations for better performance
        const particles = component.querySelectorAll('.particle');
        particles.forEach((particle, index) => {
            setTimeout(() => {
                particle.classList.add('animate');
            }, index * 100);
        });

        // Activate circuit animations
        const circuitElements = component.querySelectorAll('.circuit-line, .circuit-dot');
        circuitElements.forEach((element, index) => {
            setTimeout(() => {
                element.classList.add('animate');
            }, 200 + (index * 150));
        });
    }

    bindEvents() {
        this.components.forEach(component => {
            const titleText = component.querySelector('.special-title-text');
            
            if (titleText) {
                // Optimize hover effects with requestAnimationFrame
                let hoverTimeout;
                
                titleText.addEventListener('mouseenter', () => {
                    clearTimeout(hoverTimeout);
                    if (!this.prefersReducedMotion) {
                        requestAnimationFrame(() => {
                            titleText.classList.add('hover-active');
                        });
                    }
                });

                titleText.addEventListener('mouseleave', () => {
                    hoverTimeout = setTimeout(() => {
                        titleText.classList.remove('hover-active');
                    }, 100);
                });
            }
        });
    }

    // Method to pause animations (useful for performance)
    pauseAnimations() {
        this.components.forEach(component => {
            component.style.animationPlayState = 'paused';
            const animatedElements = component.querySelectorAll('[class*="animate"]');
            animatedElements.forEach(el => {
                el.style.animationPlayState = 'paused';
            });
        });
    }

    // Method to resume animations
    resumeAnimations() {
        this.components.forEach(component => {
            component.style.animationPlayState = 'running';
            const animatedElements = component.querySelectorAll('[class*="animate"]');
            animatedElements.forEach(el => {
                el.style.animationPlayState = 'running';
            });
        });
    }

    // Cleanup method
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.specialTitleComponent = new SpecialTitleComponent();
});

// Pause animations when page is hidden (performance optimization)
document.addEventListener('visibilitychange', () => {
    if (window.specialTitleComponent) {
        if (document.hidden) {
            window.specialTitleComponent.pauseAnimations();
        } else {
            window.specialTitleComponent.resumeAnimations();
        }
    }
});

export default SpecialTitleComponent;
