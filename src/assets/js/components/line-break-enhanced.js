/**
 * Enhanced Line Break Component - Theme Settings Integration
 * Handles customizable line break divider based on theme settings from twilight.json
 */
(function() {
    'use strict';

    // Default settings
    const DEFAULT_SETTINGS = {
        line_style: 'gaming',
        line_color: '#1de9b6',
        line_secondary_color: '#4cc9f0',
        line_thickness: 2,
        margin_top: 30,
        margin_bottom: 30,
        enable_glow: true,
        enable_shine: true,
        enable_particles: true,
        animation_speed: 'normal'
    };

    let isInitialized = false;

    /**
     * Initialize line break components
     */
    function initLineBreakComponents() {
        if (isInitialized) return;

        const dividers = document.querySelectorAll('.gaming-advanced-divider');
        if (dividers.length === 0) return;

        // Check browser support for enhanced features
        const supportsWillChange = CSS.supports('will-change', 'transform');
        const supportsBackfaceVisibility = CSS.supports('backface-visibility', 'hidden');

        // Setup intersection observer for performance optimization
        const observerOptions = {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
        };

        const dividerObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const divider = entry.target;
                const glowElement = divider.querySelector('.divider-glow');
                const particlesElement = divider.querySelector('.divider-particles');

                if (entry.isIntersecting) {
                    // Enable animations when element is visible
                    if (glowElement) {
                        glowElement.style.animationPlayState = 'running';
                    }
                    if (particlesElement) {
                        particlesElement.style.animationPlayState = 'running';
                    }

                    divider.classList.add('divider-visible');
                } else {
                    // Pause animations when element is not visible
                    if (glowElement) {
                        glowElement.style.animationPlayState = 'paused';
                    }
                    if (particlesElement) {
                        particlesElement.style.animationPlayState = 'paused';
                    }

                    divider.classList.remove('divider-visible');
                }
            });
        }, observerOptions);

        // Process each divider
        dividers.forEach(divider => {
            applyThemeSettings(divider);
            dividerObserver.observe(divider);

            // Performance optimizations
            if (supportsWillChange) {
                const glowElement = divider.querySelector('.divider-glow');
                const lineElement = divider.querySelector('.divider-line');

                if (glowElement) {
                    glowElement.style.willChange = 'transform, opacity, filter';
                }
                if (lineElement) {
                    lineElement.style.willChange = 'transform';
                }
            }

            if (supportsBackfaceVisibility) {
                const allElements = divider.querySelectorAll('*');
                allElements.forEach(el => {
                    el.style.backfaceVisibility = 'hidden';
                });
            }
        });

        // Cleanup observer on page unload
        window.addEventListener('beforeunload', () => {
            dividerObserver.disconnect();
        });

        isInitialized = true;

        // Log initialization (development only)
        if (typeof console !== 'undefined' && console.log) {
            console.log(`Enhanced line break initialized: ${dividers.length} divider(s) found`);
        }
    }

    /**
     * Apply theme settings to a divider element
     */
    function applyThemeSettings(divider) {
        const settings = getThemeSettings(divider);

        console.log('Applying settings to divider:', settings);

        // Apply CSS custom properties for dynamic styling
        divider.style.setProperty('--line-color', settings.line_color);
        divider.style.setProperty('--line-secondary-color', settings.line_secondary_color);
        divider.style.setProperty('--line-thickness', `${settings.line_thickness}px`);
        divider.style.marginTop = `${settings.margin_top}px`;
        divider.style.marginBottom = `${settings.margin_bottom}px`;

        const lineElement = divider.querySelector('.divider-line');
        const glowElement = divider.querySelector('.divider-glow');
        const particlesElement = divider.querySelector('.divider-particles');

        if (lineElement) {
            // Clear existing style classes
            lineElement.className = lineElement.className.replace(/style-\w+/g, '');
            lineElement.className = lineElement.className.replace(/enable-shine/g, '');

            // Apply line style
            lineElement.classList.add(`style-${settings.line_style}`);

            // Apply shine effect
            if (settings.enable_shine) {
                lineElement.classList.add('enable-shine');
            }

            // Reset inline styles to let CSS classes take effect
            lineElement.style.height = '';
            lineElement.style.background = '';
            lineElement.style.border = '';
            lineElement.style.borderTop = '';
            lineElement.style.borderBottom = '';

            // Apply thickness and style-specific properties
            if (settings.line_style === 'gaming') {
                lineElement.style.height = `${settings.line_thickness}px`;
                updateGamingGradient(lineElement, settings);
            }

            console.log('Applied line style:', settings.line_style, 'to element:', lineElement);
        }

        // Handle glow element
        if (glowElement) {
            if (settings.enable_glow && settings.line_style === 'gaming') {
                glowElement.style.display = 'block';
                updateGlowColors(glowElement, settings);
            } else {
                glowElement.style.display = 'none';
            }
        }

        // Handle particles element
        if (particlesElement) {
            if (settings.enable_particles && settings.line_style === 'gaming') {
                particlesElement.style.display = 'block';
                updateParticleColors(particlesElement, settings);
            } else {
                particlesElement.style.display = 'none';
            }
        }

        // Apply animation speed class to divider
        divider.className = divider.className.replace(/animation-\w+/g, '');
        divider.classList.add(`animation-${settings.animation_speed}`);

        console.log('Settings applied successfully to divider:', {
            style: settings.line_style,
            color: settings.line_color,
            thickness: settings.line_thickness,
            glow: settings.enable_glow,
            shine: settings.enable_shine,
            particles: settings.enable_particles,
            speed: settings.animation_speed
        });

        // Apply animation speed class
        divider.className = divider.className.replace(/animation-\w+/g, '');
        divider.classList.add(`animation-${settings.animation_speed}`);
    }

    /**
     * Update gaming gradient colors
     */
    function updateGamingGradient(element, settings) {
        const primaryColor = hexToRgba(settings.line_color, 0.8);
        const primaryColorLight = hexToRgba(settings.line_color, 0.3);
        
        const gradient = `linear-gradient(90deg,
            transparent 0%,
            ${primaryColorLight} 20%,
            ${primaryColor} 50%,
            ${primaryColorLight} 80%,
            transparent 100%)`;
        
        element.style.background = gradient;
    }

    /**
     * Update glow element colors
     */
    function updateGlowColors(element, settings) {
        const primaryColor = hexToRgba(settings.line_color, 0.8);
        const primaryColorMid = hexToRgba(settings.line_color, 0.4);
        
        const gradient = `radial-gradient(ellipse,
            ${primaryColor} 0%,
            ${primaryColorMid} 40%,
            transparent 70%)`;
        
        element.style.background = gradient;
    }

    /**
     * Update particle colors
     */
    function updateParticleColors(element, settings) {
        const primaryColor = hexToRgba(settings.line_color, 0.3);
        const secondaryColor = hexToRgba(settings.line_secondary_color, 0.2);
        
        const backgroundImage = `
            radial-gradient(circle at 20%, ${primaryColor} 1px, transparent 1px),
            radial-gradient(circle at 40%, ${secondaryColor} 1px, transparent 1px),
            radial-gradient(circle at 60%, ${primaryColor} 1px, transparent 1px),
            radial-gradient(circle at 80%, ${secondaryColor} 1px, transparent 1px)`;
        
        element.style.backgroundImage = backgroundImage;
    }

    /**
     * Get theme settings from data attributes or defaults
     */
    function getThemeSettings(divider) {
        const settings = { ...DEFAULT_SETTINGS };

        try {
            // Get settings from data attributes (primary method)
            if (divider && divider.dataset) {
                const dataset = divider.dataset;

                // Read each setting from data attributes
                if (dataset.lineStyle) settings.line_style = dataset.lineStyle;
                if (dataset.lineColor) settings.line_color = dataset.lineColor;
                if (dataset.lineSecondaryColor) settings.line_secondary_color = dataset.lineSecondaryColor;
                if (dataset.lineThickness) settings.line_thickness = parseInt(dataset.lineThickness);
                if (dataset.marginTop) settings.margin_top = parseInt(dataset.marginTop);
                if (dataset.marginBottom) settings.margin_bottom = parseInt(dataset.marginBottom);
                if (dataset.enableGlow) settings.enable_glow = dataset.enableGlow === 'true';
                if (dataset.enableShine) settings.enable_shine = dataset.enableShine === 'true';
                if (dataset.enableParticles) settings.enable_particles = dataset.enableParticles === 'true';
                if (dataset.animationSpeed) settings.animation_speed = dataset.animationSpeed;

                console.log('Line break settings loaded from data attributes:', settings);
            }
            // Fallback: Try to get settings from Salla theme system
            else if (typeof salla !== 'undefined' && salla.config && salla.config.get) {
                const componentSettings = salla.config.get('components.lineBreak') || {};

                Object.keys(DEFAULT_SETTINGS).forEach(key => {
                    if (componentSettings[key] !== undefined) {
                        settings[key] = componentSettings[key];
                    }
                });

                console.log('Line break settings loaded from salla.config:', settings);
            }
            else {
                console.log('Line break settings: Using defaults');
            }
        } catch (error) {
            console.warn('Could not load line break theme settings:', error);
        }

        return settings;
    }

    /**
     * Convert hex color to rgba
     */
    function hexToRgba(hex, alpha = 1) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        if (!result) return `rgba(29, 233, 182, ${alpha})`;
        
        const r = parseInt(result[1], 16);
        const g = parseInt(result[2], 16);
        const b = parseInt(result[3], 16);
        
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    /**
     * Refresh all line break components with new settings
     */
    function refreshLineBreakComponents() {
        const dividers = document.querySelectorAll('.gaming-advanced-divider');
        dividers.forEach(divider => {
            applyThemeSettings(divider);
        });
        console.log('Line break components refreshed');
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initLineBreakComponents);
    } else {
        initLineBreakComponents();
    }

    // Listen for theme changes
    document.addEventListener('salla:theme:updated', refreshLineBreakComponents);

    // Expose refresh function globally for theme customizer
    window.refreshLineBreakComponents = refreshLineBreakComponents;

    // Add body class to indicate component is loaded
    document.body.classList.add('line-break-enhanced');

    // Dispatch event to indicate component is loaded
    document.dispatchEvent(new CustomEvent('line-break-enhanced-loaded'));

})();
