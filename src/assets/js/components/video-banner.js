/**
 * Video Banner Component - Optimized Performance
 * Handles video loading, animations, and error states
 */
(function() {
    'use strict';
    
    let isInitialized = false;
    const videoBanners = new Map();
    
    // Optimized media loader
    class MediaLoader {
        constructor(banner, mediaType) {
            this.banner = banner;
            this.mediaType = mediaType;
            this.isLoaded = false;
            this.retryCount = 0;
            this.maxRetries = 2;
        }
        
        async load() {
            const loadingIndicator = this.banner.querySelector('.video-loading, .image-loading');
            
            try {
                switch (this.mediaType) {
                    case 'video':
                        await this.loadVideo();
                        break;
                    case 'iframe':
                        await this.loadIframe();
                        break;
                    case 'image':
                    case 'gif':
                        await this.loadImage();
                        break;
                }
                
                this.onLoadSuccess();
            } catch (error) {
                this.onLoadError(error);
            } finally {
                this.hideLoading(loadingIndicator);
            }
        }
        
        async loadVideo() {
            const video = this.banner.querySelector('.video-element');
            if (!video) return;
            
            return new Promise((resolve, reject) => {
                const onLoad = () => {
                    video.removeEventListener('loadeddata', onLoad);
                    video.removeEventListener('error', onError);
                    resolve();
                };
                
                const onError = () => {
                    video.removeEventListener('loadeddata', onLoad);
                    video.removeEventListener('error', onError);
                    reject(new Error('Video failed to load'));
                };
                
                video.addEventListener('loadeddata', onLoad, { once: true });
                video.addEventListener('error', onError, { once: true });
                
                video.play().catch(() => {
                    // Autoplay prevented, but still consider it loaded
                    resolve();
                });
            });
        }
        
        async loadIframe() {
            const iframe = this.banner.querySelector('.video-iframe');
            if (!iframe) return;
            
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Iframe load timeout'));
                }, 5000);
                
                const onLoad = () => {
                    clearTimeout(timeout);
                    iframe.removeEventListener('load', onLoad);
                    resolve();
                };
                
                iframe.addEventListener('load', onLoad, { once: true });
            });
        }
        
        async loadImage() {
            const image = this.banner.querySelector('.banner-image');
            if (!image) return;

            // Add loading class to hide image initially
            image.classList.add('loading');

            return new Promise((resolve, reject) => {
                // Check if image is already loaded (cached)
                if (image.complete && image.naturalHeight !== 0) {
                    image.classList.remove('loading');
                    resolve();
                    return;
                }

                const onLoad = () => {
                    image.removeEventListener('load', onLoad);
                    image.removeEventListener('error', onError);
                    image.classList.remove('loading');
                    resolve();
                };

                const onError = () => {
                    image.removeEventListener('load', onLoad);
                    image.removeEventListener('error', onError);

                    if (this.retryCount < this.maxRetries) {
                        this.retryCount++;
                        setTimeout(() => {
                            this.retryImageLoad(image).then(resolve).catch(reject);
                        }, 1000 * this.retryCount);
                    } else {
                        image.classList.remove('loading');
                        reject(new Error('Image failed to load after retries'));
                    }
                };

                image.addEventListener('load', onLoad, { once: true });
                image.addEventListener('error', onError, { once: true });

                // Force reload if src is already set but not loaded
                if (image.src && !image.complete) {
                    const currentSrc = image.src;
                    image.src = '';
                    image.src = currentSrc;
                }
            });
        }
        
        async retryImageLoad(image) {
            return new Promise((resolve, reject) => {
                // Store original src if not already stored
                if (!image.dataset.originalSrc) {
                    image.dataset.originalSrc = image.src;
                }

                const originalSrc = image.dataset.originalSrc;

                if (this.retryCount === 1) {
                    // First retry: Add cache buster
                    image.src = originalSrc + (originalSrc.includes('?') ? '&' : '?') + 'cb=' + Date.now();
                } else {
                    // Second retry: Remove crossorigin and referrerpolicy
                    image.removeAttribute('crossorigin');
                    image.removeAttribute('referrerpolicy');
                    image.src = originalSrc + (originalSrc.includes('?') ? '&' : '?') + 'retry=' + Date.now();
                }

                const onLoad = () => {
                    image.classList.remove('loading');
                    resolve();
                };

                const onError = () => {
                    reject(new Error('Retry failed'));
                };

                image.addEventListener('load', onLoad, { once: true });
                image.addEventListener('error', onError, { once: true });
            });
        }
        
        onLoadSuccess() {
            this.isLoaded = true;
            this.banner.classList.add(`${this.mediaType}-loaded`);

            // For images and gifs, also add generic image-loaded class
            if (this.mediaType === 'image' || this.mediaType === 'gif') {
                this.banner.classList.add('image-loaded');
            }

            // Remove fallback if exists
            const fallback = this.banner.querySelector('.iframe-fallback, .image-fallback');
            if (fallback) {
                fallback.style.display = 'none';
            }

            // Debug log for development
            if (process.env.NODE_ENV === 'development') {
                console.log(`${this.mediaType} loaded successfully`);
            }
        }
        
        onLoadError(error) {
            this.banner.classList.add('video-error');
            
            // Show fallback if exists
            const fallback = this.banner.querySelector('.iframe-fallback, .image-fallback');
            if (fallback) {
                fallback.style.display = 'flex';
            }
            
            if (process.env.NODE_ENV === 'development') {
                console.warn('Media load error:', error.message);
            }
        }
        
        hideLoading(loadingIndicator) {
            if (loadingIndicator) {
                loadingIndicator.style.opacity = '0';
                setTimeout(() => {
                    loadingIndicator.style.display = 'none';
                }, 300);
            }
        }
    }
    
    // Optimized animation handler with bouncing effects
    class AnimationHandler {
        constructor(banner) {
            this.banner = banner;
            this.observer = null;
            this.hasAnimated = false; // Ensure animation happens only once
        }

        init() {
            if (!('IntersectionObserver' in window)) {
                this.fallbackAnimation();
                return;
            }

            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.hasAnimated) {
                        // LCP Optimization: Remove delay for immediate animation
                        this.animateIn(entry.target);
                        this.hasAnimated = true;
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1, // Lower threshold for earlier trigger
                rootMargin: '100px' // Larger margin for earlier detection
            });

            this.observer.observe(this.banner);
        }

        animateIn(target) {
            // Add main section animation
            target.classList.add('animate-in');

            // LCP Optimization: Prioritize title element for immediate rendering
            const title = target.querySelector('.video-title');
            if (title) {
                // Immediate animation for LCP element
                title.classList.add('animate-in');
            }

            // Animate other elements with minimal staggered delay
            const animateElements = target.querySelectorAll('.animate-element:not(.video-title)');
            animateElements.forEach((element, index) => {
                // Reduced delay for faster sequence
                const delay = 0.1 + (index * 0.2);
                element.style.animationDelay = `${delay}s`;

                // Add animation class with minimal delay
                setTimeout(() => {
                    element.classList.add('animate-in');
                }, delay * 1000);
            });

            // Button container with reduced delay
            const buttonContainer = target.querySelector('.video-button-container');
            if (buttonContainer) {
                setTimeout(() => {
                    buttonContainer.classList.add('animate-in');
                }, 600);
            }
        }

        fallbackAnimation() {
            // Immediate animation for browsers without IntersectionObserver
            if (!this.hasAnimated) {
                requestAnimationFrame(() => {
                    this.animateIn(this.banner);
                    this.hasAnimated = true;
                });
            }
        }

        destroy() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
        }
    }
    
    // Video pause/play optimization
    class VideoController {
        constructor(banner) {
            this.banner = banner;
            this.video = banner.querySelector('.video-element');
            this.observer = null;
        }
        
        init() {
            if (!this.video || !('IntersectionObserver' in window)) return;
            
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.playVideo();
                    } else {
                        this.pauseVideo();
                    }
                });
            }, {
                threshold: 0.1
            });
            
            this.observer.observe(this.banner);
        }
        
        playVideo() {
            if (this.video && this.video.paused) {
                this.video.play().catch(() => {
                    // Autoplay prevented, ignore
                });
            }
        }
        
        pauseVideo() {
            if (this.video && !this.video.paused) {
                this.video.pause();
            }
        }
        
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
        }
    }
    
    // Main initialization
    function initVideoBanner(banner) {
        const bannerId = banner.id;
        if (videoBanners.has(bannerId)) return;
        
        // Determine media type
        let mediaType = 'video';
        if (banner.querySelector('.video-iframe')) mediaType = 'iframe';
        else if (banner.querySelector('.banner-image')) mediaType = banner.querySelector('.gif-image') ? 'gif' : 'image';
        
        // Initialize components
        const mediaLoader = new MediaLoader(banner, mediaType);
        const animationHandler = new AnimationHandler(banner);
        const videoController = new VideoController(banner);
        
        // Store references for cleanup
        videoBanners.set(bannerId, {
            mediaLoader,
            animationHandler,
            videoController
        });
        
        // Initialize
        animationHandler.init();
        videoController.init();
        
        // Load media
        requestAnimationFrame(() => {
            mediaLoader.load();
        });
    }
    
    // Initialize all video banners
    function init() {
        if (isInitialized) return;
        
        const banners = document.querySelectorAll('.video-banner-section');
        banners.forEach(initVideoBanner);
        
        isInitialized = true;
    }
    
    // Cleanup function
    function cleanup() {
        videoBanners.forEach(({ animationHandler, videoController }) => {
            animationHandler.destroy();
            videoController.destroy();
        });
        videoBanners.clear();
        isInitialized = false;
    }
    
    // LCP-optimized initialization - prioritize critical elements
    function initCriticalElements() {
        // Immediately show LCP elements to prevent layout shift
        const lcpOptimizedBanners = document.querySelectorAll('.video-banner-section[data-lcp-optimized="true"]');
        lcpOptimizedBanners.forEach(banner => {
            const title = banner.querySelector('.video-title');
            if (title) {
                title.style.opacity = '1';
                title.style.transform = 'translateY(0)';
                title.style.visibility = 'visible';
            }
        });
    }

    // Performance-optimized initialization
    if (document.readyState === 'loading') {
        // Initialize critical elements immediately
        document.addEventListener('DOMContentLoaded', () => {
            initCriticalElements();
            init();
        }, { once: true });
    } else {
        initCriticalElements();
        requestAnimationFrame(init);
    }
    
    // Expose for manual control
    window.videoBannerController = {
        init,
        cleanup,
        initBanner: initVideoBanner
    };
    
})();
