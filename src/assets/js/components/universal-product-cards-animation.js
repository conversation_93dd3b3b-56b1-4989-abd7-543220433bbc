/**
 * Universal Product Cards Animation System
 * Provides lazy loading and sliding up animations for all product card types
 * with performance optimization and accessibility support
 */

(function() {
    'use strict';

    class UniversalProductCardsAnimator {
        constructor() {
            this.observers = new Map();
            this.animatedCards = new Set();
            this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
            this.isMobile = window.innerWidth <= 768;
            this.isSmallMobile = window.innerWidth <= 480;
            this.init();
        }

        init() {
            this.setupIntersectionObserver();
            this.observeExistingCards();
            this.setupMutationObserver();
            this.setupResizeHandler();
        }

        /**
         * Setup Intersection Observer for lazy loading
         */
        setupIntersectionObserver() {
            if (!('IntersectionObserver' in window)) {
                this.fallbackAnimation();
                return;
            }

            const observerOptions = {
                threshold: 0.1,
                rootMargin: '50px 0px 50px 0px'
            };

            this.intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.animatedCards.has(entry.target)) {
                        this.animateCard(entry.target);
                        this.animatedCards.add(entry.target);
                        this.intersectionObserver.unobserve(entry.target);
                    }
                });
            }, observerOptions);
        }

        /**
         * Observe existing product cards on page load
         */
        observeExistingCards() {
            const cardSelectors = [
                'custom-salla-product-card',
                '.s-product-card',
                '.product-card',
                'salla-products-list custom-salla-product-card',
                '.products-grid .product-card',
                '.product-item',
                '.product-wrapper'
            ];

            cardSelectors.forEach(selector => {
                const cards = document.querySelectorAll(selector);
                cards.forEach(card => {
                    if (!this.animatedCards.has(card)) {
                        this.intersectionObserver.observe(card);
                    }
                });
            });
        }

        /**
         * Setup Mutation Observer to watch for dynamically added cards
         */
        setupMutationObserver() {
            this.mutationObserver = new MutationObserver((mutations) => {
                mutations.forEach(mutation => {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.observeNewCards(node);
                        }
                    });
                });
            });

            this.mutationObserver.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        /**
         * Observe newly added cards
         */
        observeNewCards(element) {
            const cardSelectors = [
                'custom-salla-product-card',
                '.s-product-card',
                '.product-card',
                '.product-item',
                '.product-wrapper'
            ];

            // Check if the element itself is a product card
            cardSelectors.forEach(selector => {
                if (element.matches && element.matches(selector.replace('.', ''))) {
                    if (!this.animatedCards.has(element)) {
                        this.intersectionObserver.observe(element);
                    }
                }
            });

            // Check for product cards within the element
            cardSelectors.forEach(selector => {
                const cards = element.querySelectorAll(selector);
                cards.forEach(card => {
                    if (!this.animatedCards.has(card)) {
                        this.intersectionObserver.observe(card);
                    }
                });
            });
        }

        /**
         * Animate individual product card
         */
        animateCard(card) {
            // Skip animation if user prefers reduced motion
            if (this.prefersReducedMotion) {
                card.classList.add('animate-in');
                card.classList.add('animation-complete');
                return;
            }

            // Use requestAnimationFrame for smooth animation
            requestAnimationFrame(() => {
                card.classList.add('animate-in');

                // Calculate cleanup timing based on device
                const animationDuration = this.isSmallMobile ? 500 : 
                                        this.isMobile ? 600 : 800;

                // Clean up will-change after animation completes
                setTimeout(() => {
                    card.style.willChange = 'auto';
                    card.classList.add('animation-complete');
                }, animationDuration + 100);
            });
        }

        /**
         * Fallback animation for browsers without IntersectionObserver
         */
        fallbackAnimation() {
            const cardSelectors = [
                'custom-salla-product-card',
                '.s-product-card',
                '.product-card',
                'salla-products-list custom-salla-product-card',
                '.products-grid .product-card',
                '.product-item',
                '.product-wrapper'
            ];

            cardSelectors.forEach(selector => {
                const cards = document.querySelectorAll(selector);
                cards.forEach((card, index) => {
                    setTimeout(() => {
                        this.animateCard(card);
                        this.animatedCards.add(card);
                    }, index * (this.isMobile ? 60 : 80));
                });
            });
        }

        /**
         * Setup resize handler for responsive behavior
         */
        setupResizeHandler() {
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    this.isMobile = window.innerWidth <= 768;
                    this.isSmallMobile = window.innerWidth <= 480;
                }, 250);
            }, { passive: true });
        }

        /**
         * Manually trigger animation for specific cards
         */
        animateCards(cards) {
            if (!Array.isArray(cards)) {
                cards = [cards];
            }

            cards.forEach((card, index) => {
                if (!this.animatedCards.has(card)) {
                    setTimeout(() => {
                        this.animateCard(card);
                        this.animatedCards.add(card);
                    }, index * (this.isMobile ? 60 : 80));
                }
            });
        }

        /**
         * Reset animation state for specific cards
         */
        resetCards(cards) {
            if (!Array.isArray(cards)) {
                cards = [cards];
            }

            cards.forEach(card => {
                card.classList.remove('animate-in', 'animation-complete');
                card.style.willChange = 'opacity, transform';
                this.animatedCards.delete(card);
                this.intersectionObserver.observe(card);
            });
        }

        /**
         * Refresh the entire system
         */
        refresh() {
            this.observeExistingCards();
        }

        /**
         * Destroy the animator and clean up resources
         */
        destroy() {
            if (this.intersectionObserver) {
                this.intersectionObserver.disconnect();
                this.intersectionObserver = null;
            }

            if (this.mutationObserver) {
                this.mutationObserver.disconnect();
                this.mutationObserver = null;
            }

            this.observers.clear();
            this.animatedCards.clear();
        }

        /**
         * Get performance statistics
         */
        getStats() {
            return {
                animatedCards: this.animatedCards.size,
                prefersReducedMotion: this.prefersReducedMotion,
                isMobile: this.isMobile,
                isSmallMobile: this.isSmallMobile
            };
        }
    }

    // Initialize when DOM is ready
    let universalProductCardsAnimator;

    function initUniversalProductCards() {
        if (universalProductCardsAnimator) {
            universalProductCardsAnimator.destroy();
        }
        universalProductCardsAnimator = new UniversalProductCardsAnimator();
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initUniversalProductCards);
    } else {
        initUniversalProductCards();
    }

    // Re-initialize on theme ready event (for Salla themes)
    document.addEventListener('theme::ready', () => {
        if (universalProductCardsAnimator) {
            universalProductCardsAnimator.refresh();
        } else {
            initUniversalProductCards();
        }
    });

    // Handle page navigation (for SPAs)
    window.addEventListener('popstate', () => {
        if (universalProductCardsAnimator) {
            universalProductCardsAnimator.refresh();
        }
    });

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        if (universalProductCardsAnimator) {
            universalProductCardsAnimator.destroy();
        }
    });

    // Export for global access
    window.UniversalProductCardsAnimator = universalProductCardsAnimator;

    // Export utility functions
    window.animateProductCards = function(cards) {
        if (universalProductCardsAnimator) {
            universalProductCardsAnimator.animateCards(cards);
        }
    };

    window.resetProductCards = function(cards) {
        if (universalProductCardsAnimator) {
            universalProductCardsAnimator.resetCards(cards);
        }
    };

    window.refreshProductCardsAnimation = function() {
        if (universalProductCardsAnimator) {
            universalProductCardsAnimator.refresh();
        }
    };

})();
