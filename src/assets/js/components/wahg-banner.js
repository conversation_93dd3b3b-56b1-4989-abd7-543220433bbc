/**
 * WahgBanner Component
 * Modern banner component with light sweep hover effect
 */

class WahgBanner {
    constructor() {
        this.banners = [];
        this.bannerSections = [];
        this.animatedSections = new Set();
        this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        this.init();
    }

    init() {
        this.findBanners();
        this.findBannerSections();
        this.setupEventListeners();
        this.setupIntersectionObserver();
        this.setupAnimationObserver();
    }

    findBanners() {
        this.banners = document.querySelectorAll('.wahg-banner');
        console.log(`WahgBanner: Found ${this.banners.length} banner(s)`);
    }

    findBannerSections() {
        this.bannerSections = document.querySelectorAll('.s-block--wahg-banner');
        console.log(`WahgBanner: Found ${this.bannerSections.length} banner section(s)`);
    }

    setupEventListeners() {
        this.banners.forEach((banner, index) => {
            // Add click tracking
            banner.addEventListener('click', (e) => {
                this.trackClick(banner, index);
            });

            // Add keyboard support
            banner.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    banner.click();
                }
            });

            // Add image load error handling
            const image = banner.querySelector('.wahg-banner__image');
            if (image) {
                image.addEventListener('error', () => {
                    this.handleImageError(image);
                });

                image.addEventListener('load', () => {
                    this.handleImageLoad(image);
                });
            }
        });
    }

    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.lazyLoadImage(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px'
            });

            this.banners.forEach(banner => {
                const image = banner.querySelector('.wahg-banner__image[data-src]');
                if (image) {
                    observer.observe(banner);
                }
            });
        }
    }

    setupAnimationObserver() {
        if (!('IntersectionObserver' in window)) {
            this.fallbackAnimation();
            return;
        }

        const animationObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.animatedSections.has(entry.target)) {
                    // Reduced delay for faster entrance
                    setTimeout(() => {
                        this.animateBannerSection(entry.target);
                    }, 50);
                    this.animatedSections.add(entry.target);
                    animationObserver.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.15,
            rootMargin: '50px'
        });

        this.bannerSections.forEach(section => {
            animationObserver.observe(section);
        });
    }

    lazyLoadImage(banner) {
        const image = banner.querySelector('.wahg-banner__image[data-src]');
        if (image) {
            image.src = image.dataset.src;
            image.removeAttribute('data-src');
        }
    }

    animateBannerSection(section) {
        // Use requestAnimationFrame for smooth animation batching
        requestAnimationFrame(() => {
            // Animate main section
            section.classList.add('animate-in');

            // Skip delays and complex animations if user prefers reduced motion
            if (this.prefersReducedMotion) {
                const title = section.querySelector('.s-block__title');
                const wrapper = section.querySelector('.wahg-banner-wrapper');

                if (title) title.classList.add('animate-in');
                if (wrapper) wrapper.classList.add('animate-in');

                // Clean up immediately for reduced motion
                setTimeout(() => {
                    section.style.willChange = 'auto';
                    if (title) title.style.willChange = 'auto';
                    if (wrapper) wrapper.style.willChange = 'auto';
                }, 200);
                return;
            }

            // Detect mobile for even faster animations
            const isMobile = window.innerWidth <= 768;
            const titleDelay = isMobile ? 80 : 120;
            const wrapperDelay = isMobile ? 180 : 250;

            // Animate title with device-optimized delay
            const title = section.querySelector('.s-block__title');
            if (title) {
                setTimeout(() => {
                    requestAnimationFrame(() => {
                        title.classList.add('animate-in');
                    });
                }, titleDelay);
            }

            // Animate banner wrapper with device-optimized delay
            const wrapper = section.querySelector('.wahg-banner-wrapper');
            if (wrapper) {
                setTimeout(() => {
                    requestAnimationFrame(() => {
                        wrapper.classList.add('animate-in');

                        // Clean up will-change after smooth transition completes
                        setTimeout(() => {
                            section.style.willChange = 'auto';
                            if (title) title.style.willChange = 'auto';
                            wrapper.style.willChange = 'auto';
                        }, isMobile ? 600 : 800);
                    });
                }, wrapperDelay);
            }
        });
    }

    fallbackAnimation() {
        // Immediate animation for browsers without IntersectionObserver
        this.bannerSections.forEach((section, index) => {
            if (!this.animatedSections.has(section)) {
                setTimeout(() => {
                    this.animateBannerSection(section);
                    this.animatedSections.add(section);
                }, index * 200);
            }
        });
    }

    handleImageError(image) {
        console.warn('WahgBanner: Failed to load image', image.src);
        
        // Create fallback placeholder
        const placeholder = document.createElement('div');
        placeholder.className = 'wahg-banner__placeholder';
        placeholder.innerHTML = `
            <div style="
                display: flex;
                align-items: center;
                justify-content: center;
                height: 200px;
                background: #f5f5f5;
                color: #666;
                font-size: 16px;
                text-align: center;
                border-radius: 8px;
            ">
                <div>
                    <div style="font-size: 48px; margin-bottom: 10px;">🖼️</div>
                    <div>فشل في تحميل الصورة</div>
                </div>
            </div>
        `;
        
        image.parentNode.replaceChild(placeholder, image);
    }

    handleImageLoad(image) {
        image.style.opacity = '1';
        console.log('WahgBanner: Image loaded successfully');
    }

    trackClick(banner, index) {
        const url = banner.href;
        console.log(`WahgBanner: Banner ${index + 1} clicked`, { url });

        // Track with analytics if available
        if (typeof gtag !== 'undefined') {
            gtag('event', 'banner_click', {
                'banner_position': index + 1,
                'banner_url': url
            });
        }

        // Track with Salla analytics if available
        if (typeof salla !== 'undefined' && salla.analytics) {
            salla.analytics.track('banner_click', {
                position: index + 1,
                url: url
            });
        }
    }

    // Public methods for external control
    refresh() {
        this.findBanners();
        this.findBannerSections();
        this.setupEventListeners();
        this.setupIntersectionObserver();
        this.setupAnimationObserver();
    }

    destroy() {
        this.banners.forEach(banner => {
            // Remove event listeners
            banner.replaceWith(banner.cloneNode(true));
        });
        this.banners = [];
        this.bannerSections = [];
        this.animatedSections.clear();
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.WahgBanner = new WahgBanner();
});

// Re-initialize on theme ready event (for Salla themes)
document.addEventListener('theme::ready', () => {
    if (window.WahgBanner) {
        window.WahgBanner.refresh();
    }
});

export default WahgBanner;
