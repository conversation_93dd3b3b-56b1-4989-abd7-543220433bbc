/**
 * Optimized Gaming Banners Component
 * Performance improvements:
 * - Single shared IntersectionObserver
 * - Debounced animations
 * - Memory leak prevention
 * - Reduced DOM queries
 */

class GamingBannersManager {
    constructor() {
        this.observer = null;
        this.animatedElements = new Set();
        this.isInitialized = false;
        
        // Bind methods to preserve context
        this.handleIntersection = this.handleIntersection.bind(this);
        this.init = this.init.bind(this);
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', this.init);
        } else {
            this.init();
        }
    }
    
    init() {
        if (this.isInitialized) return;
        
        try {
            this.createObserver();
            this.observeBannerSections();
            this.isInitialized = true;
        } catch (error) {
            console.warn('Gaming banners initialization failed:', error);
        }
    }
    
    createObserver() {
        // Create single observer with optimized options
        this.observer = new IntersectionObserver(this.handleIntersection, {
            threshold: 0.15, // Reduced threshold for better performance
            rootMargin: '50px 0px', // Start animation slightly before element is visible
        });
    }
    
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting && !this.animatedElements.has(entry.target)) {
                this.animateElement(entry.target);
                this.animatedElements.add(entry.target);
                
                // Unobserve after animation to free up resources
                this.observer.unobserve(entry.target);
            }
        });
    }
    
    animateElement(element) {
        // Use requestAnimationFrame for smooth animations
        requestAnimationFrame(() => {
            element.classList.add('animate-in');
            
            // Animate child elements with optimized delays
            const animateElements = element.querySelectorAll('.animate-element');
            
            animateElements.forEach((child, index) => {
                // Use CSS custom properties for better performance
                child.style.setProperty('--animation-delay', `${0.2 + (index * 0.15)}s`);
                child.style.transitionDelay = `var(--animation-delay)`;
                
                requestAnimationFrame(() => {
                    child.classList.add('animate-in');
                });
            });
        });
    }
    
    observeBannerSections() {
        // Find all banner sections efficiently
        const bannerSections = document.querySelectorAll('[id^="gaming-banners-section-"]');
        
        bannerSections.forEach(section => {
            if (section && this.observer) {
                this.observer.observe(section);
            }
        });
    }
    
    // Cleanup method for memory management
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
        
        this.animatedElements.clear();
        this.isInitialized = false;
    }
}

// Performance optimization: Use passive event listeners
const addPassiveEventListener = (element, event, handler) => {
    element.addEventListener(event, handler, { passive: true });
};

// Initialize the manager
let gamingBannersManager;

// Lazy initialization to avoid blocking main thread
const initializeGamingBanners = () => {
    if (!gamingBannersManager) {
        gamingBannersManager = new GamingBannersManager();
    }
};

// Use intersection observer to only initialize when needed
const lazyInitObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            initializeGamingBanners();
            lazyInitObserver.disconnect(); // Clean up after initialization
        }
    });
}, { rootMargin: '100px' });

// Start observing for banner sections
document.addEventListener('DOMContentLoaded', () => {
    const firstBannerSection = document.querySelector('[id^="gaming-banners-section-"]');
    if (firstBannerSection) {
        lazyInitObserver.observe(firstBannerSection);
    }
});

// Cleanup on page unload to prevent memory leaks
window.addEventListener('beforeunload', () => {
    if (gamingBannersManager) {
        gamingBannersManager.destroy();
    }
    lazyInitObserver.disconnect();
});

// Performance monitoring (optional)
if (window.performance && window.performance.mark) {
    window.performance.mark('gaming-banners-script-loaded');
}

// Export for potential external use
window.GamingBannersManager = GamingBannersManager;

// Additional performance optimizations
const optimizeForLowEndDevices = () => {
    // Detect low-end devices
    const isLowEndDevice = navigator.hardwareConcurrency <= 2 ||
                          navigator.deviceMemory <= 2 ||
                          /Android.*Chrome\/[.0-9]*\s/.test(navigator.userAgent);

    if (isLowEndDevice) {
        // Disable animations on low-end devices
        const style = document.createElement('style');
        style.textContent = `
            .gaming-particle,
            .gaming-glow {
                display: none !important;
            }
            .gaming-banner-image {
                transition: none !important;
            }
        `;
        document.head.appendChild(style);
    }
};

// Apply optimizations when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', optimizeForLowEndDevices);
} else {
    optimizeForLowEndDevices();
}
