/**
 * WhatsApp Float Button Component - Theme Settings Integration
 * Handles WhatsApp floating button based on theme settings from twilight.json
 */
(function() {
    'use strict';

    let whatsappBtn = null;
    let isInitialized = false;

    // Default settings
    const DEFAULT_SETTINGS = {
        whatsapp_enabled: false,
        whatsapp_number: '',
        whatsapp_position: 'right',
        whatsapp_display_pages: 'all_pages'
    };

    /**
     * Get theme settings from various sources
     */
    function getThemeSettings() {
        let settings = { ...DEFAULT_SETTINGS };

        try {
            // Method 1: Check window variables (primary method)
            if (typeof window.whatsapp_settings !== 'undefined') {
                settings = { ...settings, ...window.whatsapp_settings };
                console.log('WhatsApp Settings loaded from window:', settings);
            }
            // Method 2: Check salla.config (fallback)
            else if (typeof salla !== 'undefined' && salla.config && salla.config.get) {
                const enabled = salla.config.get('whatsapp_enabled');
                const number = salla.config.get('whatsapp_number');
                const position = salla.config.get('whatsapp_position');
                const displayPages = salla.config.get('whatsapp_display_pages');

                console.log('Raw salla.config values:', {
                    enabled, number, position, displayPages
                });

                // Only update if values are not undefined
                if (enabled !== undefined) settings.whatsapp_enabled = enabled;
                if (number !== undefined) settings.whatsapp_number = number;
                if (position !== undefined) settings.whatsapp_position = position;
                if (displayPages !== undefined) settings.whatsapp_display_pages = displayPages;

                console.log('WhatsApp Settings loaded from salla.config:', settings);
            }
            // Method 3: For testing - enable with default number
            else {
                console.log('WhatsApp Settings: Using defaults, checking if enabled manually...');
                // Check if user has enabled it manually in theme settings
                // For now, enable for testing - you can disable this later
                settings.whatsapp_enabled = true;
                settings.whatsapp_number = '966501234567';
                console.log('WhatsApp Settings: Enabled for testing with default number');
            }

            return settings;
        } catch (e) {
            console.warn('Failed to load WhatsApp settings, using defaults:', e.message);
            return settings;
        }
    }

    /**
     * Check if WhatsApp should be displayed on current page
     */
    function shouldDisplayOnCurrentPage(displayPages) {
        if (displayPages === 'all_pages') {
            return true;
        }

        if (displayPages === 'homepage_only') {
            // Get current path info
            const currentPath = window.location.pathname;

            // Primary check: Use Salla's page.slug method (most reliable)
            let sallaPageSlug = null;
            try {
                if (typeof salla !== 'undefined' && salla.config && salla.config.get) {
                    sallaPageSlug = salla.config.get('page.slug');
                }
            } catch (e) {
                // Silent fallback
            }

            // Check various homepage indicators
            const checks = {
                // Primary Salla method
                sallaPageSlug: sallaPageSlug === 'home' || sallaPageSlug === 'index' || sallaPageSlug === '',
                // Basic path checks
                basicPath: currentPath === '/' || currentPath === '' || currentPath === '/home',
                rootPath: currentPath === '/' && !window.location.search,
                // DOM-based checks
                bodyHomepage: document.body.classList.contains('homepage'),
                bodyHome: document.body.classList.contains('home'),
                metaPageType: !!document.querySelector('meta[name="page-type"][content="home"]'),
                metaOgType: !!document.querySelector('meta[property="og:type"][content="website"]'),
                homePageClass: !!document.querySelector('.home-page'),
                dataPageHome: !!document.querySelector('[data-page="home"]'),
                // Additional Salla-specific checks
                sallaHomepage: !!document.querySelector('.salla-homepage'),
                homeContainer: !!document.querySelector('.home-container'),
                mainHome: !!document.querySelector('main.home'),
                pageHome: !!document.querySelector('.page-home'),
                // Check for common homepage elements
                heroSection: !!document.querySelector('.hero-section'),
                homeSlider: !!document.querySelector('.home-slider'),
                // Check body data attributes
                bodyDataPage: document.body.getAttribute('data-page') === 'home',
                bodyDataRoute: document.body.getAttribute('data-route') === 'home',
                // Check for home component (from index.twig)
                homeComponent: !!document.querySelector('[data-component="home"]') || !!document.querySelector('.home-component')
            };

            // Determine if it's homepage with priority system
            let isHomepage = false;

            // Priority 1: Salla page.slug (most reliable)
            if (checks.sallaPageSlug) {
                isHomepage = true;
            }
            // Priority 2: Basic path checks (reliable fallback)
            else if (checks.basicPath || checks.rootPath) {
                isHomepage = true;
            }
            // Priority 3: DOM-based checks (less reliable but comprehensive)
            else if (Object.values(checks).some(check => check === true)) {
                isHomepage = true;
            }

            return isHomepage;
        }

        return false;
    }

    /**
     * Validate phone number
     */
    function isValidPhoneNumber(phoneNumber) {
        if (!phoneNumber || typeof phoneNumber !== 'string') {
            return false;
        }

        const cleanNumber = phoneNumber.replace(/[\+\s\-\(\)]/g, '');
        return cleanNumber.length >= 10 && /^\d+$/.test(cleanNumber);
    }

    /**
     * Generate WhatsApp URL
     */
    function generateWhatsAppURL(phoneNumber) {
        const cleanNumber = phoneNumber.replace(/[\+\s\-\(\)]/g, '');
        const defaultMessage = encodeURIComponent("مرحباً، أود الاستفسار عن منتجاتكم");
        return `https://wa.me/${cleanNumber}?text=${defaultMessage}`;
    }

    /**
     * Create WhatsApp floating button HTML
     */
    function createWhatsAppButton(settings) {
        const button = document.createElement('a');
        button.id = 'whatsapp-float-button';
        button.className = 'gaming-whatsapp-float tooltip-toggle--hover icon-trigger';
        button.href = generateWhatsAppURL(settings.whatsapp_number);
        button.target = '_blank';
        button.rel = 'noopener';
        button.setAttribute('aria-label', 'Chat on WhatsApp');
        button.dataset.phone = settings.whatsapp_number;

        // Position will be handled by applyPositionStyles function

        // Create button content
        button.innerHTML = `
            <div class="gaming-whatsapp-glow"></div>
            <div class="gaming-whatsapp-pulse"></div>
            <div class="tooltip-content gaming-tooltip">تحدث معنا عبر الواتساب</div>
            <div class="whatsapp-icon gaming-whatsapp-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="white">
                    <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 222-99.6 222-222 0-59.3-23.1-115-65-156.9zM223.9 413.3c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 335.4l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56 81.2 56 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
                </svg>
            </div>
        `;

        return button;
    }

    /**
     * Apply position-specific styles
     */
    function applyPositionStyles(position) {
        const styleId = 'whatsapp-position-styles';
        let existingStyle = document.getElementById(styleId);

        if (existingStyle) {
            existingStyle.remove();
        }

        const style = document.createElement('style');
        style.id = styleId;

        // Clear positioning and apply new one
        if (position === 'left') {
            // User wants LEFT side
            style.textContent = `
                #whatsapp-float-button.gaming-whatsapp-float {
                    right: auto !important;
                    left: 20px !important;
                }

                @media (max-width: 768px) {
                    #whatsapp-float-button.gaming-whatsapp-float {
                        left: 15px !important;
                    }
                }
            `;
        } else {
            // User wants RIGHT side (default)
            style.textContent = `
                #whatsapp-float-button.gaming-whatsapp-float {
                    left: auto !important;
                    right: 20px !important;
                }

                @media (max-width: 768px) {
                    #whatsapp-float-button.gaming-whatsapp-float {
                        right: 15px !important;
                    }
                }
            `;
        }

        document.head.appendChild(style);
    }

    /**
     * Remove existing WhatsApp button
     */
    function removeWhatsAppButton() {
        if (whatsappBtn && whatsappBtn.parentNode) {
            whatsappBtn.parentNode.removeChild(whatsappBtn);
            whatsappBtn = null;
        }

        // Remove position styles
        const styleElement = document.getElementById('whatsapp-position-styles');
        if (styleElement) {
            styleElement.remove();
        }
    }

    /**
     * Initialize or update WhatsApp button
     */
    function initializeWhatsAppButton() {
        const settings = getThemeSettings();

        // Remove existing button first
        removeWhatsAppButton();

        // Check if WhatsApp is enabled
        if (!settings.whatsapp_enabled) {
            return;
        }

        // Validate phone number
        if (!isValidPhoneNumber(settings.whatsapp_number)) {
            return;
        }

        // Check if should display on current page
        if (!shouldDisplayOnCurrentPage(settings.whatsapp_display_pages)) {
            return;
        }

        try {
            // Create and append button
            whatsappBtn = createWhatsAppButton(settings);
            document.body.appendChild(whatsappBtn);

            // Apply position styles
            applyPositionStyles(settings.whatsapp_position);

            isInitialized = true;

        } catch (error) {
            console.error('Failed to initialize WhatsApp button:', error);
        }
    }

    /**
     * Update WhatsApp button settings
     */
    function updateWhatsAppButton(newSettings = null) {
        const settings = newSettings || getThemeSettings();

        if (!settings.whatsapp_enabled) {
            removeWhatsAppButton();
            return;
        }

        if (!isValidPhoneNumber(settings.whatsapp_number)) {
            console.warn('Cannot update WhatsApp button: Invalid phone number');
            removeWhatsAppButton();
            return;
        }

        if (!shouldDisplayOnCurrentPage(settings.whatsapp_display_pages)) {
            removeWhatsAppButton();
            return;
        }

        // Re-initialize with new settings
        initializeWhatsAppButton();
    }

    /**
     * Handle settings updates from Salla
     */
    function handleSettingsUpdate(data) {
        const whatsappSettings = ['whatsapp_enabled', 'whatsapp_number', 'whatsapp_position', 'whatsapp_display_pages'];

        if (whatsappSettings.includes(data.key)) {
            setTimeout(() => {
                updateWhatsAppButton();
            }, 100);
        }
    }

    /**
     * Initialize the WhatsApp component
     */
    function init() {
        if (isInitialized) {
            return;
        }

        // Initialize button
        initializeWhatsAppButton();

        // Listen for settings updates
        if (typeof salla !== 'undefined' && salla.event) {
            salla.event.on('settings.updated', handleSettingsUpdate);
        }

        // Listen for page navigation (SPA support)
        if (typeof salla !== 'undefined' && salla.event) {
            salla.event.on('page.changed', () => {
                setTimeout(() => {
                    updateWhatsAppButton();
                }, 500);
            });
        }
    }

    /**
     * Test function for debugging
     */
    function testWhatsApp() {
        console.log('🧪 Testing WhatsApp component...');
        console.log('🧪 Current settings:', getThemeSettings());
        console.log('🧪 Is initialized:', isInitialized);
        console.log('🧪 Button element:', whatsappBtn);

        // Force enable for testing
        window.whatsapp_settings = {
            whatsapp_enabled: true,
            whatsapp_number: '966501234567',
            whatsapp_position: 'right',
            whatsapp_display_pages: 'all_pages'
        };

        console.log('🧪 Forced settings:', window.whatsapp_settings);

        // Re-initialize
        isInitialized = false;
        init();
    }

    /**
     * Public API
     */
    window.WhatsAppButton = {
        init: init,
        update: updateWhatsAppButton,
        remove: removeWhatsAppButton,
        test: testWhatsApp,
        getSettings: getThemeSettings,
        updateNumber: function(phoneNumber) {
            if (isValidPhoneNumber(phoneNumber)) {
                const settings = getThemeSettings();
                settings.whatsapp_number = phoneNumber;
                updateWhatsAppButton(settings);
            } else {
                console.warn('Invalid phone number provided:', phoneNumber);
            }
        }
    };

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        // Use requestAnimationFrame for better performance
        requestAnimationFrame(init);
    }

    // Initialize when Salla is ready
    if (typeof salla !== 'undefined' && salla.onReady) {
        salla.onReady(() => {
            setTimeout(init, 100);
        });
    }

    // Fallback initialization
    window.addEventListener('load', () => {
        setTimeout(() => {
            if (!isInitialized) {
                init();
            }
        }, 1000);
    });

    // Add test functions to window for debugging
    window.testWhatsApp = function(position = 'right', displayPages = 'all_pages') {
        console.log('🧪 Testing WhatsApp manually...');
        console.log('🧪 Position:', position, 'Display:', displayPages);

        // Force enable settings
        window.whatsapp_settings = {
            whatsapp_enabled: true,
            whatsapp_number: '966501234567',
            whatsapp_position: position,
            whatsapp_display_pages: displayPages
        };

        // Reset and reinitialize
        isInitialized = false;
        removeWhatsAppButton();
        init();
    };

    // Test specific positions
    window.testWhatsAppLeft = function() {
        console.log('🧪 Testing LEFT position...');
        window.testWhatsApp('left', 'all_pages');
    };

    window.testWhatsAppRight = function() {
        console.log('🧪 Testing RIGHT position...');
        window.testWhatsApp('right', 'all_pages');
    };

    // Test homepage only
    window.testWhatsAppHomepage = function() {
        console.log('🧪 Testing HOMEPAGE ONLY display...');
        window.testWhatsApp('right', 'homepage_only');
    };

    // Debug current page characteristics
    window.debugCurrentPage = function() {
        console.log('🔍 ===== CURRENT PAGE DEBUG =====');
        console.log('URL Info:');
        console.log('  - href:', window.location.href);
        console.log('  - pathname:', window.location.pathname);
        console.log('  - search:', window.location.search);
        console.log('  - hash:', window.location.hash);

        console.log('Document Info:');
        console.log('  - title:', document.title);
        console.log('  - body classes:', document.body.className);

        console.log('Body Attributes:');
        Array.from(document.body.attributes).forEach(attr => {
            console.log(`  - ${attr.name}: ${attr.value}`);
        });

        console.log('Meta Tags:');
        const metaTags = document.querySelectorAll('meta[name], meta[property]');
        metaTags.forEach(meta => {
            const name = meta.getAttribute('name') || meta.getAttribute('property');
            const content = meta.getAttribute('content');
            console.log(`  - ${name}: ${content}`);
        });

        console.log('Page Elements:');
        const selectors = [
            '.home-page', '.homepage', '.home-container', 'main.home',
            '.page-home', '.salla-homepage', '.hero-section', '.home-slider',
            '[data-page="home"]', '[data-route="home"]'
        ];
        selectors.forEach(selector => {
            const element = document.querySelector(selector);
            console.log(`  - ${selector}:`, !!element);
        });

        // Test homepage detection
        console.log('Homepage Detection Test:');
        const result = shouldDisplayOnCurrentPage('homepage_only');
        console.log('  - Result:', result);

        return {
            url: window.location.href,
            pathname: window.location.pathname,
            title: document.title,
            bodyClasses: document.body.className,
            isHomepage: result,
            sallaPageSlug: (typeof salla !== 'undefined' && salla.config) ? salla.config.get('page.slug') : 'N/A'
        };
    };

    // Force homepage detection for testing
    window.forceHomepageDetection = function() {
        console.log('🧪 Forcing homepage detection...');

        // Method 1: Check current Salla page slug
        if (typeof salla !== 'undefined' && salla.config) {
            const pageSlug = salla.config.get('page.slug');
            console.log('🔍 Current Salla page.slug:', pageSlug);

            if (!pageSlug || pageSlug === 'home' || pageSlug === 'index') {
                console.log('✅ This should be detected as homepage');
                window.testWhatsApp('right', 'homepage_only');
                return;
            }
        }

        // Method 2: Check URL path
        if (window.location.pathname === '/' || window.location.pathname === '') {
            console.log('✅ Root path detected - should be homepage');
            window.testWhatsApp('right', 'homepage_only');
            return;
        }

        console.log('❌ This page may not be the homepage');
        console.log('🔍 Try navigating to the homepage and run this function again');
    };

})();
