/**
 * Common Questions (FAQ) Component - Optimized Performance
 * Handles accordion functionality and animations
 */
(function() {
    'use strict';
    
    let isInitialized = false;
    const faqInstances = new Map();
    
    // Optimized FAQ controller
    class FAQController {
        constructor(section) {
            this.section = section;
            this.sectionId = section.id;
            this.questions = section.querySelectorAll('.faq-question');
            this.answers = section.querySelectorAll('.faq-answer');
            this.icons = section.querySelectorAll('.faq-icon i');
            
            this.animationObserver = null;
            this.currentOpenIndex = -1;
            
            this.init();
        }
        
        init() {
            if (!this.section || this.questions.length === 0) return;
            
            this.bindEvents();
            this.initAnimations();
            this.setupKeyboardNavigation();
        }
        
        bindEvents() {
            // Use event delegation for better performance
            this.section.addEventListener('click', (e) => {
                const button = e.target.closest('.faq-question');
                if (!button) return;
                
                e.preventDefault();
                
                const index = Array.from(this.questions).indexOf(button);
                if (index !== -1) {
                    this.toggleFAQ(index);
                }
            });
        }
        
        toggleFAQ(index) {
            if (index < 0 || index >= this.questions.length) return;
            
            const button = this.questions[index];
            const answer = this.answers[index];
            const icon = this.icons[index];
            
            if (!button || !answer || !icon) return;
            
            const isExpanded = button.getAttribute('aria-expanded') === 'true';
            
            // Close all items first
            this.closeAllItems();
            
            // Toggle current item if it wasn't expanded
            if (!isExpanded) {
                this.openItem(index, button, answer, icon);
                this.currentOpenIndex = index;
            } else {
                this.currentOpenIndex = -1;
            }
        }
        
        closeAllItems() {
            this.questions.forEach(btn => {
                btn.setAttribute('aria-expanded', 'false');
            });
            
            this.answers.forEach(answer => {
                answer.classList.add('hidden');
                answer.classList.remove('show');
                answer.style.maxHeight = '0';
            });
            
            this.icons.forEach(icon => {
                icon.classList.remove('rotate-180');
            });
        }
        
        openItem(index, button, answer, icon) {
            button.setAttribute('aria-expanded', 'true');
            answer.classList.remove('hidden');
            icon.classList.add('rotate-180');
            
            // Use requestAnimationFrame for smooth animation
            requestAnimationFrame(() => {
                answer.classList.add('show');
                answer.style.maxHeight = answer.scrollHeight + 'px';
                
                // Reset max-height after animation completes
                setTimeout(() => {
                    if (answer.classList.contains('show')) {
                        answer.style.maxHeight = 'none';
                    }
                }, 300);
            });
        }
        
        setupKeyboardNavigation() {
            this.questions.forEach((button, index) => {
                button.addEventListener('keydown', (e) => {
                    switch (e.key) {
                        case 'Enter':
                        case ' ':
                            e.preventDefault();
                            this.toggleFAQ(index);
                            break;
                        case 'ArrowDown':
                            e.preventDefault();
                            this.focusNext(index);
                            break;
                        case 'ArrowUp':
                            e.preventDefault();
                            this.focusPrevious(index);
                            break;
                        case 'Home':
                            e.preventDefault();
                            this.questions[0]?.focus();
                            break;
                        case 'End':
                            e.preventDefault();
                            this.questions[this.questions.length - 1]?.focus();
                            break;
                    }
                });
            });
        }
        
        focusNext(currentIndex) {
            const nextIndex = (currentIndex + 1) % this.questions.length;
            this.questions[nextIndex]?.focus();
        }
        
        focusPrevious(currentIndex) {
            const prevIndex = currentIndex === 0 ? this.questions.length - 1 : currentIndex - 1;
            this.questions[prevIndex]?.focus();
        }
        
        initAnimations() {
            if (!('IntersectionObserver' in window)) {
                // Fallback for older browsers
                this.section.classList.add('animate-fade-in');
                this.section.querySelectorAll('.whatsapp-faq-item').forEach(item => {
                    item.classList.add('animate-slide-in');
                });
                return;
            }
            
            // Section animation observer
            this.animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                        this.animateItems();
                        this.animationObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -10% 0px'
            });
            
            this.animationObserver.observe(this.section);
        }
        
        animateItems() {
            const items = this.section.querySelectorAll('.whatsapp-faq-item');
            items.forEach((item, index) => {
                setTimeout(() => {
                    item.classList.add('animate-slide-in');
                }, index * 100);
            });
        }
        
        // Public methods for external control
        openQuestion(index) {
            if (index >= 0 && index < this.questions.length) {
                this.toggleFAQ(index);
            }
        }
        
        closeAllQuestions() {
            this.closeAllItems();
            this.currentOpenIndex = -1;
        }
        
        getCurrentOpenIndex() {
            return this.currentOpenIndex;
        }
        
        destroy() {
            if (this.animationObserver) {
                this.animationObserver.disconnect();
                this.animationObserver = null;
            }
            
            // Remove event listeners
            this.section.removeEventListener('click', this.handleClick);
            
            this.questions.forEach(button => {
                button.removeEventListener('keydown', this.handleKeydown);
            });
        }
    }
    
    // Initialize FAQ sections
    function initCommonQuestions() {
        if (isInitialized) return;
        
        const faqSections = document.querySelectorAll('.whatsapp-faq-section');
        
        faqSections.forEach(section => {
            const id = section.id;
            
            if (id && !faqInstances.has(id)) {
                const controller = new FAQController(section);
                faqInstances.set(id, controller);
            }
        });
        
        isInitialized = true;
    }
    
    // Cleanup function
    function cleanup() {
        faqInstances.forEach(controller => {
            controller.destroy();
        });
        faqInstances.clear();
        isInitialized = false;
    }
    
    // Performance-optimized initialization
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initCommonQuestions, { once: true });
    } else {
        requestAnimationFrame(initCommonQuestions);
    }
    
    // Expose for manual control and external access
    window.faqController = {
        init: initCommonQuestions,
        cleanup,
        instances: faqInstances,
        
        // Helper methods for external control
        openQuestion: (sectionId, questionIndex) => {
            const instance = faqInstances.get(sectionId);
            if (instance) {
                instance.openQuestion(questionIndex);
            }
        },
        
        closeAll: (sectionId) => {
            const instance = faqInstances.get(sectionId);
            if (instance) {
                instance.closeAllQuestions();
            }
        },
        
        getCurrentOpen: (sectionId) => {
            const instance = faqInstances.get(sectionId);
            return instance ? instance.getCurrentOpenIndex() : -1;
        }
    };
    
})();
