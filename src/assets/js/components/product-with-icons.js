/**
 * Product with Icons Component JavaScript
 * Handles interactions and animations for the product with icons component
 */

class ProductWithIcons {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeAnimations();
        this.handleResponsiveLayout();
    }

    setupEventListeners() {
        // Handle icon clicks with analytics
        document.querySelectorAll('.product-with-icons-section .icon-link').forEach(link => {
            link.addEventListener('click', (e) => {
                this.trackIconClick(e.currentTarget);
            });
        });

        // Handle product card interactions
        document.querySelectorAll('.featured-product-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                this.animateProductCard(card, 'enter');
            });

            card.addEventListener('mouseleave', () => {
                this.animateProductCard(card, 'leave');
            });
        });

        // Handle window resize for responsive adjustments
        window.addEventListener('resize', () => {
            this.handleResponsiveLayout();
        });
    }

    initializeAnimations() {
        // Initialize AOS (Animate On Scroll) if available
        if (typeof AOS !== 'undefined') {
            AOS.refresh();
        }

        // Add stagger animation to icon items
        this.staggerIconAnimations();
    }

    staggerIconAnimations() {
        const iconItems = document.querySelectorAll('.product-with-icons-section .icon-item');
        
        iconItems.forEach((item, index) => {
            // Add entrance animation delay
            item.style.animationDelay = `${index * 0.1}s`;
            
            // Add hover effect enhancement
            item.addEventListener('mouseenter', () => {
                this.enhanceIconHover(item);
            });

            item.addEventListener('mouseleave', () => {
                this.resetIconHover(item);
            });
        });
    }

    enhanceIconHover(iconItem) {
        const iconWrapper = iconItem.querySelector('.icon-wrapper');
        const iconImage = iconItem.querySelector('.icon-image');
        
        if (iconWrapper && iconImage) {
            // Add pulse effect
            iconWrapper.style.transform = 'scale(1.1)';
            iconImage.style.transform = 'scale(1.2) rotate(5deg)';
            
            // Add glow effect
            iconWrapper.style.boxShadow = '0 0 20px rgba(255, 255, 255, 0.3)';
        }
    }

    resetIconHover(iconItem) {
        const iconWrapper = iconItem.querySelector('.icon-wrapper');
        const iconImage = iconItem.querySelector('.icon-image');
        
        if (iconWrapper && iconImage) {
            iconWrapper.style.transform = '';
            iconImage.style.transform = '';
            iconWrapper.style.boxShadow = '';
        }
    }

    animateProductCard(card, action) {
        if (action === 'enter') {
            // Add floating animation
            card.style.transform = 'translateY(-10px) scale(1.02)';
            
            // Add subtle glow
            card.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 255, 255, 0.1)';
        } else {
            // Reset to default state
            card.style.transform = '';
            card.style.boxShadow = '';
        }
    }

    trackIconClick(iconLink) {
        // Track icon clicks for analytics
        const iconTitle = iconLink.querySelector('.icon-title')?.textContent || 'Unknown Icon';
        const iconUrl = iconLink.href || '#';
        
        // Send analytics event if available
        if (typeof gtag !== 'undefined') {
            gtag('event', 'icon_click', {
                'event_category': 'Product With Icons',
                'event_label': iconTitle,
                'value': iconUrl
            });
        }

        // Console log for debugging
        console.log('Icon clicked:', {
            title: iconTitle,
            url: iconUrl
        });
    }

    handleResponsiveLayout() {
        const section = document.querySelector('.product-with-icons-section');
        if (!section) return;

        const grid = section.querySelector('.product-with-icons-grid');
        if (!grid) return;

        const screenWidth = window.innerWidth;
        
        // Adjust layout based on screen size
        if (screenWidth <= 768) {
            this.adjustMobileLayout(grid);
        } else if (screenWidth <= 1024) {
            this.adjustTabletLayout(grid);
        } else {
            this.adjustDesktopLayout(grid);
        }
    }

    adjustMobileLayout(grid) {
        // Ensure proper mobile layout
        grid.style.gridTemplateColumns = '1fr';
        
        // Reorder elements for better mobile experience
        const leftIcons = grid.querySelector('.left-icons');
        const rightIcons = grid.querySelector('.right-icons');
        const productSection = grid.querySelector('.product-section');
        
        if (leftIcons && rightIcons && productSection) {
            // Product first, then icons
            productSection.style.order = '1';
            leftIcons.style.order = '2';
            rightIcons.style.order = '3';
        }
    }

    adjustTabletLayout(grid) {
        // Tablet layout adjustments
        grid.style.gridTemplateColumns = '1fr';
        
        const iconContainers = grid.querySelectorAll('.icons-container');
        iconContainers.forEach(container => {
            container.style.flexDirection = 'row';
            container.style.justifyContent = 'center';
        });
    }

    adjustDesktopLayout(grid) {
        // Desktop layout
        grid.style.gridTemplateColumns = '1fr 2fr 1fr';
        
        const iconContainers = grid.querySelectorAll('.icons-container');
        iconContainers.forEach(container => {
            container.style.flexDirection = 'column';
            container.style.justifyContent = 'flex-start';
        });
    }

    // Utility method to add loading states
    addLoadingState(element) {
        element.classList.add('loading');
        element.style.opacity = '0.6';
        element.style.pointerEvents = 'none';
    }

    removeLoadingState(element) {
        element.classList.remove('loading');
        element.style.opacity = '';
        element.style.pointerEvents = '';
    }

    // Method to refresh component after dynamic content changes
    refresh() {
        this.init();
    }
}

// Initialize the component when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new ProductWithIcons();
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductWithIcons;
}
