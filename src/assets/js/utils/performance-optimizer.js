/**
 * Performance Optimization Utility
 * Provides common performance optimization functions for theme components
 */

(function() {
    'use strict';

    class PerformanceOptimizer {
        constructor() {
            this.isLowEndDevice = this.detectLowEndDevice();
            this.prefersReducedMotion = this.checkReducedMotion();
            this.observers = new Map();
            this.init();
        }

        init() {
            // Apply global optimizations
            this.applyGlobalOptimizations();
            
            // Monitor performance
            this.monitorPerformance();
        }

        /**
         * Detect low-end devices based on hardware capabilities
         */
        detectLowEndDevice() {
            const hardwareConcurrency = navigator.hardwareConcurrency || 2;
            const deviceMemory = navigator.deviceMemory || 2;
            const isSlowConnection = navigator.connection && 
                (navigator.connection.effectiveType === 'slow-2g' || 
                 navigator.connection.effectiveType === '2g');

            return hardwareConcurrency <= 2 || 
                   deviceMemory <= 2 || 
                   isSlowConnection ||
                   /Android.*Chrome\/[.0-9]*\s/.test(navigator.userAgent);
        }

        /**
         * Check if user prefers reduced motion
         */
        checkReducedMotion() {
            return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        }

        /**
         * Apply global performance optimizations
         */
        applyGlobalOptimizations() {
            if (this.isLowEndDevice || this.prefersReducedMotion) {
                this.disableExpensiveAnimations();
            }

            // Add CSS containment to improve layout performance
            this.addCSSContainment();

            // Optimize images
            this.optimizeImages();
        }

        /**
         * Disable expensive animations on low-end devices
         */
        disableExpensiveAnimations() {
            const style = document.createElement('style');
            style.id = 'performance-optimizations';
            style.textContent = `
                /* Disable expensive animations on low-end devices */
                .gaming-particle,
                .gaming-glow,
                .circuit-animation,
                .complex-shadow {
                    display: none !important;
                }

                /* Simplify transitions */
                * {
                    transition-duration: 0.1s !important;
                    animation-duration: 0.1s !important;
                }

                /* Disable complex filters */
                .special-gallery-image,
                .gaming-banner-image {
                    filter: none !important;
                }

                /* Reduce box-shadow complexity */
                .gaming-banner-slide,
                .special-gallery-item {
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
                }
            `;
            document.head.appendChild(style);
        }

        /**
         * Add CSS containment for better performance
         */
        addCSSContainment() {
            const style = document.createElement('style');
            style.id = 'css-containment';
            style.textContent = `
                /* CSS Containment for better performance */
                .s-block {
                    contain: layout style;
                }

                .gaming-moving-text-section,
                .video-banner-section,
                .special-gallery,
                .gaming-banners-section {
                    contain: layout style paint;
                }

                /* Optimize will-change usage */
                .gaming-moving-text,
                .special-gallery-image,
                .gaming-banner-image {
                    will-change: transform;
                }

                /* Clean up will-change after animations */
                .animation-complete {
                    will-change: auto !important;
                }
            `;
            document.head.appendChild(style);
        }

        /**
         * Optimize images for better performance
         */
        optimizeImages() {
            const images = document.querySelectorAll('img:not([loading])');
            images.forEach(img => {
                // Add lazy loading to images that don't have it
                if (!img.hasAttribute('loading')) {
                    img.setAttribute('loading', 'lazy');
                }
                
                // Add decoding async for better performance
                if (!img.hasAttribute('decoding')) {
                    img.setAttribute('decoding', 'async');
                }
            });
        }

        /**
         * Create optimized intersection observer
         */
        createOptimizedObserver(callback, options = {}) {
            const defaultOptions = {
                threshold: 0.1,
                rootMargin: '50px',
                ...options
            };

            const observer = new IntersectionObserver(callback, defaultOptions);
            
            // Store observer for cleanup
            const observerId = Date.now() + Math.random();
            this.observers.set(observerId, observer);
            
            return { observer, observerId };
        }

        /**
         * Throttle function for performance
         */
        throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }

        /**
         * Debounce function for performance
         */
        debounce(func, wait, immediate) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                const later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }

        /**
         * Monitor performance metrics
         */
        monitorPerformance() {
            if (!window.performance || !window.performance.mark) return;

            // Mark theme load time
            window.performance.mark('theme-performance-optimizer-loaded');

            // Monitor long tasks
            if ('PerformanceObserver' in window) {
                try {
                    const observer = new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        entries.forEach(entry => {
                            if (entry.duration > 50) {
                                console.warn(`Long task detected: ${entry.duration}ms`);
                            }
                        });
                    });
                    observer.observe({ entryTypes: ['longtask'] });
                } catch (e) {
                    // PerformanceObserver not supported
                }
            }
        }

        /**
         * Clean up all observers and resources
         */
        destroy() {
            this.observers.forEach(observer => {
                observer.disconnect();
            });
            this.observers.clear();

            // Remove performance optimization styles
            const perfStyle = document.getElementById('performance-optimizations');
            if (perfStyle) perfStyle.remove();

            const containmentStyle = document.getElementById('css-containment');
            if (containmentStyle) containmentStyle.remove();
        }

        /**
         * Get performance recommendations
         */
        getRecommendations() {
            const recommendations = [];

            if (this.isLowEndDevice) {
                recommendations.push('Device detected as low-end - animations simplified');
            }

            if (this.prefersReducedMotion) {
                recommendations.push('User prefers reduced motion - animations disabled');
            }

            const longTasks = performance.getEntriesByType('longtask') || [];
            if (longTasks.length > 0) {
                recommendations.push(`${longTasks.length} long tasks detected - consider code splitting`);
            }

            return recommendations;
        }
    }

    // Initialize performance optimizer
    const performanceOptimizer = new PerformanceOptimizer();

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        performanceOptimizer.destroy();
    });

    // Export for global use
    window.PerformanceOptimizer = performanceOptimizer;

    // Export utility functions
    window.throttle = performanceOptimizer.throttle.bind(performanceOptimizer);
    window.debounce = performanceOptimizer.debounce.bind(performanceOptimizer);

})();
