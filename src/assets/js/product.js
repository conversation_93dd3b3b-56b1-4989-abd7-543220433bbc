import 'lite-youtube-embed';
import BasePage from './base-page';
import Fslightbox from 'fslightbox';
window.fslightbox = Fslightbox;
import { zoom, cleanupZoom } from './partials/image-zoom';

class Product extends BasePage {
    onReady() {
        app.watchElements({
            totalPrice: '.total-price',
            beforePrice: '.before-price',
            startingPriceTitle: '.starting-price-title',
        });

        this.initProductOptionValidations();

        if(imageZoom){
            // call the function when the page is ready
            this.initImagesZooming();
            // listen to screen resizing
            window.addEventListener('resize', () => this.initImagesZooming());
        }

        // Initialize enhanced gallery features
        this.initEnhancedGallery();
    }

    initProductOptionValidations() {
      document.querySelector('.product-form')?.addEventListener('change', function(){
        this.reportValidity() && salla.product.getPrice(new FormData(this));
      });
    }

    initImagesZooming() {
      // skip if the screen is not desktop
      if (window.innerWidth < 1024) return;

      // Initialize zoom for the first active image
      this.initZoomForActiveImage();

      // Listen for slider changes
      const slider = document.querySelector('salla-slider.details-slider');
      if (slider) {
        slider.addEventListener('slideChange', (e) => {
          // Clean up existing zoom
          this.cleanupZoom();

          // Set delay till the active class is ready
          setTimeout(() => {
            this.initZoomForActiveImage();
            this.updateImageCounter(e.detail?.activeIndex || 0);
          }, 300);
        });
      }

      // Initialize thumbnail click handlers
      this.initThumbnailHandlers();

      // Initialize image counter
      this.updateImageCounter(0);
    }

    initZoomForActiveImage() {
      if (window.innerWidth < 1024) return;

      const activeImage = document.querySelector('.image-slider .swiper-slide-active img');
      if (activeImage && activeImage.id && !activeImage.closest('.model-entry-wrapper')) {
        // Clean up any existing zoom glass first
        this.cleanupZoom();

        // Create new zoom
        setTimeout(() => {
          zoom(activeImage.id, 2.5);
        }, 100);
      }
    }

    cleanupZoom() {
      // Use the imported cleanup function
      cleanupZoom();
    }

    initThumbnailHandlers() {
      const thumbnails = document.querySelectorAll('.thumb-slide');
      thumbnails.forEach((thumb, index) => {
        thumb.addEventListener('click', () => {
          this.setActiveThumb(index);
        });
      });
    }

    setActiveThumb(index) {
      // Remove active class from all thumbnails
      document.querySelectorAll('.thumb-wrapper').forEach(wrapper => {
        wrapper.classList.remove('active');
      });

      // Add active class to clicked thumbnail
      const activeThumb = document.querySelector(`.thumb-slide[data-index="${index}"] .thumb-wrapper`);
      if (activeThumb) {
        activeThumb.classList.add('active');
      }
    }

    updateImageCounter(activeIndex = 0) {
      const currentImageSpan = document.querySelector('.current-image');
      if (currentImageSpan) {
        currentImageSpan.textContent = activeIndex + 1;
      }

      // Update active thumbnail
      this.setActiveThumb(activeIndex);
    }

    // Enhanced gallery initialization
    initEnhancedGallery() {
      // Add keyboard navigation
      this.initKeyboardNavigation();

      // Add touch gestures for mobile
      this.initTouchGestures();

      // Add lazy loading for thumbnails
      this.initLazyLoading();

      // Add accessibility improvements
      this.initAccessibility();
    }

    initKeyboardNavigation() {
      const slider = document.querySelector('salla-slider.details-slider');
      if (!slider) return;

      slider.setAttribute('tabindex', '0');
      slider.addEventListener('keydown', (e) => {
        const totalImages = document.querySelectorAll('.thumb-slide').length;
        const currentIndex = parseInt(document.querySelector('.current-image')?.textContent || '1') - 1;

        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            if (currentIndex > 0) {
              this.goToSlide(currentIndex - 1);
            }
            break;
          case 'ArrowRight':
            e.preventDefault();
            if (currentIndex < totalImages - 1) {
              this.goToSlide(currentIndex + 1);
            }
            break;
          case 'Home':
            e.preventDefault();
            this.goToSlide(0);
            break;
          case 'End':
            e.preventDefault();
            this.goToSlide(totalImages - 1);
            break;
        }
      });
    }

    initTouchGestures() {
      const slider = document.querySelector('.image-slider');
      if (!slider) return;

      let startX = 0;
      let startY = 0;
      let isSwipe = false;

      slider.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
        isSwipe = false;
      }, { passive: true });

      slider.addEventListener('touchmove', (e) => {
        if (!startX || !startY) return;

        const diffX = Math.abs(e.touches[0].clientX - startX);
        const diffY = Math.abs(e.touches[0].clientY - startY);

        if (diffX > diffY && diffX > 50) {
          isSwipe = true;
        }
      }, { passive: true });

      slider.addEventListener('touchend', (e) => {
        if (!isSwipe) return;

        const endX = e.changedTouches[0].clientX;
        const totalImages = document.querySelectorAll('.thumb-slide').length;
        const currentIndex = parseInt(document.querySelector('.current-image')?.textContent || '1') - 1;

        if (startX - endX > 50 && currentIndex < totalImages - 1) {
          // Swipe left - next image
          this.goToSlide(currentIndex + 1);
        } else if (endX - startX > 50 && currentIndex > 0) {
          // Swipe right - previous image
          this.goToSlide(currentIndex - 1);
        }

        startX = 0;
        startY = 0;
        isSwipe = false;
      }, { passive: true });
    }

    goToSlide(index) {
      const slider = document.querySelector('salla-slider.details-slider');
      if (slider && slider.swiper) {
        slider.swiper.slideTo(index);
      }
    }

    initLazyLoading() {
      // Enhanced lazy loading for thumbnails
      const lazyImages = document.querySelectorAll('.thumb-image.lazy');

      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target;
              img.src = img.dataset.src;
              img.classList.remove('lazy');
              img.classList.add('loaded');
              imageObserver.unobserve(img);
            }
          });
        }, {
          rootMargin: '50px'
        });

        lazyImages.forEach(img => imageObserver.observe(img));
      } else {
        // Fallback for older browsers
        lazyImages.forEach(img => {
          img.src = img.dataset.src;
          img.classList.remove('lazy');
          img.classList.add('loaded');
        });
      }
    }

    initAccessibility() {
      // Add ARIA labels and roles
      const slider = document.querySelector('salla-slider.details-slider');
      if (slider) {
        slider.setAttribute('role', 'region');
        slider.setAttribute('aria-label', 'Product image gallery');
      }

      // Add alt text to thumbnails
      const thumbnails = document.querySelectorAll('.thumb-slide');
      thumbnails.forEach((thumb, index) => {
        thumb.setAttribute('role', 'button');
        thumb.setAttribute('aria-label', `View image ${index + 1}`);
        thumb.setAttribute('tabindex', '0');

        // Add keyboard support for thumbnails
        thumb.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.setActiveThumb(index);
            this.goToSlide(index);
          }
        });
      });

      // Add live region for screen readers
      const imageCounter = document.querySelector('.image-counter');
      if (imageCounter) {
        imageCounter.setAttribute('aria-live', 'polite');
        imageCounter.setAttribute('aria-atomic', 'true');
      }
    }

    initZoomForDesktop() {
      // skip if the screen is not desktop or if glass magnifier
      // is already crated for the image before
      const imageZoom = document.querySelector('.image-slider .magnify-wrapper.swiper-slide-active .img-magnifier-glass');
      if (window.innerWidth  < 1024 || imageZoom) return;

      // Fix image loading issues
      this.ensureImagesLoaded();

      setTimeout(() => {
          // set delay after the resizing is done, start creating the glass
          // to create the glass in the proper position
          const image = document.querySelector('.image-slider .swiper-slide-active img');
          if (image && image.complete && image.naturalHeight !== 0) {
              zoom(image?.id, 2);
          }
      }, 500); // Increased delay for better loading


      document.querySelector('salla-slider.details-slider')?.addEventListener('slideChange', (e) => {
          // set delay till the active class is ready
          setTimeout(() => {
              const imageZoom = document.querySelector('.image-slider .swiper-slide-active .img-magnifier-glass');

              // if the zoom glass is already created skip
              if (window.innerWidth  < 1024 || imageZoom) return;
              const image = document.querySelector('.image-slider .magnify-wrapper.swiper-slide-active img');
              if (image && image.complete && image.naturalHeight !== 0) {
                  zoom(image?.id, 2);
              }
          }, 500) // Increased delay
      })
    }

    ensureImagesLoaded() {
        // Force load all product images
        const images = document.querySelectorAll('.image-slider img[data-src]');
        images.forEach(img => {
            if (img.dataset.src && !img.src.includes(img.dataset.src)) {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
            }
        });

        // Ensure main images are properly loaded
        const mainImages = document.querySelectorAll('.image-slider img:not([data-src])');
        mainImages.forEach(img => {
            if (!img.complete) {
                img.addEventListener('load', () => {
                    // Image loaded, can proceed with zoom if needed
                    if (window.innerWidth >= 1024) {
                        setTimeout(() => {
                            const activeImg = document.querySelector('.image-slider .swiper-slide-active img');
                            if (activeImg === img) {
                                zoom(img.id, 2);
                            }
                        }, 100);
                    }
                });
            }
        });
    }

    registerEvents() {
      salla.event.on('product::price.updated.failed',()=>{
        app.element('.price-wrapper').classList.add('hidden');
        app.element('.out-of-stock').classList.remove('hidden')
        app.anime('.out-of-stock', { scale: [0.88, 1] });
      })
      salla.product.event.onPriceUpdated((res) => {

        app.element('.out-of-stock').classList.add('hidden')
        app.element('.price-wrapper').classList.remove('hidden')

        let data = res.data,
            is_on_sale = data.has_sale_price && data.regular_price > data.price;

        app.startingPriceTitle?.classList.add('hidden');

        app.totalPrice.forEach((el) => {el.innerHTML = salla.money(data.price)});
        app.beforePrice.forEach((el) => {el.innerHTML = salla.money(data.regular_price)});

        app.toggleClassIf('.price_is_on_sale','showed','hidden', ()=> is_on_sale)
        app.toggleClassIf('.starting-or-normal-price','hidden','showed', ()=> is_on_sale)

        app.anime('.total-price', { scale: [0.88, 1] });
      });

      app.onClick('#btn-show-more', e => {
        const button = e.target.closest('.gaming-read-more-btn');
        const content = document.querySelector('#more-content');

        if (content && button) {
          button.classList.add('is-expanded');
          content.style.maxHeight = `${content.scrollHeight}px`;

          // Update button text and icon
          const span = button.querySelector('span');
          const icon = button.querySelector('i');

          if (span) span.textContent = '{{ trans("pages.products.read_less") }}' || 'اقرأ أقل';
          if (icon) icon.classList.replace('sicon-keyboard_arrow_down', 'sicon-keyboard_arrow_up');

          // Add smooth animation
          setTimeout(() => {
            button.style.opacity = '0.7';
            setTimeout(() => {
              button.style.opacity = '1';
            }, 200);
          }, 300);
        }
      });
    }
}

Product.initiateWhenReady(['product.single']);
