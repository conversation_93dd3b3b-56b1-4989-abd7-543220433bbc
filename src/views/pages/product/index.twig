{#
| Variable                   | Type        | Description                                                          |
|----------------------------|-------------|----------------------------------------------------------------------|
| page                       | object      |                                                                      |
| page.title                 | string      | *could be html                                                       |
| page.slug                  | string      | ex: "cat.show"                                                       |
| category                   | ?Category   |                                                                      |
| category.name              | string      |                                                                      |
| category.url               | string      |                                                                      |
| category.sub_categories    | ?Category[] |                                                                      |
| category.image             | ?string     |                                                                      |
| filters                     | bool        | is filters allowed here or not                                        |
| sort_options[]             | SortList[]  |                                                                      |
| sort_options[].id          | string      | ourSuggest, bestSell, topRated, priceFromTopToLow, priceFromLowToTop |
| sort_options[].name        | string      |                                                                      |
| sort_options[].is_selected | bool        | Is current page sorted by it                                         |
| search_query               | ?string     |                                                                      |
#}
{% extends "layouts.master" %}
{% block content %}
    <div class="container px-2.5 ms:px-5 mb-10">
        {# add breadcumbs container in pages to make a space in case breadcrumbs is off #}
        <nav class="breadcrumbs w-full py-5">
            <salla-breadcrumb></salla-breadcrumb>
        </nav>
        <div class="flex items-start flex-col md:flex-row">
            {% if filters %}
                <button class="btn--close-sm close-filters sicon-cancel hidden"></button>
                <salla-filters class="md:w-72 lg:sticky lg:top-20 flex-none" id="filters-menu"></salla-filters>
            {% endif %}

            <div class="main-content flex-1 w-full {{ filters ? 'ltr:lg:ml-8 rtl:lg:mr-8' : '' }}">
                <div class="mb-4 sm:mb-6 flex justify-between items-center">
                    <h1 class="font-bold text-xl rtl:pl-3 ltr:pr-3" id="page-main-title">{{ page.title|raw }}</h1>
                    <div class="flex items-center space-x-2 rtl:space-x-reverse">
                        {% if filters %}
                            <a href="#filters-menu"
                               class="filters-trigger bg-border-color text-primary lg:hidden mt-2">
                                <i class="sicon-filter text-gray-text text-2xl me-2.5"></i>
                            </a>
                        {% endif %}
                        {% if sort_options|length %}
                            <div class="flex items-center">
                                <label class="hidden sm:block rtl:ml-3 ltr:mr-3 whitespace-nowrap"
                                       for="product-filter">{{ trans('pages.categories.sorting') }}</label>
                                <select id="product-filter" class="form-input pt-0 pb-1 rtl:md:pl-10 ltr:md:pr-10">
                                    {% for sort in sort_options %}
                                        <option value="{{ sort.id }}" {{ sort.is_selected?'selected':'' }}>{{ sort.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        {% endif %}
                    </div>

                </div>

                {% hook 'product:index.items.start' %}
                <div class="flex min-h-screen">
                    <salla-products-list class="flex-1 min-w-0 overflow-auto "
                        source="{{ page.slug }}"
                        source-value="{{ page.id }}"
                        {{ filters ?'filters-Results':'' }}>
					          </salla-products-list>
                </div>
                {% hook 'product:index.items.end' %}
            </div>
        </div>
    </div>


    {% if store.settings.category.testimonial_enabled %}
        {% component 'home.testimonials' %}
    {% endif %}
{% endblock %}

{% block scripts %}
    <script defer src="{{ 'product.js' | asset }}"></script>
{% endblock %}
