{#
| Variable                         | Type                              | Description                                                                              |
|----------------------------------|-----------------------------------|------------------------------------------------------------------------------------------|
| page                             | object                            |                                                                                          |
| page.title                       | string                            |                                                                                          |
| page.slug                        | string                            |                                                                                          |
| order                            | Order                             |                                                                                          |
| order.id                         | integer                           |                                                                                          |
| order.reference_id               | integer                           |                                                                                          |
| order.created_at                 | date                              |                                                                                          |
| order.sub_total                  | float                             |                                                                                          |
| order.total                      | float                             |                                                                                          |
| order.cod_cost                   | float                             | * Cash On Delivery                                                                       |
| order.can_reorder                | bool                              |                                                                                          |
| order.can_cancel                 | bool                              |                                                                                          |
| order.can_rate                   | bool                              |                                                                                          |
| order.cancel_url                 | string                            |                                                                                          |
| order.payment_url                | string                            |                                                                                          |
| order.is_pending_payment         | bool                              |                                                                                          |
| order.pending_payment_ends_in    | int                               | How much many seconds untile pending payment ends, zero if it's already expired          |
| order.shipping                   | ?object                           |                                                                                          |
| order.shipping.id                | integer                           |                                                                                          |
| order.shipping.name              | string                            |                                                                                          |
| order.shipping.number            | string                            | number/string/link (..) for shipment number (tracking number)                            |
| order.shipping.logo              | ?string                           |                                                                                          |
| order.shipping.cost              | ?Money                            |                                                                                          |
| order.shipments[]                | ShipmentObject[]                  |                                                                                          |
| order.tax                        | ?object                           |                                                                                          |
| order.tax.amount                 | float                             |                                                                                          |
| order.tax.percent                | float                             |                                                                                          |
| order.status                     | object                            |                                                                                          |
| order.status.name                | string                            |                                                                                          |
| order.status.icon                | string                            |                                                                                          |
| order.status.color               | string                            |                                                                                          |
| order.items                      | OrderItem[]                       |                                                                                          |
| order.options                    | OrderOption[]                     |                                                                                          |
| order.items[].name               |                                   |                                                                                          |
| order.items[].image              |                                   |                                                                                          |
| order.items[].price              | TaxableMoney                      |                                                                                          |
| order.items[].quantity           |                                   |                                                                                          |
| order.items[].total              | TaxableMoney                      |                                                                                          |
| order.items[].product            | ?Product                          | There are cases product is null, when merchant adds special product to the order         |
| order.items[].sub_products       | ?Product[] *Collection            |                                                                                          |
| order.items[].note               |                                   |                                                                                          |
| order.items[].attachments        | OrderItemAttachment[] *Collection | if it's empty will be empty array so you can use it as check `{% if item.attachments %}` |
| order.items[].attachments[].type | string                            |                                                                                          |
| order.items[].attachments[].url  | string                            |                                                                                          |
| order.items[].options            | ?OrderItemOption[] *Collection    |                                                                                          |
| order.items[].options[].name     | string                            |                                                                                          |
| order.items[].options[].value    | string                            |                                                                                          |
| order.items[].options[].is_image | bool                              |                                                                                          |
| order.items[].options[].color    | string                            | option hex color                                                                         |
| order.items[].rating             | ?Rating                           |                                                                                          |
| order.items[].rating.content     | string                            | *HTML                                                                                    |
| order.items[].rating.is_pending  | bool                              |                                                                                          |
| order.items[].rating.stars       | int                               |                                                                                          |
| order.items[].rating.images      | array                             |                                                                                          |
| order.items[].rating.created_at  | date                              |                                                                                          |
| order.discounts                  | array                             |                                                                                          |
| order.discounts[].name           | string                            |                                                                                          |
| order.discounts[].discount       | float                             | minus number                                                                             |
| order.is_rated                   | bool                              |                                                                                          |
| order.rating                     | ?OrderRatings                     |                                                                                          |
| order.rating.store               | ?Rating                           |                                                                                          |
| order.rating.shipping            | ?Rating                           |                                                                                          |
| order.links                      | array                             |                                                                                          |
| order.links[].url                | string                            |                                                                                          |
| order.links[].label              | string                            |                                                                                          |
| order.links[].type               | string                            | One of ['print', 'shipment', 'digital']                                                  |
#}

{% extends "layouts.customer" %}
{% block inner_title %}
<div class="flex justify-between items-center mb-2">
    <h1 class="font-bold text-lg text-center text-start flex-1">
        {{ page.title }}
        {% if order.source is same as('buy_as_gift') %}
            <span class="inline-block text-primary mx-2"><i class="sicon-gift-sharing"></i>&nbsp;{{ trans('pages.orders.gift_tag') }}</span>
        {% endif %}
    </h1>
    {% if order.links | length %}
        <div class="flex space-x-2 rtl:space-x-reverse text-start flex-none px-2">
            {% for link in order.links %}
                <a href="{{link.url}}" class="text-primary" target="_blank">
                    {{ link.label }}
                     <i class="{{user.language.dir == 'rtl' ? 'sicon-arrow-up-left' : 'sicon-arrow-up-right' }}"></i>
                </a>
            {% endfor %}
        </div>
    {% endif %}
</div>
{% endblock %}
{% block inner_content %}
    <div class="space-y-4">

        {% hook 'customer:orders.single.details.start' %}

        {# Order Details #}
        <table class="min-w-full rounded-md overflow-hidden">
            <tbody>
            <tr class="bg-white overflow-hidden flex md:table-row flex-row md:flex-row flex-wrap md:flex-no-wrap py-1.5 md:py-0">
                <td class="single-order-header-item w-full md:w-auto  px-3 md:px-6 py-2 md:py-4 md:h-20 whitespace-nowrap text-sm ">
                    <div class="rtl:ml-2 ltr:mr-2 font-normal text-gray-400">{{ trans('pages.orders.date') }}</div>
                    <b>{{ order.created_at|date|number }}</b>
                </td>

                <td class="single-order-header-item flex justify-between items-center w-full md:w-auto px-3 md:px-6 py-2 md:py-4 md:h-20 whitespace-nowrap text-sm ">
                    {% if order.is_pending_payment and order.pending_payment_ends_in == 0 %}
                        <span class="text-red-500">{{ trans('pages.orders.pending_payment_expired') }}</span>
                    {% else %}
                        <span class="text-primary inline-flex items-center rtl:space-x-reverse space-x-1"
                              style="color: {{ order.status.color }}">
                        <i class="mt-1 {{ order.status.icon }}"></i>
                        <span class=""> {{ order.status.name }}</span>
                    </span>
                    {% endif %}

                    <div class="flex items-center">
                        {% if order.is_price_quote %}
                            <span class="text-red-500 ml-4">{{ trans('pages.orders.your_order_is_under_review') }}</span>
                        {% endif %}

                        {% if (can_print_invoice) %}
                            <salla-button fill="outline" size="small"
                                        onclick="window.open('{{ order.print_url()}}', '_blank', 'toolbar=yes, scrollbars=yes, resizable=yes, width=900, height=700');">
                                <i class="sicon-printer2 v-align"></i>
                                {{ trans('pages.orders.print') }}
                            </salla-button>
                        {% else %}
                            <div class="tooltip-toggle tooltip-toggle--clickable text-primary border border-primary rounded text-center inline-block px-4 py-2 cursor-pointer">
                                <i class="sicon-printer2 v-align"></i>
                                {{ trans('pages.orders.print') }}
                                <div class="tooltip-content shadow-md rounded text-gray-500">
                                    <small> {{  trans('pages.orders.to_order_invoice_contact_vendor') }}
                                        <br />
                                        {{ trans('pages.orders.print_order_details') }}
                                        <a class="text-primary underline cursor-pointer" onclick="window.open('{{ order.print_url()}}', '_blank', 'toolbar=yes, scrollbars=yes, resizable=yes, width=900, height=700');">{{trans('pages.orders.click_here')  }}</a>
                                    </small>
                                    <salla-button shape="link" color="danger" size="small" class="close-tooltip">
                                        <i class="sicon-cancel"></i>
                                    </salla-button>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </td>
            </tr>
            </tbody>
        </table>

        {% if store.settings.rating.allow_update and order.rating is not empty %}    
            <div class="flex items-start gap-2 bg-blue-50 border border-blue-200 text-blue-500 px-2 py-3 rounded-md text-sm">
                <i class="sicon-info"></i>   
                <p>{{trans('pages.order.can_edit_or_delete_your_review_within')}} {{store.settings.rating.update_period}} {{trans('pages.order.days_since_added')}}.</p>
            </div>
        {% endif %}
        {# Order Rating Message & Modal #}
        {% if order.can_rate %}
            <div class="rating-header gradient-bg center-between">
                <div>
                    <h2 class="font-bold mb-1">{{ trans('pages.order.order_rating_title') }}</h2>
                    <p class="text-sm">{{ trans('pages.order.order_rating_message') }}</p>
                </div>
                <salla-button color="light" class="btn--rounded-full !text-primary" onclick="salla.event.dispatch('rating::open')">{{ trans('pages.rating.rate') }}</salla-button>
            </div>
        {% endif %}
        

        {# Order Summary #}
        <div class="overflow-hidden space-y-5">
            {% for package in order.packages %}
                <div class="shipping-orders">
                    {# Shipping Title #}
                    {% if package.shipping_company %}
                    <div class="flex flex-col md:flex-row justify-between md:items-center bg-white px-5 py-3 border-b border-gray-100 rounded-t-md text-sm space-y-4 md:space-y-0">
                        <div class="flex items-center space-x-2 rtl:space-x-reverse">
                            {% if package.shipping_company.logo %}
                                <img src="{{ package.shipping_company.logo }}" class="max-w-16 max-h-8"
                                     alt="{{ package.shipping_company.name }}">
                            {% endif %}
                            <span>{{ package.shipping_company.name }}</span>
                        </div>
                        <div class="flex space-x-5 rtl:space-x-reverse">
                            {% if package.branch %}
                                <span>
                                    {{ trans('pages.orders.branch') }}:
                                    <span class="font-bold">{{ package.branch.name }}</span>
                                </span>
                            {% endif %}
                            {% if package.shipping_company.number %}
                                <span>{{ trans('pages.orders.shipment_no') }}:
                                            <span class="font-bold">{{ package.shipping_company.number|raw }}</span>
                                        </span>
                            {% endif %}
                            {% if package.is_delivered %}
                                <span>{{ trans('pages.orders.status') }}:
                                    <span style="color: {{ package.status.color }}">
                                        <i class="center-v mr-1 {{ package.status.icon }}"></i>
                                        {{ package.status.name }}
                                    </span>
                                </span>
                            {% elseif package.shipping_company.tracing_link %}
                                <a class="font-bold text-primary hover:opacity-80 flex items-center"
                                   href="{{ package.shipping_company.tracing_link|raw }}"
                                   target="_blank"
                                >
                                    <span class="sicon-shipping rtl:ml-1 ltr:mr-1"></span>
                                    {{ trans('pages.orders.tracking_url')}}
                                </a>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    {% for item in package.items %}
                        <div data-order-item-id="{{ item.id }}" data-order-item-product-id="{{ item.product.id }}" class="order-item bg-white p-5 border-b border-gray-100 first:rounded-t-md last:rounded-b-md last:border-none">

                            <div class="mb-5 last:mb-0">
                                {% if item.product %}
                                    <div class="flex rtl:space-x-reverse space-x-5 mb-4">
                                        <a href="{{ item.product.url }}" class="shrink-0">
                                            <img src="{{ item.image ?: 'images/placeholder.png' | asset }}"
                                                 alt="{{ item.name }}"
                                                 class="w-18 h-14 object-{{ item.image ? 'cover':'contain' }} rounded-md">
                                        </a>
                                        <div>
                                            <a href="{{ item.product.url }}"
                                               class="block leading-5 mb-1.5 md:text-base font-semibold transition-colors text-gray-500 hover:text-primary">
                                                {{ item.name }}
                                            </a>
                                            <b>{{ item.price|money }}</b>
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="w-full  flex  justify-between text-sm py-2.5">
                                        <span class="w-40 inline-block font-normal">{{ item.name }}</span>
                                        <b>{{ item.price|money }}</b>
                                    </div>
                                {% endif %}

                                <div class="w-full flex justify-between text-sm py-2.5">
                                    <span class="w-40 inline-block font-normal">{{ trans('pages.products.quantity') }}</span>
                                    <b>{{ item.quantity }}</b>
                                </div>
                                <div class="flex justify-between text-sm py-2.5">
                                    <span class=" w-40 inline-block font-normal">{{ trans('pages.products.price') }}</span>
                                    <b>{{ item.price|money }}</b>
                                </div>

                                <div class="flex justify-between  text-sm py-2.5">
                                  <span class="w-40 inline-block font-normal">{{ trans('pages.orders.total') }}</span>
                                  <b>{{ item.total|money }}</b>
                                </div>
                            </div>

                            {% if item.product.type == 'booking' and item.product_reservations is not empty %}
                              {% if item.booking_location != null %}
                                  <ul class="font-medium text-xs text-gray-600">
                                      <li class="py-1 flex justify-between items-baseline">
                                          <span class="mt-5 mb-3.5 text-sm font-bold">{{ trans('common.elements.location') }}</span>
                                          <span class="text-unicode">{{ item.booking_location }}</span>
                                      </li>
                                  </ul>
                              {% endif %}
                              {% if item.product_reservations.count() > 1 %}
                                  <h2 class="mt-5 mb-3.5 text-sm font-bold">{{ trans('pages.cart.reservations') }}</h2>
                              {% else %}
                                  <h2 class="mt-5 mb-3.5 text-sm font-bold">{{ trans('pages.cart.item_options') }}</h2>
                              {% endif %}

                              <div class="rounded-md p-4 border border-gray-200 mb-2">
                                {% include 'pages.partials.product.reservations' with { reservations: item.product_reservations } %}
                              </div>
                          {% endif %}

                            {% if item.options %}
                              <h2 class="mt-5 mb-3.5 text-sm font-bold">{{ trans('pages.cart.item_options') }}</h2>
                              <div class="flow-root rounded-md px-4 border border-gray-200 mb-2">
                                  <dl class="text-sm text-dark divide-y divide-border-color">
                                      {% for option in item.options %}
                                          <div class="py-2.5 center-between">
                                              <dt class="mb-2 md:mb-0">
                                                  {{ option.name }}:
                                              </dt>
                                              <dd class="font-medium {{ option.is_image ? 'text-gray-900' : 'text-xs' }}">
                                                  {% if option.is_image %}
                                                      <div class="flex rtl:space-x-reverse space-x-2.5">
                                                          <a href="{{ option.value }}" target="_blank">
                                                              <img class="h-6 w-6 object-cover rounded-md"
                                                                    src="{{ option.value ?: 'images/placeholder.png' | asset }}"
                                                                    alt="{{ option.name }}"/>
                                                          </a>
                                                      </div>
                                                  {% elseif option.color %}
                                                    <span title="{{option.value}}" class="inline-block h-6 w-6 rtl:mr-2 ltr:ml-2 rounded-full shadow" style="background-color: {{ option.color }}" ></span>
                                                  {% elseif option.is_file %}
                                                    <a href="{{ option.value }}" target="_blank" class="text-sm text-primary underline">{{ trans('pages.products.attachments') }}</a>
                                                  {% elseif option.is_color_picker %}
                                                    <div class="flex w-fit border border-gray-100 rounded">
                                                        <div class="flex-center ltr:border-r rtl:border-l border-gray-100 ltr:pr-1 rtl:pl-1">
                                                            <span class="text-xs px-1">{{option.value}}</span>
                                                            <span class="inline-block h-4 rtl:mr-1 ltr:ml-1 rounded p-1 border border-gray-100 shadow" style="background-color: {{option.value}};width: 1rem"></span>
                                                        </div>
                                                        <salla-button onclick="app.copyToClipboard(event)" shape="link" data-content="{{option.value}}" class="pt-1 px-1">
                                                            <i class="sicon-swap-stroke pointer-events-none text-sm text-gray-500"></i>
                                                        </salla-button>
                                                    </div>
                                                  {% elseif option.is_map %}
                                                    <salla-map
                                                        id="location_map"
                                                        class="map_{{option.id}}"
                                                        zoom="15"
                                                        readonly
                                                        lat="{{option.latitude}}"
                                                        lng="{{option.longitude}}"
                                                        >
                                                          <div slot="button">
                                                            <button type="button" class="text-sm text-primary" onclick="document.querySelector('salla-map.map_{{option.id}}').open()">
                                                              {{ trans('pages.products.show_location') }}
                                                            </button>
                                                          </div>
                                                    </salla-map>
                                                  {% else %}
                                                    {{ option.value }}
                                                  {% endif %}
                                              </dd>
                                          </div>
                                      {% endfor %}
                                  </dl>
                              </div>
                            {% endif %}

                            {% if item.sub_products %}
                                <h2 class="mb-3.5 text-sm font-bold">{{ trans('pages.orders.sub_products') }}</h2>
                                <div class="flow-root rounded-md px-4 border border-gray-200">
                                    <dl class="text-sm text-dark divide-y divide-border-color">
                                        {% for product in item.sub_products %}
                                            <div class="py-3.5 md:flex items-center justify-between">
                                                <dt class="mb-2 md:mb-0">
                                                    <a href="{{ product.url }}">
                                                        {{ product.name }}
                                                    </a>
                                                </dt>
                                            </div>
                                        {% endfor %}
                                    </dl>
                                </div>
                            {% endif %}
                            {% if item.note %}
                                <p class="mt-3 text-sm text-gray-500">
                                    <span>{{ trans('common.elements.note') }} : </span>{{ item.note }}
                                </p>
                            {% endif %}

                            {% if item.attachments %}
                                <div class="flow-root rounded-md px-4 border border-gray-200">
                                    <dl class="text-sm text-dark divide-y divide-border-color">
                                        {% for attachment in item.attachments %}
                                            <div class="py-3.5 md:flex items-center justify-between">
                                                <dt class="mb-2 md:mb-0">
                                                    {{ trans('pages.products.attachments') }}
                                                </dt>
                                                <dd class="font-medium {{ attachment.type == 'image' ? 'text-gray-900' : 'text-xs' }}">
                                                    <a href="{{ attachment.url }}">
                                                        {% if attachment.type == 'image' %}
                                                            <div class="flex rtl:space-x-reverse space-x-2.5">
                                                                <a href="{{ attachment.url }}" target="_blank">
                                                                    <img class="h-7 w-7 object-cover rounded-md"
                                                                         src="{{ attachment.url }}"
                                                                         alt="{{ item.name }}"/>
                                                                </a>
                                                            </div>
                                                        {% else %}
                                                            {{ trans('pages.orders.file_url') }}
                                                        {% endif %}
                                                    </a>
                                                </dd>
                                            </div>
                                        {% endfor %}
                                    </dl>
                                </div>
                            {% endif %}
                            
                            {% if item.rating %}
                                <section class="flex justify-between items-start border-t mt-10 pt-8">
                                    <div>
                                        <p>{{ item.rating.content|raw }}</p>
                                        {% if item.rating.is_pending %}
                                            <span>{{ trans('pages.rating.pending') }}</span>
                                        {% endif %}
                                        <salla-rating-stars with-label size="small" value="{{item.rating.stars}}"></salla-rating-stars>
                                        {% if item.rating.images|length %}
                                            <div class="flex items-center gap-2 mt-2">
                                                {% for image in item.rating.images %}
                                                    <img class="w-20 h-20 object-cover rounded-md" src="{{image}}"/>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="rating-actions">
                                        {% if store.settings.rating.allow_update %}
                                            <div class="tooltip-toggle tooltip-toggle--hover icon-trigger mobile-shifted">
                                                {% if not item.rating.can_update %}
                                                    <div class="tooltip-content shadow-md rounded">
                                                        <small> {{trans('pages.order.edit_period_ended')}}.</small>
                                                    </div>
                                                {% endif %}
                                                <salla-button 
                                                    loader-position="center" 
                                                    {% if not item.rating.can_update %} disabled {% endif %} 
                                                    shape="icon" 
                                                    fill="outline" 
                                                    color="dark" 
                                                    size="medium" 
                                                    {% if item.rating.can_update %} 
                                                        onclick="salla.event.dispatch('rating::edit', {type: 'product', feedback_id: {{item.rating.id}} })"
                                                    {% endif %} 
                                                    >
                                                    <i class="sicon-format-border-color text-xl"></i>
                                                </salla-button>
                                            </div>
                                            <div class="tooltip-toggle tooltip-toggle--hover icon-trigger mobile-shifted">
                                                {% if not item.rating.can_update %}
                                                    <div class="tooltip-content shadow-md rounded">
                                                        <small> {{trans('pages.order.delete_period_ended')}}.</small>
                                                    </div>
                                                {% endif %}
                                                <salla-button 
                                                    loader-position="center" 
                                                    shape="icon" 
                                                    {% if not item.rating.can_update %} disabled {% endif %} 
                                                    color="danger" 
                                                    fill="outline" 
                                                    size="medium" 
                                                    {% if item.rating.can_update %}
                                                        onclick="salla.event.dispatch('rating::delete', {feedback_id: {{item.rating.id}} })"
                                                    {% endif %} 
                                                    >
                                                    <i class="sicon-trash-2 text-xl"></i>
                                                </salla-button>
                                            </div>
                                        {% endif %}
                                    </div>
                                </section>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            {% endfor %}

            {% if order.options|length %}
              <div class="bg-white p-5 rounded-md">
                <h2 class="mb-3.5 text-sm font-bold">{{ trans('pages.cart.cart_options') }}</h2>
                <table class="table-fixed w-full border border-gray-200">
                  <thead>
                    <tr class="border-b border-gray-200">
                      <th scope="col" class="p-3 text-start text-sm">
                        {{ trans('pages.order.option_name') }}
                      </th>
                      <th scope="col" class="p-3 text-sm">
                        {{ trans('pages.order.option_content') }}
                      </th>
                      <th scope="col" class="p-3 rtl:text-left ltr:text-right text-sm">
                        {{ trans('pages.products.price') }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for item in order.options %}
                      {% for option in item.options %}
                        <tr class="border-b last:border-b-0 border-gray-200">
                          <td class="text-sm p-3">{{ option.name }} :</td>
                          <td class="text-center p-3 font-medium text-xs">
                          {% if option.reservations %}
                            {% include 'pages.partials.product.reservations' with { reservations: option.reservations } %}
                          {% else %}
                            {{ option.value }}
                          {% endif %}
                          </td>
                          <td class="rtl:text-start ltr:text-end p-3 font-medium text-xs"><strong>{{option.price|money}}</strong></td>
                        </tr>
                      {% endfor %}
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            {% endif %} 
        </div>

        <div class="bg-white rounded-md px-6">
            <h2 id="summary-heading" class="sr-only">Order summary</h2>
            <div class="flow-root">
                <dl class="text-sm text-gray-500 divide-y divide-border-color">
                    <div class="py-5 center-between">
                        <dt class="text-gray-600">
                            {{ trans('pages.cart.items_total') }}
                        </dt>
                        <dd class="font-medium">
                            {{ order.sub_total|money }}
                        </dd>
                    </div>

                    {% if order.options|length %}
                      <div class="py-5 center-between">
                        <dt class="text-gray-600">
                          {{ trans('pages.cart.order_options_total') }}
                        </dt>
                        <dd class="font-medium">
                          {{ order.options_total|money }}
                        </dd>
                      </div>
                    {% endif %}

                    {% for discount in order.discounts %}
                        <div class="py-5 center-between">
                            <dt class="discount">
                                {{ discount.name }}
                            </dt>
                            <dd class="font-medium">
                                {{ discount.discount }}
                            </dd>
                        </div>
                    {% endfor %}
                    {# Cash On Delivery #}
                    {% if order.cod_cost %}
                        <div class="py-5 center-between">
                            <dt class="">
                                {{ trans('pages.orders.cod_cost') }}
                            </dt>
                            <dd class="font-medium">
                                {{ order.cod_cost|money }}
                            </dd>
                        </div>
                    {% endif %}
                    {% if order.shipping_cost %}
                        <div class="py-5 center-between">
                            <dt class="shipping">
                                {{ trans('pages.orders.shipping_cost') }}
                            </dt>
                            <dd class="font-medium">
                                {{ order.shipping_cost|money }}
                            </dd>
                        </div>
                    {% endif %}
                    {% if order.tax %}
                        <div class="py-5 center-between">
                            <dt class="">
                                {{ trans('pages.cart.tax') }} ({{ order.tax.percent }}%)
                            </dt>
                            <dd class="font-medium text-gray-900">
                                {{ order.tax.amount|money }}
                            </dd>
                        </div>
                    {% endif %}
                    <div class="bg-border-color rounded-md py-5 -mx-6 px-6 center-between">
                        <dt class="text-base font-medium">
                            {{ trans('pages.orders.final_total') }}
                        </dt>
                        <dd class="text-base font-medium ">
                            <b>{{ order.total|money }}</b>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        {# Reorder & Cancel #}
        {% if order.can_reorder or order.can_cancel or order.is_pending_payment %}
            <div class="bg-white rounded-md p-5 divide-y divide-border-color space-y-4">
                {% if order.can_reorder or order.is_pending_payment %}
                    <div class="{{order.is_pending_payment ? 'flex-center': 'center-between'}}">
                        {% if order.is_pending_payment %}
                            {% if order.pending_payment_ends_in == 0 %}
                                <span class="text-red-500">{{ trans('pages.orders.pending_payment_expired') }}</span>
                            {% else %}
                                <salla-button width="wide" href="{{ order.payment_url }}">
                                    <i class="sicon sicon-wallet rtl:ml-2 ltr:mr-2"></i>
                                    <span>{{ trans('pages.orders.finish_payment') }}</span>
                                </salla-button>
                            {% endif %}
                        {% else %}
                            <div class="text-sm">
                                <h3 class="font-bold text-lg leading-none mb-1">{{ trans('pages.orders.reorder') }}</h3>
                                <p class="text-gray-500">{{ trans('pages.orders.reorder_description') }}</p>
                            </div>
                            <salla-button shape="link" onclick="document.querySelector('#reorder-modal').open()">
                                <i class="sicon sicon-rotate rtl:ml-2 ltr:mr-2"></i>
                                <span>{{ trans('pages.orders.reorder') }}</span>
                            </salla-button>
                        {% endif %}
                    </div>
                {% endif %}

                {% if order.can_cancel %}
                    <div class="center-between pt-4">
                        <div class="text-sm">
                            <h3 class="font-bold text-lg leading-none mb-1">{{ trans('pages.orders.cancel') }}</h3>
                            <p class="text-gray-500">{{ trans('pages.order.order_cancelation_desc') }}</p>
                        </div>
                        <salla-button shape="link" color="danger" onclick="document.querySelector('#modal-order-cancel').open()">
                            <i class="sicon sicon-cancel-circle rtl:ml-2 ltr:mr-2"></i>
                            <span>{{ trans('pages.orders.cancel') }}</span>
                        </salla-button>
                    </div>
                {% endif %}
            </div>

            {# Reorder Modal #}
            {% if order.can_reorder %}
                <salla-modal width="sm" sub-title="{{ trans('pages.orders.reorder_confirmation')|raw }}"
                             icon="sicon-rotate" id="reorder-modal" icon-style="success"
                             modal-title="{{ trans('pages.orders.reorder') }}">
                    <div slot="footer" class="grid grid-cols-2 gap-3">
                        <salla-button id="btn-reorder" width="wide">
                            {{ trans('common.elements.ok') }}
                        </salla-button>

                        <salla-button class="w-full" fill="outline" color="light" onclick="document.querySelector('#reorder-modal').close()">
                            {{ trans('common.elements.cancel') }}
                        </salla-button>
                    </div>
                </salla-modal>
            {% endif %}

            {# Cancel Modal #}
            {% if order.can_cancel %}
                <salla-modal width="sm" sub-title="{{ trans('pages.orders.cancel_confirmation') }}"
                             icon="sicon-cancel" id="modal-order-cancel" icon-style="error"
                             modal-title="{{ trans('common.elements.warning') }}">
                             
                    <div slot="footer" class="grid grid-cols-2 gap-3">
                        <salla-button id="confirm-cancel" color="danger" class="w-full cursor-pointer">
                            {{ trans('common.elements.ok') }}
                        </salla-button>
                        <salla-button class="w-full" fill="outline" color="light" onclick="document.querySelector('#modal-order-cancel').close()">
                            {{ trans('common.elements.cancel') }}
                        </salla-button>
                    </div>
                </salla-modal>
            {% endif %}
        {% endif %}

        {# Store Rating #}
        {% if order.rating.store.stars %}
            <section class="flex justify-between items-start bg-white p-5 rounded-md">
                <div>
                    <h2 class="text-lg font-bold mb-5">{{ trans('pages.rating.rate_the_store') }}</h2>
                    <p class="mb-2">{{ order.rating.store.content|raw }}</p>
                    <salla-rating-stars with-label value="{{order.rating.store.stars}}"></salla-rating-stars>
                </div>
                <div class="rating-actions">
                    {% if store.settings.rating.allow_update %}
                        <div class="tooltip-toggle tooltip-toggle--hover icon-trigger mobile-shifted">
                            {% if not order.rating.store.can_update %}
                                <div class="tooltip-content shadow-md rounded">
                                    <small> {{trans('pages.order.edit_period_ended')}}.</small>
                                </div>
                            {% endif %}
                            <salla-button 
                                loader-position="center" 
                                {% if not order.rating.store.can_update %} disabled {% endif %} 
                                shape="icon" 
                                fill="outline" 
                                color="dark" 
                                size="medium" 
                                {% if order.rating.store.can_update %}
                                    onclick="salla.event.dispatch('rating::edit', {type: 'store', feedback_id: {{order.rating.store.id}} })"
                                {% endif %} 
                                >
                                <i class="sicon-format-border-color text-xl"></i>
                            </salla-button>
                        </div>
                        <div class="tooltip-toggle tooltip-toggle--hover icon-trigger mobile-shifted">
                            {% if not order.rating.store.can_update %}
                                <div class="tooltip-content shadow-md rounded">
                                    <small> {{trans('pages.order.delete_period_ended')}}.</small>
                                </div>
                            {% endif %}
                            <salla-button 
                                loader-position="center" 
                                shape="icon" 
                                {% if not order.rating.store.can_update %} disabled {% endif %} 
                                color="danger" 
                                fill="outline" 
                                size="medium" 
                                {% if order.rating.store.can_update %} 
                                    onclick="salla.event.dispatch('rating::delete', {feedback_id: {{order.rating.store.id}} })" 
                                {% endif %} 
                                >
                                <i class="sicon-trash-2 text-xl"></i>
                            </salla-button>
                        </div>
                    {% endif %}
                </div>
            </section>
        {% endif %}

        {# Shipping Rating #}
        {% if order.rating.shipping.stars %}
            <section class="flex justify-between items-start bg-white p-5 rounded-md">
                <div>
                    <h2 class="text-lg font-bold mb-5">{{ trans('pages.rating.rate_shipping') }}</h2>
                    <p class="mb-2">{{ order.rating.shipping.content|raw }}</p>
                    <salla-rating-stars with-label value="{{order.rating.shipping.stars}}"></salla-rating-stars>
                </div>
                <div class="rating-actions">
                {% if store.settings.rating.allow_update %}
                    <div class="tooltip-toggle tooltip-toggle--hover icon-trigger mobile-shifted">
                        {% if not order.rating.shipping.can_update %}
                            <div class="tooltip-content shadow-md rounded">
                                <small> {{trans('pages.order.edit_period_ended')}}.</small>
                            </div>
                        {% endif %}
                        <salla-button 
                            loader-position="center" 
                            {% if not order.rating.shipping.can_update %} disabled {% endif %} 
                            shape="icon" 
                            fill="outline" 
                            color="dark" 
                            size="medium" 
                            {% if order.rating.shipping.can_update %} 
                                onclick="salla.event.dispatch('rating::edit', {type: 'shipping', feedback_id: {{order.rating.shipping.id}} })" 
                            {% endif %}
                            >
                            <i class="sicon-format-border-color text-xl"></i>
                        </salla-button>
                    </div>
                    <div class="tooltip-toggle tooltip-toggle--hover icon-trigger mobile-shifted">
                        {% if not order.rating.shipping.can_update %}
                            <div class="tooltip-content shadow-md rounded">
                                <small> {{trans('pages.order.delete_period_ended')}}.</small>                            
                            </div>
                        {% endif %}
                        <salla-button 
                            loader-position="center" 
                            shape="icon" 
                            {% if not order.rating.shipping.can_update %} disabled {% endif %} 
                            color="danger" 
                            fill="outline" 
                            size="medium" 
                            {% if order.rating.shipping.can_update %} 
                                onclick="salla.event.dispatch('rating::delete', {feedback_id: {{order.rating.shipping.id}} })" 
                            {% endif %} 
                            > 
                            <i class="sicon-trash-2 text-xl"></i>
                        </salla-button>
                    </div>
                {% endif %}
                </div>
            </section>
        {% endif %}

        {% hook 'customer:orders.single.details.end' %}

        {% if order.can_rate or store.settings.rating.allow_update %} 
            <salla-rating-modal></salla-rating-modal>
        {% endif %}
    </div>
{% endblock %}

{% block scripts %}
    <script defer src="{{ 'order.js' | asset }}"></script>
{% endblock %}
