{#
| Variable                  | Type                      | Description |
|---------------------------|---------------------------|-------------|
| page                      | object                    |             |
| page.title                | string                    |             |
| page.slug                 | string                    |             |
| notifications             | Notification[] *Paginator |             |
| notifications.next_page   | ?string                   |             |
| notifications.count       | int                       |             |
| notifications[].is_new    | Bool                      |             |
| notifications[].url       | string                    |             |
| notifications[].sub_title | string                    |             |
| notifications[].title     | string                    |             |
| notifications[].date      | string                    |             |
#}
{% extends "layouts.customer" %}
{% block inner_content %}
	{% hook 'customer:notifications.items.start' %}

	<salla-notifications></salla-notifications>

	{% hook 'customer:notifications.items.end' %}
{% endblock %}
