{#
| Variable            | Type     | Description                                     |
|---------------------|----------|-------------------------------------------------|
| component.title     | string   | Section title                                   |
| component.icon      | array    | Array of icons to display                       |
| component.icon[].image | string | Icon image URL                                 |
| component.icon[].title | string | Icon title                                     |
| component.icon[].url   | object | Icon link configuration                        |
#}

{# Styles moved to separate SCSS file: src/assets/styles/04-components/second-gallery.scss #}
<!-- All styles moved to separate SCSS file for better performance and maintainability -->


{% set component_class = component_class|default('s-block') %}
{% set title = component.title %}
{% set icon_items = component.icon %}
{% set has_slider = icon_items|length > 3 %}
{% set unique_id = 'gallery-' ~ random() %}

<section class="gaming-gallery-section {{ component_class }}" id="{{ unique_id }}" style="width: 100%; margin: 0; padding-left: 0; padding-right: 0;">
    <div class="gaming-gallery-container">
        <div class="gaming-gallery-title-wrapper">
            <h2 class="gaming-gallery-title" data-text="{{ title }}" style="visibility: visible !important; opacity: 1 !important; display: inline-block !important;">{{ title }}</h2>
            <div class="gaming-title-underline"></div>
        </div>

        <div class="gaming-gallery-slider {% if has_slider %}with-slider{% endif %}" id="gaming-gallery-slider-{{ unique_id }}">
            <div class="gaming-gallery-track">
                {% for item in icon_items %}
                    {% set item_url = null %}
                    {% if item.url is defined and item.url %}
                        {% if item.url.type is defined %}
                            {% set item_url = item.url.url %}
                        {% else %}
                            {% set item_url = item.url %}
                        {% endif %}
                    {% endif %}

                    <div class="gaming-gallery-item">
                        {% if item_url %}
                            <a href="{{ item_url }}" class="gaming-gallery-link" aria-label="انتقل إلى {{ item.title }}" target="_blank" rel="noopener">
                        {% endif %}

                        <div class="gaming-gallery-image-wrapper">
                            <div class="gaming-gallery-image-glow"></div>
                            <div class="gaming-gallery-image-container">
                                <img src="{{ item.image }}"
                                     alt="{{ item.title }}"
                                     class="gaming-gallery-image"
                                     decoding="async"
                                     width="400"
                                     height="250"
                                     style="opacity: 1 !important; visibility: visible !important; display: block !important;">
                            </div>
                            {% if item_url %}
                                <div class="gaming-gallery-overlay">
                                    <div class="gaming-overlay-content">
                                        <i class="sicon-link gaming-gallery-link-icon"></i>
                                        <span class="gaming-overlay-text">عرض التفاصيل</span>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                        <h3 class="gaming-gallery-item-title">{{ item.title }}</h3>

                        {% if item_url %}
                            </a>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
            
            {% if has_slider %}
                <div class="gaming-gallery-nav">
                    <button class="gaming-gallery-prev-btn"
                            aria-label="الشريحة السابقة"
                            data-gallery="{{ unique_id }}">
                        <i class="sicon-chevron-left"></i>
                    </button>
                    <div class="gaming-gallery-dots">
                        {% for i in 0..((icon_items|length / 3)|round(0, 'ceil') - 1) %}
                            <button class="gaming-gallery-dot {% if loop.first %}active{% endif %}"
                                    data-index="{{ i }}"
                                    data-gallery="{{ unique_id }}"
                                    aria-label="انتقل إلى الشريحة {{ i + 1 }}"></button>
                        {% endfor %}
                    </div>
                    <button class="gaming-gallery-next-btn"
                            aria-label="الشريحة التالية"
                            data-gallery="{{ unique_id }}">
                        <i class="sicon-chevron-right"></i>
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</section>

{# Load optimized JavaScript #}
<script src="{{ 'assets/js/components/second-gallery.js' | asset }}" defer></script>


