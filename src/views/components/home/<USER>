{#
| Variable       | Type   | Description                  |
|----------------|--------|------------------------------|
| component      | object | Main component data          |
| offer          | object | Offer data (fallback)        |
| timer          | object | Timer data (fallback)        |
#}

{# إعدادات افتراضية #}
{% set config = {
    bg_color: '#1DE9B6',
    days: 5,
    overlay_opacity: '0.7',
    button_text: 'اضغط للعرض',
    button_link: '#',
    image_fit: 'contain',
    timer_enabled: true,
    timer_text_color: '#ffffff',
    offer_button_enabled: true,
    offer_bg_color: '#f5f5f5',
    offer_animation: 'fadeIn'
} %}

{# استخراج بيانات التايمر من الإعدادات - طريقة صحيحة لقراءة collection data #}
{% set timer_data = component.timer[0]|default({}) %}
{# قراءة البيانات المتداخلة بالطريقة الصحيحة #}
{% if timer_data['timer.bg'] is defined and timer_data['timer.bg'] %}
    {% set config = config|merge({bg_color: timer_data['timer.bg']}) %}
{% endif %}
{% if timer_data['timer.days'] is defined and timer_data['timer.days'] %}
    {% set config = config|merge({days: timer_data['timer.days']}) %}
{% endif %}

{# Debug: طباعة البيانات للتأكد من القراءة الصحيحة #}
{# {{ dump(timer_data) }} #}

{# استخراج بيانات العرض من الإعدادات - طريقة صحيحة لقراءة collection data #}
{% set offer_data = component.offer[0]|default({}) %}
{% set offer_img = offer_data['offer.img']|default(null) %}
{% set offer_url = offer_data['offer.url']|default('#') %}

{# Debug: طباعة البيانات للتأكد من القراءة الصحيحة #}
{# {{ dump(offer_data) }} #}

{# إعدادات عامة من الكومبوننت - قراءة الحقول المباشرة #}
{% if component.timer_enabled is defined %}
    {% set config = config|merge({timer_enabled: component.timer_enabled}) %}
{% endif %}

{% if component.timer_text_color is defined and component.timer_text_color %}
    {% set config = config|merge({timer_text_color: component.timer_text_color}) %}
{% endif %}

{% if component.offer_button_enabled is defined %}
    {% set config = config|merge({offer_button_enabled: component.offer_button_enabled}) %}
{% endif %}

{% if component.offer_button_text is defined and component.offer_button_text|length > 0 %}
    {% set config = config|merge({button_text: component.offer_button_text}) %}
{% endif %}

{% if component.offer_bg_color is defined and component.offer_bg_color %}
    {% set config = config|merge({offer_bg_color: component.offer_bg_color}) %}
{% endif %}

{% if component.offer_animation is defined and component.offer_animation %}
    {% set config = config|merge({offer_animation: component.offer_animation}) %}
{% endif %}

{% set unique_id = component.id|default('offer-' ~ random()) %}

<section class="s-block s-block--banner-with-offer container">
    {% if component.title %}
        <div class="s-block__title">
            <div class="right-side">
                <h2>{{ component.title }}</h2>
            </div>
        </div>
    {% endif %}

    <div class="banner-offer-wrapper"
         id="offer-component-{{ unique_id }}"
         data-timer-days="{{ config.days }}"
         data-timer-bg="{{ config.bg_color }}"
         data-timer-text-color="{{ config.timer_text_color }}"
         data-offer-button-enabled="{{ config.offer_button_enabled ? 'true' : 'false' }}"
         data-offer-button-text="{{ config.button_text }}"
         data-timer-enabled="{{ config.timer_enabled ? 'true' : 'false' }}"
         data-offer-bg-color="{{ config.offer_bg_color }}"
         data-offer-animation="{{ config.offer_animation }}"
         style="--offer-bg-color: {{ config.offer_bg_color }}; --timer-bg-color: {{ config.bg_color }}; --timer-text-color: {{ config.timer_text_color }};">
        <div class="offer-container {{ config.offer_animation }}"
             style="background-color: var(--offer-bg-color);">
            {# Banner Image #}
            {% if offer_img and offer_img != '' %}
                <img src="{{ offer_img }}" alt="Special Offer"
                     class="banner-image {{ config.image_fit == 'cover' ? 'cover-mode' : (config.image_fit == 'fill' ? 'fill-mode' : (config.image_fit == 'scale-down' ? 'scale-down-mode' : '')) }}"
                     decoding="async"
                     style="opacity: 1 !important; visibility: visible !important; display: block !important;">
            {% else %}
                <div class="banner-image placeholder-banner">
                    <div class="placeholder-content">
                        <div class="placeholder-icon">🎁</div>
                        <h3>عرض خاص</h3>
                        <p>قم بإضافة صورة العرض من لوحة التحكم</p>
                    </div>
                </div>
            {% endif %}

            {# Overlay #}
            <div class="overlay"></div>

            {# Optimized particles #}
            <div class="particles">
                <div class="particle particle-1"></div>
                <div class="particle particle-2"></div>
                <div class="particle particle-3"></div>
            </div>

            {# محتوى العرض #}
            <div class="offer-content">
                {% if config.timer_enabled %}
                    <h3 class="offer-title">العرض ينتهي خلال:</h3>
                    {# Countdown Timer #}
                    <div id="countdown-timer-{{ unique_id }}" class="countdown-timer"
                         style="color: {{ config.timer_text_color }};">
                        <div class="countdown-box">
                            <span id="days-{{ unique_id }}" class="countdown-value">00</span>
                            <span class="countdown-label">أيام</span>
                        </div>
                        <div class="countdown-box">
                            <span id="hours-{{ unique_id }}" class="countdown-value">00</span>
                            <span class="countdown-label">ساعات</span>
                        </div>
                        <div class="countdown-box">
                            <span id="minutes-{{ unique_id }}" class="countdown-value">00</span>
                            <span class="countdown-label">دقائق</span>
                        </div>
                        <div class="countdown-box">
                            <span id="seconds-{{ unique_id }}" class="countdown-value">00</span>
                            <span class="countdown-label">ثواني</span>
                        </div>
                    </div>
                    {# Offer ended badge #}
                    <div id="offer-ended-badge-{{ unique_id }}" class="offer-ended-badge">
                        <div class="badge-pattern"></div>
                        <span class="badge-text">نهاية<br>العرض</span>
                    </div>
                {% endif %}

                {# زر العرض #}
                {% if config.offer_button_enabled %}
                    <a href="{{ offer_url }}" class="offer-cta-button">
                        <span class="button-text">{{ config.button_text }}</span>
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</section>

{# JavaScript Configuration for Banner Component #}
<script>
(function() {
    'use strict';

    // Initialize global banner config if not exists
    if (!window.bannerOfferConfig) {
        window.bannerOfferConfig = {};
    }

    // Configuration for this specific banner instance
    window.bannerOfferConfig['{{ unique_id }}'] = {
        // Timer settings
        timer_enabled: {{ config.timer_enabled ? 'true' : 'false' }},
        timer_bg_color: '{{ config.bg_color }}',
        timer_text_color: '{{ config.timer_text_color }}',
        timer_days: {{ config.days }},

        // Offer settings
        offer_button_enabled: {{ config.offer_button_enabled ? 'true' : 'false' }},
        offer_button_text: '{{ config.button_text|e('js') }}',
        offer_bg_color: '{{ config.offer_bg_color }}',
        offer_animation: '{{ config.offer_animation }}',

        // Additional settings
        unique_id: '{{ unique_id }}',
        offer_img: '{{ offer_img|default('') }}',
        offer_url: '{{ offer_url|default('#') }}',

        // Fallback properties for compatibility
        bg_color: '{{ config.bg_color }}',
        days: {{ config.days }},
        button_text: '{{ config.button_text|e('js') }}'
    };

    // Apply colors immediately and forcefully
    const bannerElement = document.getElementById('offer-component-{{ unique_id }}');
    if (bannerElement) {
        // Set CSS variables
        bannerElement.style.setProperty('--timer-bg-color', '{{ config.bg_color }}');
        bannerElement.style.setProperty('--timer-text-color', '{{ config.timer_text_color }}');
        bannerElement.style.setProperty('--offer-bg-color', '{{ config.offer_bg_color }}');
        bannerElement.style.setProperty('--gaming-primary', '{{ config.bg_color }}');

        // Apply colors directly to countdown boxes
        const countdownBoxes = bannerElement.querySelectorAll('.countdown-box');
        countdownBoxes.forEach(box => {
            box.style.setProperty('background-color', '{{ config.bg_color }}', 'important');
            box.style.setProperty('border-color', '{{ config.bg_color }}', 'important');

            const value = box.querySelector('.countdown-value');
            const label = box.querySelector('.countdown-label');
            if (value) value.style.setProperty('color', '{{ config.timer_text_color }}', 'important');
            if (label) label.style.setProperty('color', '{{ config.timer_text_color }}', 'important');
        });

        // Update button text immediately
        const offerButton = bannerElement.querySelector('.offer-cta-button');
        if (offerButton) {
            {% if not config.offer_button_enabled %}
                offerButton.style.display = 'none';
            {% else %}
                offerButton.style.display = '';
                const buttonText = offerButton.querySelector('.button-text');
                if (buttonText) {
                    buttonText.textContent = '{{ config.button_text|e('js') }}';
                }
            {% endif %}
        }

        // Hide/show timer based on settings
        {% if not config.timer_enabled %}
            const timerSection = bannerElement.querySelector('.countdown-timer');
            if (timerSection && timerSection.parentElement) {
                timerSection.parentElement.style.display = 'none';
            }
        {% endif %}

        console.log('🎨 All settings applied immediately: bg={{ config.bg_color }}, text={{ config.timer_text_color }}, button={{ config.button_text }}');
    }

    console.log('🎯 Banner configuration loaded for:', '{{ unique_id }}', window.bannerOfferConfig['{{ unique_id }}']);
    console.log('📊 Raw component data:', {
        timer: {{ component.timer|json_encode|raw }},
        offer: {{ component.offer|json_encode|raw }},
        timer_enabled: {{ component.timer_enabled|json_encode|raw }},
        timer_text_color: '{{ component.timer_text_color }}',
        offer_button_enabled: {{ component.offer_button_enabled|json_encode|raw }},
        offer_button_text: '{{ component.offer_button_text }}',
        offer_bg_color: '{{ component.offer_bg_color }}',
        offer_animation: '{{ component.offer_animation }}'
    });
    console.log('🔧 Processed config:', {
        bg_color: '{{ config.bg_color }}',
        days: {{ config.days }},
        timer_enabled: {{ config.timer_enabled ? 'true' : 'false' }},
        timer_text_color: '{{ config.timer_text_color }}',
        button_text: '{{ config.button_text }}',
        offer_button_enabled: {{ config.offer_button_enabled ? 'true' : 'false' }},
        offer_bg_color: '{{ config.offer_bg_color }}',
        offer_animation: '{{ config.offer_animation }}'
    });
    console.log('🎨 Timer data extracted:', {
        timer_bg: '{{ timer_data['timer.bg']|default('NOT_FOUND') }}',
        timer_days: {{ timer_data['timer.days']|default('NOT_FOUND') }},
        offer_img: '{{ offer_img|default('NOT_FOUND') }}',
        offer_url: '{{ offer_url|default('NOT_FOUND') }}'
    });
})();
</script>