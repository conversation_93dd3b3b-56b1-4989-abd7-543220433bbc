{#
| Variable                       | Type    | Description                                                                  |
|--------------------------------|---------|------------------------------------------------------------------------------|
| component                      | object  | Contains merchant settings for fields from twilight.json `component` section |
| component.title                | string  | Main title for the FAQ section                                              |
| component.description          | string  | Description text below the title                                            |
| component.questions            | array   | Array of FAQ questions and answers                                          |
| component.title_color          | string  | Color for the main title                                                     |
| component.question_color       | string  | Color for the question texts                                                 |
| position                       | int     | Sorting number start from zero                                               |
#}

<section class="s-block s-block--faq whatsapp-faq-section" id="faq-section-{{ position }}">
    {# Header Section #}
    {% if component.title or component.description %}
        <div class="s-block__title whatsapp-faq-header">
            {% if component.title %}
                <h2 class="whatsapp-title">
                    {{ component.title }}
                </h2>
            {% endif %}
            {% if component.description %}
                <p class="whatsapp-description">
                    {{ component.description }}
                </p>
            {% endif %}
        </div>
    {% endif %}

    {# FAQ Accordion #}
    {% if component.questions and component.questions|length > 0 %}
        <div class="faq-accordion whatsapp-faq-accordion">
            {% for faq in component.questions %}
                <div class="faq-item whatsapp-faq-item">
                    <button class="faq-question whatsapp-faq-question"
                            aria-expanded="false"
                            aria-controls="faq-answer-{{ position }}-{{ loop.index0 }}">
                        <span class="whatsapp-question-text"
                              style="color: {{ component.question_color|default('#25D366') }}">
                            {{ faq.question }}
                        </span>
                        <span class="faq-icon whatsapp-faq-icon">
                            <i class="sicon-keyboard_arrow_down"></i>
                        </span>
                    </button>
                    <div class="faq-answer whatsapp-faq-answer hidden"
                         id="faq-answer-{{ position }}-{{ loop.index0 }}">
                        <div>
                            <p class="whatsapp-answer-text">
                                {{ faq.answer }}
                            </p>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="whatsapp-no-content">
            <div class="whatsapp-empty-icon">
                <i class="sicon-help-circle"></i>
            </div>
            <p class="whatsapp-empty-text">لا توجد أسئلة شائعة متاحة حالياً</p>
        </div>
    {% endif %}
</section>



