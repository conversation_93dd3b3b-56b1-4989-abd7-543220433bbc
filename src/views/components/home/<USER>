{#
| Variable                | Type     | Description                                                                  |
|-------------------------|----------|------------------------------------------------------------------------------|
| component               | object   | Contains merchant settings for fields from twilight.json `component` section |
| component.video_url     | string   | Media URL (YouTube, Vimeo, direct video, GIF, or image link)                |
| component.title         | string   | Main title to display on media                                              |
| component.subtitle      | string   | Subtitle/description to display under main title                            |
| component.button_text   | string   | Text to display on the button                                               |
| component.button_url    | object   | Button link (variable-list format)                                          |
| position                | int      | Component position for unique IDs                                           |
#}

{% set video_url = component.video_url %}
{% set title = component.title %}
{% set subtitle = component.subtitle %}
{% set button_text = component.button_text %}
{% set button_url = component.button_url %}
{% set unique_id = 'video-banner-' ~ position %}

{# Extract video ID and determine media type #}
{% set video_id = null %}
{% set media_type = 'direct' %}
{% set embed_url = video_url %}

{# Determine media type based on URL patterns #}
{% set url_lower = video_url|lower %}

{# Enhanced media type detection with better GIF support #}
{% if video_url %}
    {# GIF image processing - more comprehensive detection #}
    {% if '.gif' in url_lower or 'giphy.com' in url_lower or 'tenor.com' in url_lower or 'media.giphy.com' in url_lower or 'gfycat.com' in url_lower %}
        {% set media_type = 'gif' %}
    {# YouTube URL processing #}
    {% elseif 'youtube.com' in video_url or 'youtu.be/' in video_url %}
        {% if 'youtube.com/watch' in video_url %}
            {% set video_id = video_url|split('v=')|last|split('&')|first %}
        {% elseif 'youtube.com/shorts/' in video_url %}
            {% set video_id = video_url|split('shorts/')|last|split('?')|first %}
        {% elseif 'youtu.be/' in video_url %}
            {% set video_id = video_url|split('youtu.be/')|last|split('?')|first %}
        {% endif %}
        {% set media_type = 'youtube' %}
        {% set embed_url = 'https://www.youtube-nocookie.com/embed/' ~ video_id ~ '?autoplay=1&mute=1&loop=1&playlist=' ~ video_id ~ '&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&enablejsapi=1&origin=' ~ app.request.getSchemeAndHttpHost() %}
    {# Vimeo URL processing #}
    {% elseif 'vimeo.com/' in video_url %}
        {% set video_id = video_url|split('vimeo.com/')|last|split('?')|first|split('/')|first %}
        {% set media_type = 'vimeo' %}
        {% set embed_url = 'https://player.vimeo.com/video/' ~ video_id ~ '?autoplay=1&muted=1&loop=1&background=1&controls=0&title=0&byline=0&portrait=0' %}
    {# Direct video file processing #}
    {% elseif '.mp4' in url_lower or '.webm' in url_lower or '.ogg' in url_lower or '.mov' in url_lower or '.avi' in url_lower %}
        {% set media_type = 'direct' %}
    {# Static image processing - comprehensive detection #}
    {% elseif '.jpg' in url_lower or '.jpeg' in url_lower or '.png' in url_lower or '.webp' in url_lower or '.bmp' in url_lower or '.svg' in url_lower %}
        {% set media_type = 'image' %}
    {# Fallback: if it contains common image hosting domains #}
    {% elseif 'imgur.com' in url_lower or 'unsplash.com' in url_lower or 'pexels.com' in url_lower %}
        {% if '.gif' in url_lower %}
            {% set media_type = 'gif' %}
        {% else %}
            {% set media_type = 'image' %}
        {% endif %}
    {# Default fallback #}
    {% else %}
        {% set media_type = 'image' %}
    {% endif %}
{% endif %}

{# Debug info - remove in production #}
<!-- Debug: URL={{ video_url }}, Type={{ media_type }}, ID={{ unique_id }} -->

<section class="s-block s-block--video-banner video-banner-section" id="{{ unique_id }}" data-media-type="{{ media_type }}" data-url="{{ video_url }}" data-lcp-optimized="true">
    {% if video_url %}
        <div class="video-banner-container">
            {# Media Background #}
            <div class="media-background">
                {% if media_type == 'gif' or media_type == 'image' %}
                    <div class="image-container">
                        <img
                            src="{{ video_url }}"
                            alt="{{ title ? title : 'Banner Image' }}"
                            class="banner-image {{ media_type == 'gif' ? 'gif-image' : 'static-image' }}"
                            {% if media_type == 'gif' %}
                                crossorigin="anonymous"
                                referrerpolicy="no-referrer-when-downgrade"
                            {% endif %}
                            loading="eager"
                            decoding="async">

                        {# Image loading indicator #}
                        <div class="image-loading">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل {{ media_type == 'gif' ? 'الصورة المتحركة' : 'الصورة' }}...</p>
                        </div>

                        {# Image error fallback #}
                        <div class="image-fallback" style="display: none;">
                            <div class="fallback-content">
                                <i class="sicon-image fallback-icon"></i>
                                <h3>فشل في تحميل {{ media_type == 'gif' ? 'الصورة المتحركة' : 'الصورة' }}</h3>
                                <p>يرجى التحقق من صحة الرابط أو المحاولة لاحقاً</p>
                                <a href="{{ video_url }}" target="_blank" class="fallback-link">
                                    عرض {{ media_type == 'gif' ? 'الصورة المتحركة' : 'الصورة' }} في نافذة جديدة
                                </a>
                            </div>
                        </div>
                    </div>
                {% elseif media_type == 'direct' %}
                    <video autoplay muted loop playsinline class="video-element" preload="metadata">
                        <source src="{{ video_url }}" type="video/mp4">
                        {% if video_url ends with '.webm' %}
                            <source src="{{ video_url }}" type="video/webm">
                        {% endif %}
                        {% if video_url ends with '.ogg' %}
                            <source src="{{ video_url }}" type="video/ogg">
                        {% endif %}
                        <p>متصفحك لا يدعم عرض الفيديو.</p>
                    </video>
                {% else %}
                    <div class="iframe-container">
                        <iframe
                            src="{{ embed_url }}"
                            class="video-iframe"
                            frameborder="0"
                            allow="autoplay; encrypted-media; fullscreen; picture-in-picture; accelerometer; gyroscope"
                            allowfullscreen
                            loading="lazy"
                            title="Video Banner">
                        </iframe>

                        {# Fallback for when iframe fails #}
                        <div class="iframe-fallback" style="display: none;">
                            <div class="fallback-content">
                                <i class="sicon-video fallback-icon"></i>
                                <h3>الفيديو غير متاح</h3>
                                <p>يرجى التحقق من رابط الفيديو أو المحاولة لاحقاً</p>
                                <a href="{{ video_url }}" target="_blank" class="fallback-link">
                                    مشاهدة الفيديو في موقعه الأصلي
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}

                {# Loading indicator #}
                <div class="video-loading">
                    <div class="loading-spinner"></div>
                    <p>جاري تحميل الفيديو...</p>
                </div>
            </div>

            {# Overlay #}
            <div class="video-overlay"></div>

            {# Content #}
            {% if title or subtitle or button_text %}
                <div class="video-content">
                    <div class="container">
                        <div class="video-text-content">
                            {% if title %}
                                <h1 class="video-title animate-element font-primary" style="font-display: swap;">{{ title }}</h1>
                            {% endif %}

                            {% if subtitle %}
                                <p class="video-subtitle animate-element">{{ subtitle }}</p>
                            {% endif %}

                            {% if button_text and button_url %}
                                <div class="video-button-container animate-element">
                                    <a href="{{ button_url }}" class="video-banner-button">
                                        <span class="button-text">{{ button_text }}</span>
                                        <span class="button-icon">
                                            <i class="sicon-arrow-{{ salla.config.get('theme.is_rtl') ? 'left' : 'right' }}"></i>
                                        </span>
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    {% else %}
        {# Fallback when no video URL is provided #}
        <div class="video-banner-placeholder">
            <div class="container">
                <div class="placeholder-content">
                    <i class="sicon-video placeholder-icon"></i>
                    <h3>بنر فيديو</h3>
                    <p>يرجى إضافة رابط الفيديو من إعدادات الكومبوننت</p>
                </div>
            </div>
        </div>
    {% endif %}
</section>



