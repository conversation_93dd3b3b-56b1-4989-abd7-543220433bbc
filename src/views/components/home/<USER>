{# معرض مع صور مصغرة - محسن مع إعدادات شاملة #}
{% set component_class = component_class|default('s-block py-0') %}
{% set title = component.title %}
{% set description = component.description %}
{% set banner = component.banner %}
{% set icon_items = component.icon %}
{% set unique_id = component.id|default('first-gallery-' ~ random()) %}

{# Get enhanced settings with defaults - handle both array and direct values #}
{% set gallery_layout = component.gallery_layout is iterable and component.gallery_layout[0] is defined ? component.gallery_layout[0].value : (component.gallery_layout|default('grid')) %}
{% set columns_count = component.columns_count|default(4) %}
{% set image_aspect_ratio = component.image_aspect_ratio is iterable and component.image_aspect_ratio[0] is defined ? component.image_aspect_ratio[0].value : (component.image_aspect_ratio|default('square')) %}
{% set image_spacing = component.image_spacing|default(15) %}
{% set enable_hover_effects = component.enable_hover_effects|default(true) %}
{% set hover_overlay_color = component.hover_overlay_color|default('#000000') %}
{% set hover_overlay_opacity = component.hover_overlay_opacity|default(30) %}
{% set image_border_radius = component.image_border_radius|default(8) %}
{% set enable_image_shadows = component.enable_image_shadows|default(true) %}
{% set enable_entrance_animations = component.enable_entrance_animations|default(true) %}
{% set animation_type = component.animation_type is iterable and component.animation_type[0] is defined ? component.animation_type[0].value : (component.animation_type|default('fadeIn')) %}
{% set animation_delay = component.animation_delay is iterable and component.animation_delay[0] is defined ? component.animation_delay[0].value : (component.animation_delay|default('short')) %}
{% set stagger_animations = component.stagger_animations|default(true) %}
{% set mobile_columns = component.mobile_columns|default(2) %}
{% set tablet_columns = component.tablet_columns|default(3) %}
{% set hide_on_mobile = component.hide_on_mobile|default(false) %}
{% set lazy_loading = component.lazy_loading|default(true) %}
{% set image_optimization = component.image_optimization|default(true) %}

{# Build CSS classes based on settings #}
{% set gallery_classes = 'gaming-gallery-showcase first-gallery-enhanced' %}
{% set gallery_classes = gallery_classes ~ ' layout-' ~ gallery_layout %}
{% if enable_hover_effects %}
    {% set gallery_classes = gallery_classes ~ ' hover-effects-enabled' %}
{% endif %}
{% if enable_entrance_animations %}
    {% set gallery_classes = gallery_classes ~ ' animations-enabled' %}
{% endif %}
{% set gallery_classes = gallery_classes ~ ' animation-' ~ animation_type ~ ' delay-' ~ animation_delay %}
{% if stagger_animations %}
    {% set gallery_classes = gallery_classes ~ ' stagger-enabled' %}
{% endif %}
{% if hide_on_mobile %}
    {% set gallery_classes = gallery_classes ~ ' hide-mobile' %}
{% endif %}

{# Debug output - remove in production #}
<!-- DEBUG: gallery_layout = {{ gallery_layout }}, animation_type = {{ animation_type }}, columns = {{ columns_count }} -->

<section class="{{ component_class }} {{ gallery_classes }}"
         id="first-gallery-{{ unique_id }}"
         data-component="first-gallery"
         data-gallery-layout="{{ gallery_layout }}"
         data-columns-count="{{ columns_count }}"
         data-image-aspect-ratio="{{ image_aspect_ratio }}"
         data-image-spacing="{{ image_spacing }}"
         data-enable-hover-effects="{{ enable_hover_effects ? 'true' : 'false' }}"
         data-hover-overlay-color="{{ hover_overlay_color }}"
         data-hover-overlay-opacity="{{ hover_overlay_opacity }}"
         data-image-border-radius="{{ image_border_radius }}"
         data-enable-image-shadows="{{ enable_image_shadows ? 'true' : 'false' }}"
         data-enable-entrance-animations="{{ enable_entrance_animations ? 'true' : 'false' }}"
         data-animation-type="{{ animation_type }}"
         data-animation-delay="{{ animation_delay }}"
         data-stagger-animations="{{ stagger_animations ? 'true' : 'false' }}"
         data-mobile-columns="{{ mobile_columns }}"
         data-tablet-columns="{{ tablet_columns }}"
         data-hide-on-mobile="{{ hide_on_mobile ? 'true' : 'false' }}"
         data-lazy-loading="{{ lazy_loading ? 'true' : 'false' }}"
         data-image-optimization="{{ image_optimization ? 'true' : 'false' }}"
         style="--columns-count: {{ columns_count }}; --image-spacing: {{ image_spacing }}px; --hover-overlay-color: {{ hover_overlay_color }}; --hover-overlay-opacity: {{ hover_overlay_opacity / 100 }}; --image-border-radius: {{ image_border_radius }}px; --mobile-columns: {{ mobile_columns }}; --tablet-columns: {{ tablet_columns }};">

    <div class="full-width-banner">
        <div class="game-gallery-banner"
            {% if banner %}style="background-image: url('{{ banner }}')"{% endif %}>
            
            <div class="game-gallery-content">
                {% if title %}
                    <div class="game-gallery-title">
                        <h2>{{ title }}</h2>
                    </div>
                {% endif %}
                
                {% if description %}
                    <div class="game-gallery-description">
                        <p>{{ description }}</p>
                    </div>
                {% endif %}
                
                {% if component.button_text %}
                    <div class="game-gallery-button">
                        <a href="{{ component.button_url|default('#') }}" class="game-cta-button">
                            {{ component.button_text }}
                        </a>
                    </div>
                {% endif %}
            </div>
            
            {% if icon_items|length > 0 %}
                <div class="game-gallery-icons enhanced-gallery-container">
                    <div class="game-cards-container gallery-grid aspect-ratio-{{ image_aspect_ratio }}">
                        {% for icon in icon_items %}
                            <div class="game-card gallery-item"
                                 data-index="{{ loop.index0 }}"
                                 data-animation-delay="{{ stagger_animations ? (loop.index0 * 100) : 0 }}ms">
                                {% set icon_url = icon.url|default(icon['icon.url'])|default(null) %}
                                {% set icon_image = icon.img|default(icon.image)|default(icon['icon.img'])|default(icon['icon.image'])|default(null) %}

                                {% if icon_url %}
                                    <a href="{{ icon_url }}" class="game-gallery-link gallery-link"
                                       aria-label="Gallery item {{ loop.index }}"
                                       rel="noopener"
                                       {% if icon_url starts with 'http' %}target="_blank"{% endif %}>
                                {% endif %}

                                <div class="game-gallery-icon gallery-image-container"
                                     role="img"
                                     aria-label="Gallery icon {{ loop.index }}"
                                     tabindex="0">
                                    {% if icon_image %}
                                        <img src="{{ icon_image }}"
                                             alt="Gallery Icon {{ loop.index }}"
                                             class="img-fluid gallery-image enhanced-gallery-image"
                                             decoding="async"
                                             {% if lazy_loading %}loading="lazy"{% endif %}
                                             style="opacity: 1 !important; visibility: visible !important; display: block !important;">
                                    {% else %}
                                        <div class="icon-placeholder gallery-placeholder" aria-hidden="true">
                                            <i class="sicon-image" aria-hidden="true"></i>
                                        </div>
                                    {% endif %}

                                    {% if icon_url and enable_hover_effects %}
                                        <div class="gallery-link-overlay hover-overlay" aria-hidden="true">
                                            <i class="sicon-link" aria-hidden="true"></i>
                                        </div>
                                    {% endif %}
                                </div>

                                {% if icon_url %}
                                    </a>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
            
            <div class="gaming-particle-overlay"></div>
        </div>
    </div>
</section>

<!-- All styles moved to separate SCSS file: src/assets/styles/04-components/first-gallery.scss -->

<!-- JavaScript functionality moved to separate file: src/assets/js/components/first-gallery-animation.js -->