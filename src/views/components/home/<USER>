{#
| Variable       | Type   | Description                  |
|----------------|--------|------------------------------|
| component      | object | Main component data          |
| offer          | object | Offer data (fallback)        |
| timer          | object | Timer data (fallback)        |
#}

{# إعدادات افتراضية #}
{% set config = {
    bg_color: '#1DE9B6',
    days: 5,
    overlay_opacity: '0.7',
    button_text: 'اضغط للعرض',
    button_link: '#',
    image_fit: 'contain',
    timer_enabled: true,
    timer_text_color: '#ffffff',
    offer_button_enabled: true,
    offer_bg_color: '#f5f5f5',
    offer_animation: 'fadeIn'
} %}

{# استخراج بيانات التايمر من الإعدادات #}
{% set timer_data = component.timer[0]|default({}) %}
{% if timer_data.bg is defined %} {% set config = config|merge({bg_color: timer_data.bg}) %} {% endif %}
{% if timer_data.days is defined %} {% set config = config|merge({days: timer_data.days}) %} {% endif %}

{# استخراج بيانات العرض من الإعدادات #}
{% set offer_data = component.offer[0]|default({}) %}
{% set offer_img = offer_data.img|default(null) %}
{% set offer_url = offer_data.url|default('#') %}

{# إعدادات عامة من الكومبوننت #}
{% if component.timer_enabled is defined %} {% set config = config|merge({timer_enabled: component.timer_enabled}) %} {% endif %}
{% if component.timer_text_color is defined %} {% set config = config|merge({timer_text_color: component.timer_text_color}) %} {% endif %}
{% if component.offer_button_enabled is defined %} {% set config = config|merge({offer_button_enabled: component.offer_button_enabled}) %} {% endif %}
{% if component.offer_button_text is defined and component.offer_button_text|length > 0 %} {% set config = config|merge({button_text: component.offer_button_text}) %} {% endif %}
{% if component.offer_bg_color is defined %} {% set config = config|merge({offer_bg_color: component.offer_bg_color}) %} {% endif %}
{% if component.offer_animation is defined %} {% set config = config|merge({offer_animation: component.offer_animation}) %} {% endif %}

{% set unique_id = component.id|default('offer-' ~ random()) %}

<section class="s-block s-block--banner-with-offer container">
    {% if component.title %}
        <div class="s-block__title">
            <div class="right-side">
                <h2>{{ component.title }}</h2>
            </div>
        </div>
    {% endif %}

    <div class="banner-offer-wrapper"
         id="offer-component-{{ unique_id }}"
         style="--offer-bg-color: {{ config.offer_bg_color }};">
        <div class="offer-container {{ config.offer_animation }}"
             style="background-color: var(--offer-bg-color);">
            {# Banner Image #}
            {% if offer_img and offer_img != '' %}
                <img src="{{ offer_img }}" alt="Special Offer"
                     class="banner-image {{ config.image_fit == 'cover' ? 'cover-mode' : (config.image_fit == 'fill' ? 'fill-mode' : (config.image_fit == 'scale-down' ? 'scale-down-mode' : '')) }}"
                     decoding="async"
                     style="opacity: 1 !important; visibility: visible !important; display: block !important;">
            {% else %}
                <div class="banner-image placeholder-banner">
                    <div class="placeholder-content">
                        <div class="placeholder-icon">🎁</div>
                        <h3>عرض خاص</h3>
                        <p>قم بإضافة صورة العرض من لوحة التحكم</p>
                    </div>
                </div>
            {% endif %}

            {# Overlay #}
            <div class="overlay"></div>

            {# Optimized particles #}
            <div class="particles">
                <div class="particle particle-1"></div>
                <div class="particle particle-2"></div>
                <div class="particle particle-3"></div>
            </div>

            {# محتوى العرض #}
            <div class="offer-content">
                {% if config.timer_enabled %}
                    <h3 class="offer-title">العرض ينتهي خلال:</h3>
                    {# Countdown Timer #}
                    <div id="countdown-timer-{{ unique_id }}" class="countdown-timer"
                         style="color: {{ config.timer_text_color }};">
                        <div class="countdown-box">
                            <span id="days-{{ unique_id }}" class="countdown-value">00</span>
                            <span class="countdown-label">أيام</span>
                        </div>
                        <div class="countdown-box">
                            <span id="hours-{{ unique_id }}" class="countdown-value">00</span>
                            <span class="countdown-label">ساعات</span>
                        </div>
                        <div class="countdown-box">
                            <span id="minutes-{{ unique_id }}" class="countdown-value">00</span>
                            <span class="countdown-label">دقائق</span>
                        </div>
                        <div class="countdown-box">
                            <span id="seconds-{{ unique_id }}" class="countdown-value">00</span>
                            <span class="countdown-label">ثواني</span>
                        </div>
                    </div>
                    {# Offer ended badge #}
                    <div id="offer-ended-badge-{{ unique_id }}" class="offer-ended-badge">
                        <div class="badge-pattern"></div>
                        <span class="badge-text">نهاية<br>العرض</span>
                    </div>
                {% endif %}

                {# زر العرض #}
                {% if config.offer_button_enabled %}
                    <a href="{{ offer_url }}" class="offer-cta-button">
                        <span class="button-text">{{ config.button_text }}</span>
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</section>



