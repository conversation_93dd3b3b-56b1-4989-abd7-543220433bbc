{#
| Variable                    | Type     | Description                                                                  |
|-----------------------------|----------|------------------------------------------------------------------------------|
| component                   | object   | Contains merchant settings for fields from twilight.json `component` section |
| component.moving_text       | string   | The text to display in the moving banner                                     |
| component.color             | string   | Background color for the banner (hex color)                                 |
| component.animation_speed   | array    | Animation speed selection (default: 25 seconds)                             |
| position                    | int      | Sorting number start from zero                                               |
#}

{# CSS and JS files are imported in main app.scss and app.js files #}

{% if component.moving_text %}
{% set speed_value = component.animation_speed[0].value|default('25') %}
{% set color_value = component.color|default('#000000') %}
<section class="gaming-moving-text-section s-block--full-bg" id="moving-text-{{ position }}" style="--animation-speed: {{ speed_value }}s; --section-color: {{ color_value }};" data-speed="{{ speed_value }}" data-position="{{ position }}" data-color="{{ color_value }}">
    <div class="gaming-moving-text-container">
        <div class="gaming-moving-text" data-text="{{ component.moving_text }}">
            {{ component.moving_text }}
        </div>
    </div>
</section>

{# JavaScript functionality is handled in the separate JS file #}
{% endif %}