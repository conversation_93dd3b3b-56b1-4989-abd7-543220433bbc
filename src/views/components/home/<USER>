{#
| Variable        | Type      | Description                                                      |
|-----------------|-----------|------------------------------------------------------------------|
| products        | Product[] | List of products                                                 |
| title           | string    | Section title                                                    |
| display_all_url | ?string   | If it's existed, mean should show load more link to this url     |
| position        | Int       | Sorting number start from zero                                   |
#}

<section id="best-offers-{{ position }}-slider" class="s-block s-block--best-offers container overflow-hidden">
  {% if products|length %}
    <salla-products-slider
            source="selected"
            limit="{{ limit }}"
            autoplay
            source-value="[{{ products|map((product) => product.id)|join(',') }}]"
            display-all-url="{{display_all_url}}"
            block-title="{{ title }}"
            block-subTitle="{{ sub_title|raw }}"
            slider-id="slider-with-bg-{{ position }}"
    ></salla-products-slider>
  {% endif %}
</section>
