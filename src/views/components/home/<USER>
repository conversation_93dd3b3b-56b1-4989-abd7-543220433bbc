{#
| Variable                      | Type      | Description                                                         |
|-------------------------------|-----------|---------------------------------------------------------------------|
| component                     | object    | Contains merchant settings for fields from twilight.json            |
| component.stats               | array     | List of statistics                                                  |
| component.stats[].icon        | string    | Statistic icon (SallaIcons css class `sicon-*`)                     |
| component.stats[].title       | string    | Statistic title                                                     |
| component.stats[].number      | integer   | Statistic number value (0-10000)                                    |
| position                      | int       | Sorting number starts from zero                                     |
#}

<style>
/* Store Stats Component - Gaming Theme - Optimized */
:root {
  --color-primary-rgb: 29, 233, 182; /* <PERSON><PERSON> */
  --color-primary: #1DE9B6;
  --color-primary-reverse: #121212;
  --color-dark: #121212;
  --glow-shadow: 0 0 10px rgba(29, 233, 182, 0.5), 0 0 20px rgba(29, 233, 182, 0.3);
}

.s-block--store-stats {
  padding: 3rem 0;
  position: relative;
  overflow: hidden;
  contain: layout style paint; /* Performance optimization */
}

.s-block--store-stats::before {
  content: none;
}

.stats-title-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 3rem;
  margin-top: 2rem;
  position: relative;
  z-index: 2;
  contain: layout style; /* Performance optimization */
}

.stats-title {
  text-align: center;
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  position: relative;
  padding: 0.5rem 2rem;
  color: white;
  text-shadow: 0 0 10px var(--color-primary), 0 0 20px var(--color-primary), 0 0 30px var(--color-primary);
  opacity: 0;
  transform: translateY(-20px);
  animation: titleAppear 1s ease-out forwards;
  letter-spacing: 1px;
}

/* Remove will-change after animation completes */
.stats-title.animation-complete {
  will-change: auto;
}

.stats-title::before,
.stats-title::after {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  border: 2px solid var(--color-primary);
  opacity: 0;
  animation: cornerAppear 1.5s ease-out forwards 0.5s;
  box-shadow: var(--glow-shadow);
}

.stats-title::before {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.stats-title::after {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

.stats-subtitle {
  text-align: center;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto 1.5rem;
  opacity: 0;
  animation: fadeIn 1.5s ease-out forwards 0.8s;
}

/* Remove will-change after animation completes */
.stats-subtitle.animation-complete {
  will-change: auto;
}

/* Consolidated divider styles */
.stats-divider,
.stat-divider {
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
  margin: 0.75rem auto;
  position: relative;
  box-shadow: var(--glow-shadow);
}

.stats-divider {
  width: 80px;
  height: 3px;
  opacity: 0;
  animation: expandDivider 1.5s ease-out forwards 0.3s;
}

/* Remove will-change after animation completes */
.stats-divider.animation-complete {
  will-change: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 5;
  contain: layout style; /* Performance optimization */
}

@media (min-width: 640px) {
  .stats-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.stat-card {
  background-color: rgba(18, 18, 18, 0.7);
  border-radius: 0.5rem;
  box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.3);
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.3s cubic-bezier(0.2, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.2, 0, 0.2, 1);
  border: 1px solid rgba(29, 233, 182, 0.3);
  opacity: 0;
  transform: translateY(30px);
  position: relative;
  overflow: hidden;
  z-index: 1;
  contain: layout style paint; /* Performance optimization */
  transform: translateZ(0); /* Hardware acceleration */
}

/* Remove will-change after animation completes */
.stat-card.animation-complete {
  will-change: auto;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(29, 233, 182, 0.1) 0%, transparent 100%);
  z-index: -1;
  pointer-events: none;
}

/* Simplified grid pattern for better performance */
.stat-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, rgba(29, 233, 182, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(29, 233, 182, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: -1;
  opacity: 0.1;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.stat-card.animate-stat-card {
  animation: statCardAppear 0.8s cubic-bezier(0.2, 0, 0.2, 1) forwards;
}

/* Optimized hover effects - only on devices that support hover */
@media (hover: hover) {
  .stat-card:hover {
    box-shadow: 0 15px 30px rgba(29, 233, 182, 0.2), 0 0 10px rgba(29, 233, 182, 0.1);
    transform: translateY(-8px) translateZ(0); /* Reduced movement for better performance */
    border-color: rgba(29, 233, 182, 0.5);
  }

  .stat-card:hover::after {
    opacity: 0.2;
  }
}

.stat-icon-wrapper {
  position: relative;
  margin-bottom: 1.25rem;
  contain: layout style; /* Performance optimization */
}

.stat-icon {
  margin-bottom: 1.25rem;
  background: rgba(18, 18, 18, 0.7);
  width: 6rem;
  height: 6rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  z-index: 10;
  position: relative;
  transition: transform 0.3s cubic-bezier(0.2, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.2, 0, 0.2, 1);
  box-shadow: 0 0 15px rgba(29, 233, 182, 0.3);
  transform: translateZ(0); /* Hardware acceleration */
  border: 1px solid rgba(29, 233, 182, 0.5);
}

/* Simplified border effect for better performance */
.stat-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(135deg, var(--color-primary), transparent);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0.8;
  pointer-events: none;
}

.stat-icon i {
  color: var(--color-primary);
  font-size: 2.25rem;
  transition: transform 0.3s cubic-bezier(0.2, 0, 0.2, 1), filter 0.3s cubic-bezier(0.2, 0, 0.2, 1);
  filter: drop-shadow(0 0 5px rgba(29, 233, 182, 0.5));
}

/* Optimized hover effects - only on devices that support hover */
@media (hover: hover) {
  .stat-card:hover .stat-icon {
    transform: scale(1.05) translateZ(0); /* Reduced scale for better performance */
    box-shadow: 0 0 20px rgba(29, 233, 182, 0.5), 0 0 40px rgba(29, 233, 182, 0.2);
  }
}

/* Simplified circuit line animation for better performance */
@media (hover: hover) and (prefers-reduced-motion: no-preference) {
  .stat-card:hover .stat-icon::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background: var(--color-primary);
    animation: circuit-line 2s linear infinite;
    opacity: 0.7;
  }
}

/* Simplified circuit animation */
@keyframes circuit-line {
  0% {
    top: 0;
    left: -100%;
  }
  25% {
    top: 0;
    left: 100%;
  }
  50% {
    top: 100%;
    left: 100%;
  }
  75% {
    top: 100%;
    left: -100%;
  }
  100% {
    top: 0;
    left: -100%;
  }
}

/* Simplified glitch effect - only when user prefers motion and device supports hover */
@media (hover: hover) and (prefers-reduced-motion: no-preference) {
  .stat-card:hover .stat-icon i {
    animation: iconGlitch 2s infinite;
    filter: drop-shadow(0 0 10px rgba(29, 233, 182, 0.8));
  }
}

/* Simplified glitch animation */
@keyframes iconGlitch {
  0%, 90%, 100% {
    transform: translate(0);
    opacity: 1;
  }
  2% {
    transform: translate(-2px, 1px);
    opacity: 0.9;
  }
  4% {
    transform: translate(2px, -1px);
    opacity: 1;
  }
  92% {
    transform: translate(1px, 1px);
    opacity: 0.9;
  }
  94% {
    transform: translate(-1px, -1px);
    opacity: 1;
  }
}

.stat-icon-glow {
  position: absolute;
  width: 7rem;
  height: 7rem;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(29, 233, 182, 0.3) 0%, rgba(29, 233, 182, 0) 70%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.2, 0, 0.2, 1), transform 0.3s cubic-bezier(0.2, 0, 0.2, 1);
  z-index: 1;
  pointer-events: none;
}

/* Optimized hover effects - only on devices that support hover */
@media (hover: hover) {
  .stat-card:hover .stat-icon-glow {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.1); /* Reduced scale for better performance */
  }
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  contain: layout style; /* Performance optimization */
}

.stat-title {
  font-weight: 700;
  font-size: 1.125rem;
  color: white;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
  min-height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding-bottom: 0.5rem;
}

.stat-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width 0.3s cubic-bezier(0.2, 0, 0.2, 1);
  box-shadow: var(--glow-shadow);
}

/* Optimized hover effects - only on devices that support hover */
@media (hover: hover) {
  .stat-card:hover .stat-title::after {
    width: 40px;
  }
}

/* Card-specific divider styles */
.stat-card .stat-divider {
  margin-bottom: 1rem;
  width: 0;
  transition: width 0.3s cubic-bezier(0.2, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.2, 0, 0.2, 1);
  opacity: 0;
}

/* Optimized hover effects - only on devices that support hover */
@media (hover: hover) {
  .stat-card:hover .stat-divider {
    width: 60%;
    opacity: 1;
  }
}

.stat-number-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--color-primary);
  text-shadow: 0 0 10px rgba(29, 233, 182, 0.5);
  transition: transform 0.3s cubic-bezier(0.2, 0, 0.2, 1), text-shadow 0.3s cubic-bezier(0.2, 0, 0.2, 1);
  transform: scale(0.95); /* Slightly reduced initial scale */
}

/* Optimized hover effects - only on devices that support hover */
@media (hover: hover) {
  .stat-card:hover .stat-number-wrapper {
    transform: scale(1);
    text-shadow: 0 0 15px rgba(29, 233, 182, 0.7), 0 0 30px rgba(29, 233, 182, 0.4);
  }
}

.stat-number {
  margin-right: 0.25rem;
  position: relative;
}

html[dir="rtl"] .stat-number {
  margin-left: 0.25rem;
  margin-right: 0;
}

.stat-number-plus {
  color: var(--color-primary);
  font-size: 0.8em;
  vertical-align: super;
  margin-left: -5px;
  text-shadow: 0 0 10px rgba(29, 233, 182, 0.5);
}

@keyframes statCardAppear {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes titleAppear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cornerAppear {
  0% {
    opacity: 0;
    width: 0;
    height: 0;
  }
  50% {
    opacity: 1;
    width: 0;
    height: 30px;
  }
  100% {
    opacity: 1;
    width: 30px;
    height: 30px;
  }
}

@keyframes expandDivider {
  from {
    opacity: 0;
    width: 0;
  }
  to {
    opacity: 1;
    width: 80px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .stats-title {
    font-size: 1.5rem;
  }

  .stats-subtitle {
    font-size: 0.875rem;
    padding: 0 1rem;
  }

  .stat-icon {
    width: 5rem;
    height: 5rem;
  }

  .stat-icon i {
    font-size: 1.75rem;
  }

  .stat-icon-glow {
    width: 6rem;
    height: 6rem;
  }

  .stat-number-wrapper {
    font-size: 2rem;
  }

  .stat-title {
    font-size: 1rem;
    min-height: 2.5rem;
  }

  .stat-card {
    padding: 1.5rem 1rem;
  }
}
</style>

<section class="s-block s-block--store-stats">
    <div class="container">
        <div class="stats-title-wrapper">
            <h2 class="stats-title">{{ component.title ? component.title : 'احصائيات المتجر' }}</h2>
            <div class="stats-divider"></div>
            <p class="stats-subtitle">{{ component.subtitle ? component.subtitle : 'تعرف على أرقام متجرنا المميزة والإنجازات التي حققناها' }}</p>
        </div>

        <div class="stats-grid">
            {% if component.stats is defined and component.stats|length > 0 %}
                {% for stat in component.stats %}
                    <div class="stat-card" data-index="{{ loop.index }}">
                        <div class="stat-icon-wrapper">
                            <div class="stat-icon">
                                <i class="{{ stat.icon }}"></i>
                            </div>
                            <div class="stat-icon-glow"></div>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-title">{{ stat.title }}</h3>
                            <div class="stat-divider"></div>
                            <div class="stat-number-wrapper">
                                <span class="stat-number" data-value="{{ stat.number }}">0</span>
                                <span class="stat-number-plus">+</span>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                {# Default stats if none are provided #}
                <div class="stat-card" data-index="1">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon">
                            <i class="sicon-users"></i>
                        </div>
                        <div class="stat-icon-glow"></div>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-title">عملاء سعداء</h3>
                        <div class="stat-divider"></div>
                        <div class="stat-number-wrapper">
                            <span class="stat-number" data-value="1500">0</span>
                            <span class="stat-number-plus">+</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card" data-index="2">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon">
                            <i class="sicon-shopping-bag"></i>
                        </div>
                        <div class="stat-icon-glow"></div>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-title">منتجات متنوعة</h3>
                        <div class="stat-divider"></div>
                        <div class="stat-number-wrapper">
                            <span class="stat-number" data-value="500">0</span>
                            <span class="stat-number-plus">+</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card" data-index="3">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon">
                            <i class="sicon-box-bankers"></i>
                        </div>
                        <div class="stat-icon-glow"></div>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-title">طلبات منجزة</h3>
                        <div class="stat-divider"></div>
                        <div class="stat-number-wrapper">
                            <span class="stat-number" data-value="2500">0</span>
                            <span class="stat-number-plus">+</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card" data-index="4">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon">
                            <i class="sicon-map-pin"></i>
                        </div>
                        <div class="stat-icon-glow"></div>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-title">مدن التوصيل</h3>
                        <div class="stat-divider"></div>
                        <div class="stat-number-wrapper">
                            <span class="stat-number" data-value="50">0</span>
                            <span class="stat-number-plus">+</span>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</section>

<script>
    // Optimized performance with better memory management
    (function() {
        'use strict';

        // Use requestAnimationFrame for better performance
        requestAnimationFrame(function() {
            // Initialize number animations when elements come into view
            const statCards = document.querySelectorAll('.stat-card');
            const statNumbers = document.querySelectorAll('.stat-number');
            const titleElement = document.querySelector('.stats-title');
            const subtitleElement = document.querySelector('.stats-subtitle');
            const dividerElement = document.querySelector('.stats-divider');

            if (!statCards.length) return;

            // Check if user prefers reduced motion
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

            // Clean up will-change after animations complete
            function cleanupWillChange(element, delay = 0) {
                setTimeout(() => {
                    element.classList.add('animation-complete');
                }, delay);
            }

            // Add animation classes with staggered delay - only if animation is preferred
            if (!prefersReducedMotion) {
                statCards.forEach((card, index) => {
                    // Limit maximum delay to improve perceived performance
                    const delay = Math.min(index * 0.15, 0.6);
                    card.style.animationDelay = `${delay}s`;
                    card.classList.add('animate-stat-card');

                    // Clean up will-change after animation
                    cleanupWillChange(card, (delay + 0.8) * 1000);
                });

                // Clean up will-change for title elements
                if (titleElement) cleanupWillChange(titleElement, 1000);
                if (subtitleElement) cleanupWillChange(subtitleElement, 1800);
                if (dividerElement) cleanupWillChange(dividerElement, 1800);
            } else {
                // If reduced motion is preferred, just show the cards without animation
                statCards.forEach(card => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                    card.classList.add('animation-complete');
                });

                // Clean up title elements immediately
                if (titleElement) titleElement.classList.add('animation-complete');
                if (subtitleElement) subtitleElement.classList.add('animation-complete');
                if (dividerElement) dividerElement.classList.add('animation-complete');
            }

            // Use a more efficient animation technique with requestAnimationFrame
            function animateNumberRAF(element, finalValue, duration = 1200) { // Reduced duration
                const startTime = performance.now();
                const startValue = 0;
                const easeOutQuad = t => t * (2 - t);
                let animationId;

                // Use requestAnimationFrame for smoother animation
                function updateNumber(currentTime) {
                    const elapsedTime = currentTime - startTime;
                    const progress = Math.min(elapsedTime / duration, 1);
                    const easedProgress = easeOutQuad(progress);
                    const currentValue = Math.round(startValue + (finalValue - startValue) * easedProgress);

                    element.textContent = currentValue;

                    if (progress < 1) {
                        animationId = requestAnimationFrame(updateNumber);
                    } else {
                        // Clean up
                        if (animationId) {
                            cancelAnimationFrame(animationId);
                        }
                    }
                }

                animationId = requestAnimationFrame(updateNumber);

                // Return cleanup function
                return () => {
                    if (animationId) {
                        cancelAnimationFrame(animationId);
                    }
                };
            }

            // Initialize Intersection Observer for number counting animation
            if ('IntersectionObserver' in window) {
                const cleanupFunctions = new Map();

                // Create a single observer instance for better performance
                const numberObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const numberElement = entry.target;
                            const finalValue = parseInt(numberElement.getAttribute('data-value'));

                            // Animate the number counting with RAF
                            const cleanup = animateNumberRAF(numberElement, finalValue);
                            cleanupFunctions.set(numberElement, cleanup);

                            // Stop observing after animation
                            numberObserver.unobserve(numberElement);
                        }
                    });
                }, {
                    root: null,
                    threshold: 0.1, // Lower threshold for earlier triggering
                    rootMargin: '50px' // Start animation a bit before element comes into view
                });

                // Observe each number element
                statNumbers.forEach(number => {
                    numberObserver.observe(number);
                });

                // Cleanup function for page unload
                window.addEventListener('beforeunload', () => {
                    cleanupFunctions.forEach(cleanup => cleanup());
                    numberObserver.disconnect();
                });
            } else {
                // Fallback for browsers that don't support IntersectionObserver
                statNumbers.forEach(number => {
                    const finalValue = parseInt(number.getAttribute('data-value'));
                    animateNumberRAF(number, finalValue);
                });
            }
        });
    })();
</script>