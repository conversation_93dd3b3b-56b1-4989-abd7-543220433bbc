{#
| Variable                        | Type     | Description                                                                  |
|---------------------------------|----------|------------------------------------------------------------------------------|
| component                       | object   | Contains merchant settings for fields from twilight.json `component` section |
| component.banners               | object[] | list of banners                                                              |
| component.banners[].image       | string   |                                                                              |
| component.banners[].url         | string   |                                                                              |
| component.banners[].title       | ?string  |                                                                              |
| component.banners[].description | ?string  |                                                                              |
| position                        | int      | Sorting number start from zero                                               |
#}

<section class="s-block s-block--banners container">
    <div class="grid {{ component.banners|length <= 3 ? 'md:grid-cols-' ~ component.banners|length : 'md:grid-cols-3 two-row' }} grid-flow-row gap-3 sm:gap-8">
        {% for index, banner in component.banners %}
            <a  href="{{banner.url}}" aria-label={{banner.title ? banner.title : 'square-banner-' ~ index }} class="banner-entry lazy {{banner.title ? 'has-overlay' : ''}} {{ component.banners|length <= 3 ? "h-lg-banner" : "h-banner" }}" data-bg="{{ banner.image }}">
                <article class="banner-entry__text text-with-border">
                    <h3 class="banner__title font-bold mb-1 leading-6">{{banner.title}}</h3>
                    <p class="banner__description">{{banner.description}}</p>
                </article>
            </a>
        {% endfor %}
    </div>
</section>