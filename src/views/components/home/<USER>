{# مكون خط فاصل محسن بثيم الألعاب مع إعدادات قابلة للتخصيص #}
{% set component_class = component_class|default('s-block py-3') %}

{# Get theme settings with defaults - handle both array and direct values #}
{% set line_style = component.line_style is iterable and component.line_style[0] is defined ? component.line_style[0].value : (component.line_style|default('gaming')) %}
{% set line_color = component.line_color|default('#1de9b6') %}
{% set line_secondary_color = component.line_secondary_color|default('#4cc9f0') %}
{% set line_thickness = component.line_thickness|default(2) %}
{% set margin_top = component.margin_top|default(30) %}
{% set margin_bottom = component.margin_bottom|default(30) %}
{% set enable_glow = component.enable_glow|default(true) %}
{% set enable_shine = component.enable_shine|default(true) %}
{% set enable_particles = component.enable_particles|default(true) %}
{% set animation_speed = component.animation_speed is iterable and component.animation_speed[0] is defined ? component.animation_speed[0].value : (component.animation_speed|default('normal')) %}

{# Debug output - remove in production #}
<!-- DEBUG: line_style = {{ line_style }}, animation_speed = {{ animation_speed }} -->

{# Build CSS classes based on settings #}
{% set line_classes = 'divider-line style-' ~ line_style %}
{% if enable_shine %}
    {% set line_classes = line_classes ~ ' enable-shine' %}
{% endif %}

{% set divider_classes = component_class ~ ' gaming-advanced-divider animation-' ~ animation_speed %}

<div class="{{ divider_classes }}"
     role="separator"
     aria-hidden="true"
     data-component="line-break"
     data-line-style="{{ line_style }}"
     data-line-color="{{ line_color }}"
     data-line-secondary-color="{{ line_secondary_color }}"
     data-line-thickness="{{ line_thickness }}"
     data-margin-top="{{ margin_top }}"
     data-margin-bottom="{{ margin_bottom }}"
     data-enable-glow="{{ enable_glow ? 'true' : 'false' }}"
     data-enable-shine="{{ enable_shine ? 'true' : 'false' }}"
     data-enable-particles="{{ enable_particles ? 'true' : 'false' }}"
     data-animation-speed="{{ animation_speed }}"
     style="--line-color: {{ line_color }}; --line-secondary-color: {{ line_secondary_color }}; --line-thickness: {{ line_thickness }}px; margin-top: {{ margin_top }}px; margin-bottom: {{ margin_bottom }}px;">

    <div class="{{ line_classes }}"
         aria-hidden="true"
         {% if line_style == 'gaming' %}style="height: {{ line_thickness }}px;"{% endif %}>

        {% if enable_glow and line_style == 'gaming' %}
            <span class="divider-glow" aria-hidden="true"></span>
        {% endif %}

        {% if enable_particles and line_style == 'gaming' %}
            <span class="divider-particles" aria-hidden="true"></span>
        {% endif %}
    </div>
</div>

