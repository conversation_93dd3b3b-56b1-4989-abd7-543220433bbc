{% if component.gallery_items and component.gallery_items|length > 0 %}
<section class="whatsapp-special-gallery-section" id="special-gallery-{{ component.id }}">
    <!-- العنوان الرئيسي والوصف -->
    {% if component.main_title or component.main_description %}
    <div class="whatsapp-gallery-header">
        {% if component.main_title %}
        <h2 class="whatsapp-gallery-title" style="color: {{ component.title_color|default('#25D366') }}">
            {{ component.main_title }}
        </h2>
        {% endif %}

        {% if component.main_description %}
        <p class="whatsapp-gallery-description">
            {{ component.main_description }}
        </p>
        {% endif %}
    </div>
    {% endif %}

    <!-- عناصر المعرض - بدون كونتينر -->
    <div class="whatsapp-gallery-container">
        {% for item in component.gallery_items %}
        <div class="whatsapp-gallery-item">
            <div class="flex {{ loop.index is odd ? 'md:flex-row' : 'md:flex-row-reverse' }}">

                <!-- صورة العنصر -->
                {% if item.image %}
                <div class="whatsapp-gallery-image-wrapper">
                    <img data-src="{{ item.image }}"
                         alt="{{ item.title|default('صورة المعرض') }}"
                         class="whatsapp-gallery-image"
                         loading="lazy"
                         decoding="async">

                    <!-- تأثير WhatsApp glow overlay -->
                    <div class="whatsapp-gallery-overlay"></div>
                    <div class="whatsapp-gallery-gradient {{ loop.index is odd ? 'gradient-left' : 'gradient-right' }}"></div>
                </div>
                {% endif %}

                <!-- محتوى العنصر مع WhatsApp chat bubble background -->
                <div class="whatsapp-gallery-content-wrapper">
                    <!-- خلفية WhatsApp chat bubble -->
                    <div class="whatsapp-gallery-bubble-bg"></div>

                    <!-- المحتوى -->
                    <div class="whatsapp-gallery-content {{ loop.index is odd ? 'text-right' : 'text-left' }}">
                        {% if item.title %}
                        <h3 class="whatsapp-gallery-item-title" style="color: {{ component.item_title_color|default('#25D366') }}">
                            {{ item.title }}
                        </h3>
                        {% endif %}

                        {% if item.description %}
                        <p class="whatsapp-gallery-item-description" style="color: {{ component.item_description_color|default('#128C7E') }}">
                            {{ item.description }}
                        </p>
                        {% endif %}

                        <!-- زر الرابط -->
                        {% if item.url and item.button_text %}
                        <div>
                            <a href="{{ item.url }}" class="whatsapp-gallery-button">
                                {{ item.button_text|default('اطلب الآن') }}
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</section>




{% endif %}