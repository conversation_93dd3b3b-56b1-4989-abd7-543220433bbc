{#
| Variable                | Type     | Description                                                                  |
|-------------------------|----------|------------------------------------------------------------------------------|
| component               | object   | Contains merchant settings for fields from twilight.json `component` section |
| component.banner        | object[] | list of banners                                                              |
| component.banner[].img  | string   | Banner image URL                                                             |
| component.banner[].url  | object   | Banner link (variable-list format)                                           |
| component.banner[].title| ?string  | Banner title                                                                 |
| component.banner[].subtitle| ?string | Banner subtitle/description                                                |
| position                | int      | Sorting number start from zero                                               |
#}

{# Load optimized external CSS and JS for better performance #}
<link rel="stylesheet" href="{{ 'assets/css/components/new-banners.css' | asset }}" media="all">
<script src="{{ 'assets/js/components/new-banners.js' | asset }}" defer></script>

{# Inline critical CSS to ensure styles are applied immediately #}
<style>
/* Critical Gaming Theme Banner Styles */
.gaming-theme-slider {
    margin-bottom: 2rem;
    will-change: transform;
}

.gaming-banners-section {
    opacity: 0;
    transform: translate3d(0, 20px, 0) scale(0.98);
    transition: opacity 0.6s ease, transform 0.6s ease;
    contain: layout style paint;
}

.gaming-banners-section.animate-in {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
}

.gaming-banner-slide {
    height: 500px;
    position: relative;
    overflow: hidden;
    contain: layout style paint;
}

.gaming-banner-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.gaming-banner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.8s ease;
    will-change: transform;
}

.gaming-banner-link:hover .gaming-banner-image {
    transform: scale3d(1.05, 1.05, 1);
}

.gaming-banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to top,
        rgba(10, 10, 15, 0.9) 0%,
        rgba(10, 10, 15, 0.6) 40%,
        rgba(10, 10, 15, 0.3) 70%,
        rgba(10, 10, 15, 0.1) 100%);
    z-index: 1;
}

.gaming-banner-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 3rem;
    z-index: 2;
    color: #fff;
    text-align: start;
    transform: translate3d(0, 0, 0);
    transition: transform 0.4s ease;
    will-change: transform;
}

.gaming-banner-link:hover .gaming-banner-content {
    transform: translate3d(0, -10px, 0);
}

.animate-element {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
    transition: opacity 0.6s ease, transform 0.6s ease;
    transition-delay: 0s;
}

.animate-element.animate-in {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}

.gaming-banner-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);
    color: #fff;
    position: relative;
    display: inline-block;
}

.gaming-banner-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--color-primary, #1DE9B6);
    box-shadow: 0 0 10px var(--color-primary, #1DE9B6);
    transition: width 0.6s ease 0.6s;
}

.gaming-banner-title.animate-in::after {
    width: 60px;
}

.gaming-banner-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.7);
    margin-bottom: 1.5rem;
    max-width: 600px;
}

.gaming-banner-button {
    display: inline-flex;
    align-items: center;
    background: rgba(29, 233, 182, 0.2);
    border: 1px solid var(--color-primary, #1DE9B6);
    color: #fff;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 0 15px rgba(29, 233, 182, 0.3);
    cursor: pointer;
    will-change: transform, box-shadow;
}

.gaming-banner-button .btn-icon {
    margin-right: 10px;
    margin-left: 10px;
    transition: transform 0.3s ease;
    will-change: transform;
}

.gaming-banner-link:hover .gaming-banner-button {
    background: rgba(29, 233, 182, 0.3);
    box-shadow: 0 0 20px rgba(29, 233, 182, 0.5);
}

.gaming-banner-link:hover .gaming-banner-button .btn-icon {
    transform: translate3d(5px, 0, 0);
}

.gaming-banner-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
    contain: layout style paint;
}

.gaming-particle {
    position: absolute;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(29, 233, 182, 0.15) 0%, rgba(29, 233, 182, 0) 70%);
    border-radius: 50%;
    animation: optimizedFloat 12s infinite ease-in-out;
    animation-delay: var(--delay, 0s);
    opacity: 0;
    will-change: transform, opacity;
}

.particle-1 {
    top: 20%;
    left: 15%;
    width: 100px;
    height: 100px;
}

.particle-2 {
    top: 60%;
    right: 20%;
    width: 80px;
    height: 80px;
}

.gaming-glow {
    position: absolute;
    bottom: -30px;
    left: 0;
    right: 0;
    height: 60px;
    background: radial-gradient(ellipse at center, rgba(29, 233, 182, 0.15) 0%, rgba(29, 233, 182, 0) 70%);
    opacity: 0.5;
}

@keyframes optimizedFloat {
    0% {
        transform: translate3d(0, 0, 0);
        opacity: 0;
    }
    25% {
        opacity: 0.4;
    }
    50% {
        transform: translate3d(10px, -15px, 0);
        opacity: 0.6;
    }
    75% {
        opacity: 0.4;
    }
    100% {
        transform: translate3d(0, 0, 0);
        opacity: 0;
    }
}

/* Responsive */
@media (max-width: 1024px) {
    .gaming-banner-slide { height: 450px; }
    .gaming-banner-title { font-size: 2rem; }
    .gaming-banner-subtitle { font-size: 1.1rem; }
    .gaming-particle { width: 60px; height: 60px; }
}

@media (max-width: 768px) {
    .gaming-banner-slide { height: 350px; }
    .gaming-banner-title { font-size: 1.75rem; }
    .gaming-banner-subtitle { font-size: 1rem; }
    .gaming-banner-content { padding: 2rem; }
    .gaming-banner-button { padding: 0.6rem 1.2rem; }
    .gaming-particle { display: none; }
}

@media (max-width: 480px) {
    .gaming-banner-slide { height: 300px; }
    .gaming-banner-title { font-size: 1.5rem; }
    .gaming-banner-subtitle { font-size: 0.9rem; margin-bottom: 1rem; }
    .gaming-banner-content { padding: 1.5rem; }
    .gaming-banner-button { padding: 0.5rem 1rem; font-size: 0.9rem; }
    .gaming-glow { display: none; }
}

@media (prefers-reduced-motion: reduce) {
    .gaming-particle, .gaming-glow { animation: none; opacity: 0; }
    .gaming-banner-image, .gaming-banner-content, .animate-element { transition: none; }
}
</style>

<section class="s-block s-block--new-banners wide-placeholder gaming-banners-section" id="gaming-banners-section-{{ position }}">
    {% if component.banner|length %}
        <salla-slider
            id="gaming-banners-{{ position }}"
            type="fullwidth"
            auto-play
            pagination
            show-controls="true"
            class="gaming-theme-slider">
            <div slot="items">
                {% for banner in component.banner %}
                    <div class="swiper-slide gaming-banner-slide">
                        <a href="{{ banner.url }}" class="gaming-banner-link">
                            <div class="gaming-banner-image-container">
                                <img src="{{ banner.img }}" alt="{{ banner.title|default('Banner ' ~ loop.index) }}" class="gaming-banner-image" loading="lazy" decoding="async">

                                <div class="gaming-banner-overlay"></div>

                                {% if banner.title or banner.subtitle %}
                                    <div class="gaming-banner-content">
                                        {% if banner.title %}
                                            <h3 class="gaming-banner-title animate-element">{{ banner.title }}</h3>
                                        {% endif %}

                                        {% if banner.subtitle %}
                                            <p class="gaming-banner-subtitle animate-element">{{ banner.subtitle }}</p>
                                        {% endif %}

                                        <div class="gaming-banner-button animate-element">
                                            <span class="btn-text">اكتشف الآن</span>
                                            <span class="btn-icon">
                                                <i class="sicon-arrow-{{ salla.config.get('theme.is_rtl') ? 'left' : 'right' }}"></i>
                                            </span>
                                        </div>
                                    </div>
                                {% endif %}

                                <!-- Optimized Animated Elements - Reduced from 5 to 2 particles -->
                                <div class="gaming-banner-effects">
                                    {% for i in 1..2 %}
                                        <div class="gaming-particle particle-{{ i }}" style="--delay: {{ i * 1 }}s;"></div>
                                    {% endfor %}
                                    <div class="gaming-glow"></div>
                                </div>
                            </div>
                        </a>
                    </div>
                {% endfor %}
            </div>
        </salla-slider>
    {% endif %}
</section>

{# Inline JavaScript to ensure immediate functionality #}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gaming Banners Animation Manager
    class GamingBannersManager {
        constructor() {
            this.observer = null;
            this.animatedElements = new Set();
            this.init();
        }

        init() {
            this.createObserver();
            this.observeBannerSections();
        }

        createObserver() {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.animatedElements.has(entry.target)) {
                        this.animateElement(entry.target);
                        this.animatedElements.add(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.15,
                rootMargin: '50px 0px'
            });
        }

        animateElement(element) {
            requestAnimationFrame(() => {
                element.classList.add('animate-in');

                const animateElements = element.querySelectorAll('.animate-element');
                animateElements.forEach((child, index) => {
                    child.style.transitionDelay = `${0.2 + (index * 0.15)}s`;
                    requestAnimationFrame(() => {
                        child.classList.add('animate-in');
                    });
                });
            });
        }

        observeBannerSections() {
            const bannerSections = document.querySelectorAll('[id^="gaming-banners-section-"]');
            bannerSections.forEach(section => {
                if (section && this.observer) {
                    this.observer.observe(section);
                }
            });
        }
    }

    // Initialize the manager
    new GamingBannersManager();

    // Performance optimization for low-end devices
    const isLowEndDevice = navigator.hardwareConcurrency <= 2 ||
                          navigator.deviceMemory <= 2;

    if (isLowEndDevice) {
        const style = document.createElement('style');
        style.textContent = `
            .gaming-particle,
            .gaming-glow {
                display: none !important;
            }
            .gaming-banner-image {
                transition: none !important;
            }
        `;
        document.head.appendChild(style);
    }
});
</script>

