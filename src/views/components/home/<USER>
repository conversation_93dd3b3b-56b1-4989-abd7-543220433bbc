{#
| Variable                      | Type      | Description                                                         |
|-------------------------------|-----------|---------------------------------------------------------------------|
| items                         | array     |                                                                     |
| items[].title                 | string    | Section title                                                       |
| items[].name                  | string    | Alias for title                                                     |
| items[].type                  | string    | category,most_sales,latest_products,chosen_products                 |
| items[].featured_product      | Product   |                                                                     |
| items[].special_product.id    | string    | Product string id                                                   |
| items[].special_product.title | string    | Product Name                                                        |
| items[].products[]            | Product[] |                                                                     |
| items[].limit                 | Int       | Number of products to be shown                                      |
| items[].id                    | string    | Section id                                                          |
| position                      | int       | Sorting number starts from zero                                     |
#}
<section class="s-block s-block--features-products {% if items | length > 1 %}two-cols{% endif %} container">
    <div class="inner">
        {% for section in items %}
            <div>
                <div class="s-block__title">
                  <h2>{{ section.title }}</h2>
                  {# <p>عنوان فرعي توصيفي صغير</p> #}
                </div> 
                <div class="flex-1 grid lg:grid-cols-2 gap-4 sm:gap-8">
                    {% if section.featured_product %}
                        <custom-salla-product-card shadow-on-hover product="{{section.featured_product}}" fullImage></custom-salla-product-card>
                        <div class="grid gap-4 sm:gap-8">
                            {% for product in section.products|slice(0,3) %}
                                <custom-salla-product-card shadow-on-hover product="{{product}}" minimal></custom-salla-product-card>
                            {% endfor %}
                        </div>
                    {% else %}
                        {% for product in section.products %}
                            <custom-salla-product-card shadow-on-hover product="{{product}}" minimal></custom-salla-product-card>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>
</section>
 
