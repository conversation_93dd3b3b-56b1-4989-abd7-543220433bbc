/* Banner with Offer Component - Optimized for Performance */
:root {
    --gaming-primary: #1DE9B6;
    --gaming-primary-rgb: 29, 233, 182;
    --gaming-bg: #121212;
    --gaming-white: #ffffff;
    --gaming-dark: #333333;
    --gaming-overlay: rgba(18, 18, 18, 0.7);
    --gaming-primary-alpha-10: rgba(29, 233, 182, 0.1);
    --gaming-primary-alpha-20: rgba(29, 233, 182, 0.2);
    --gaming-primary-alpha-30: rgba(29, 233, 182, 0.3);
    --gaming-primary-alpha-40: rgba(29, 233, 182, 0.4);
    --gaming-primary-alpha-60: rgba(29, 233, 182, 0.6);
    --gaming-primary-alpha-80: rgba(29, 233, 182, 0.8);
    --gaming-primary-alpha-90: rgba(29, 233, 182, 0.9);

    /* New dynamic variables */
    --offer-bg-color: #f5f5f5;
    --timer-text-color: #ffffff;
    --timer-bg-color: #1DE9B6;
}

/* ديناميكية لون خلفية العرض */
[id^="offer-component-"], .banner-offer-wrapper {
    background-color: var(--offer-bg-color) !important;
}

/* ديناميكية لون نص العد التنازلي */
[id^="offer-component-"] .countdown-timer {
    color: var(--timer-text-color) !important;
}

/* ديناميكية لون خلفية العد التنازلي */
[id^="offer-component-"] .countdown-box {
    background-color: var(--timer-bg-color) !important;
    border-color: var(--timer-bg-color) !important;
}

/* تطبيق قوي للألوان */
[id^="offer-component-"] .countdown-box * {
    color: var(--timer-text-color) !important;
}

/* تطبيق الألوان على العناصر المحددة */
[id^="offer-component-"] .countdown-value,
[id^="offer-component-"] .countdown-label {
    color: var(--timer-text-color) !important;
}

/* ديناميكية إخفاء الزر أو العد التنازلي */
.hide-offer-btn { display: none !important; }
.hide-timer { display: none !important; }

/* ديناميكية الحركة */
.fadeIn { animation: fadeIn 1s; }
.slideUp { animation: slideUp 1s; }
.scaleIn { animation: scaleIn 1s; }
@keyframes fadeIn {
    from { opacity: 0; }
    to   { opacity: 1; }
}
@keyframes slideUp {
    from { opacity: 0; transform: translateY(40px);}
    to   { opacity: 1; transform: translateY(0);}
}
@keyframes scaleIn {
    from { opacity: 0; transform: scale(0.8);}
    to   { opacity: 1; transform: scale(1);}
}

/* Main component styles */
.s-block--banner-with-offer {
    margin: 40px auto;
    padding: 0 20px;
}

[id^="offer-component-"],
.banner-offer-wrapper {
    display: block !important;
    width: 100% !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
}

[id^="offer-component-"] .offer-container,
.banner-offer-wrapper .offer-container {
    position: relative !important;
    border-radius: 12px;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5), 0 0 20px rgba(29, 233, 182, 0.4);
    overflow: hidden;
    background-color: var(--offer-bg-color) !important;
    border: 1px solid #1DE9B6;
    display: block !important;
    min-height: 400px;
    will-change: transform;
    width: 100% !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

[id^="offer-component-"] .offer-container:hover,
.banner-offer-wrapper .offer-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6), 0 0 30px rgba(29, 233, 182, 0.5);
}

[id^="offer-component-"] .banner-image,
.banner-offer-wrapper .banner-image {
    width: 100% !important;
    height: 400px !important;
    object-fit: cover;
    display: block !important;
    filter: brightness(0.7) contrast(1.1);
    transition: filter 0.3s ease;
    position: relative !important;
}

/* Placeholder banner styles */
[id^="offer-component-"] .placeholder-banner,
.banner-offer-wrapper .placeholder-banner {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #121212 0%, #333333 100%);
    display: flex !important;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

[id^="offer-component-"] .placeholder-banner::before,
.banner-offer-wrapper .placeholder-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        rgba(29, 233, 182, 0.1),
        rgba(29, 233, 182, 0.1) 20px,
        transparent 20px,
        transparent 40px
    );
    opacity: 0.3;
}

[id^="offer-component-"] .placeholder-content,
.banner-offer-wrapper .placeholder-content {
    text-align: center;
    z-index: 1;
    position: relative;
}

[id^="offer-component-"] .placeholder-icon,
.banner-offer-wrapper .placeholder-icon {
    color: #1DE9B6;
    text-shadow: 0 0 20px #1DE9B6;
    animation: pulse 2s infinite;
    font-size: 4rem !important;
    margin-bottom: 1rem;
    display: block;
}

[id^="offer-component-"] .placeholder-content h3,
.banner-offer-wrapper .placeholder-content h3 {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    margin: 1rem 0;
}

[id^="offer-component-"] .placeholder-content p,
.banner-offer-wrapper .placeholder-content p {
    color: #ccc;
    font-size: 1rem;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        text-shadow: 0 0 20px #1DE9B6;
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
        text-shadow: 0 0 30px #1DE9B6, 0 0 40px #1DE9B6;
    }
}

[id^="offer-component-"] .overlay,
.banner-offer-wrapper .overlay {
    position: absolute !important;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        rgba(18, 18, 18, 0.7),
        rgba(18, 18, 18, 0.9)
    ) !important;
    z-index: 1 !important;
}

/* Optimized background pattern */
[id^="offer-component-"] .overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 50 50' width='50' height='50'%3E%3Ccircle cx='10' cy='10' r='2' fill='%231de9b610'/%3E%3Ccircle cx='40' cy='20' r='1.5' fill='%231de9b608'/%3E%3Ccircle cx='25' cy='35' r='1' fill='%231de9b605'/%3E%3C/svg%3E");
    background-size: 50px 50px;
    opacity: 0.3;
}

[id^="offer-component-"] .offer-content,
.banner-offer-wrapper .offer-content {
    position: absolute !important;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2 !important;
    display: flex !important;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    color: #ffffff !important;
    text-align: center;
}

[id^="offer-component-"] .offer-title,
.banner-offer-wrapper .offer-title {
    font-size: 28px !important;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #1DE9B6, 0 0 20px #1DE9B6 !important;
    font-weight: 700;
    letter-spacing: 1px;
    color: #ffffff !important;
    will-change: text-shadow;
}

[id^="offer-component-"] .countdown-timer {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

/* Countdown styles */
[id^="offer-component-"] .countdown-box {
    text-align: center;
    background: var(--timer-bg-color, var(--gaming-overlay));
    color: var(--timer-text-color, var(--gaming-white));
    border-radius: 12px;
    padding: 15px;
    min-width: 70px;
    box-shadow:
        0 0 10px var(--gaming-primary-alpha-60),
        0 0 20px var(--gaming-primary-alpha-20),
        inset 0 0 15px rgba(0, 0, 0, 0.5);
    position: relative;
    overflow: hidden;
    transition: transform 0.3s cubic-bezier(0.2, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.2, 0, 0.2, 1),
              background-color 0.3s ease;
    will-change: transform, box-shadow;
    transform: translateZ(0);
    border: 1px solid var(--timer-bg-color, var(--gaming-primary));
}

[id^="offer-component-"] .countdown-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 0 15px var(--gaming-primary-alpha-90), 0 0 30px var(--gaming-primary-alpha-30);
}

[id^="offer-component-"] .countdown-value {
    font-size: 28px;
    font-weight: bold;
    text-shadow: 0 0 10px var(--timer-bg-color, var(--gaming-primary)), 0 0 5px var(--timer-bg-color, var(--gaming-primary));
    color: var(--timer-text-color, var(--gaming-primary)) !important;
    will-change: transform, text-shadow;
}

[id^="offer-component-"] .countdown-label {
    display: block;
    font-size: 14px;
    margin-top: 5px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--timer-text-color, inherit) !important;
}

/* Offer ended badge */
[id^="offer-component-"] .offer-ended-badge {
    position: absolute;
    top: -25px;
    right: -25px;
    background: linear-gradient(135deg, var(--gaming-bg), var(--gaming-dark));
    color: var(--gaming-primary);
    border-radius: 50%;
    width: 120px;
    height: 120px;
    display: none;
    justify-content: center;
    align-items: center;
    transform: rotate(15deg);
    box-shadow: 0 0 15px var(--gaming-primary-alpha-60), 0 0 30px var(--gaming-primary-alpha-30);
    border: 2px dashed var(--gaming-primary);
    overflow: hidden;
    z-index: 3;
    will-change: transform, opacity;
}

[id^="offer-component-"] .badge-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg, 
        var(--gaming-primary-alpha-10), 
        var(--gaming-primary-alpha-10) 10px, 
        transparent 10px, 
        transparent 20px
    );
}

[id^="offer-component-"] .badge-text {
    font-weight: bold;
    text-align: center;
    font-size: 18px;
    position: relative;
    text-shadow: 0 0 10px var(--gaming-primary), 0 0 20px var(--gaming-primary);
}

/* CTA Button */
[id^="offer-component-"] .offer-cta-button {
    display: inline-block;
    background: var(--gaming-overlay);
    color: var(--gaming-primary);
    padding: 12px 30px;
    border-radius: 50px;
    margin-top: 20px;
    text-decoration: none;
    font-weight: bold;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 10px var(--gaming-primary-alpha-60), 0 0 20px var(--gaming-primary-alpha-20);
    transition: transform 0.3s cubic-bezier(0.2, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.2, 0, 0.2, 1),
              background 0.3s ease;
    will-change: transform, box-shadow;
    transform: translateY(0);
    border: 1px solid var(--gaming-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

[id^="offer-component-"] .offer-cta-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 0 15px var(--gaming-primary-alpha-90), 0 0 30px var(--gaming-primary-alpha-40);
    background: var(--gaming-primary-alpha-20);
    color: var(--gaming-white);
}

[id^="offer-component-"] .offer-cta-button:active {
    transform: translateY(1px);
    box-shadow: 0 0 5px var(--gaming-primary-alpha-60);
}

[id^="offer-component-"] .button-text {
    position: relative;
    z-index: 2;
}

/* Optimized particles */
[id^="offer-component-"] .particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
    pointer-events: none;
}

[id^="offer-component-"] .particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: var(--gaming-primary);
    border-radius: 50%;
    box-shadow: 0 0 8px var(--gaming-primary);
    opacity: 0;
    will-change: transform, opacity;
    transform: translateZ(0);
}

[id^="offer-component-"] .particle-1 {
    top: 20%;
    left: 10%;
    animation: float1 12s infinite ease-in-out;
}

[id^="offer-component-"] .particle-2 {
    top: 60%;
    right: 15%;
    animation: float2 15s infinite ease-in-out 2s;
}

[id^="offer-component-"] .particle-3 {
    bottom: 30%;
    left: 70%;
    animation: float3 18s infinite ease-in-out 4s;
}

[id^="offer-component-"] .particle-4 {
    top: 80%;
    left: 30%;
    animation: float1 20s infinite ease-in-out 6s;
}

[id^="offer-component-"] .particle-5 {
    top: 40%;
    right: 40%;
    animation: float2 14s infinite ease-in-out 8s;
}

/* Optimized animations */
@keyframes float1 {
    0% { opacity: 0; transform: translate(0, 0); }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { opacity: 0; transform: translate(-30px, -80px); }
}

@keyframes float2 {
    0% { opacity: 0; transform: translate(0, 0); }
    15% { opacity: 1; }
    85% { opacity: 1; }
    100% { opacity: 0; transform: translate(40px, -100px); }
}

@keyframes float3 {
    0% { opacity: 0; transform: translate(0, 0); }
    20% { opacity: 1; }
    80% { opacity: 1; }
    100% { opacity: 0; transform: translate(-20px, -120px); }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    [id^="offer-component-"] .countdown-timer {
        gap: 15px;
    }

    [id^="offer-component-"] .countdown-box {
        min-width: 80px;
        padding: 12px;
    }
}

@media (max-width: 992px) {
    .s-block--banner-with-offer {
        padding: 0 15px;
    }

    [id^="offer-component-"] .offer-content {
        padding: 30px 15px;
    }

    [id^="offer-component-"] .offer-title {
        font-size: 24px;
        margin-bottom: 25px;
    }

    [id^="offer-component-"] .countdown-timer {
        gap: 12px;
        margin: 25px 0;
    }
}

@media (max-width: 768px) {
    [id^="offer-component-"] .banner-image,
    [id^="offer-component-"] .placeholder-banner {
        height: 300px;
    }

    [id^="offer-component-"] .offer-container {
        min-height: 300px;
        margin: 15px 0;
    }

    [id^="offer-component-"] .offer-title {
        font-size: 20px;
        margin-bottom: 20px;
    }

    [id^="offer-component-"] .countdown-timer {
        gap: 8px;
        margin: 20px 0;
    }

    [id^="offer-component-"] .countdown-box {
        min-width: 65px;
        padding: 10px 8px;
    }

    [id^="offer-component-"] .countdown-value {
        font-size: 20px;
    }

    [id^="offer-component-"] .countdown-label {
        font-size: 12px;
        margin-top: 4px;
    }

    [id^="offer-component-"] .offer-cta-button {
        padding: 10px 25px;
        font-size: 14px;
        margin-top: 20px;
    }

    /* Reduce particles on mobile */
    [id^="offer-component-"] .particle-4,
    [id^="offer-component-"] .particle-5 {
        display: none;
    }
}

@media (max-width: 576px) {
    [id^="offer-component-"] .banner-image,
    [id^="offer-component-"] .placeholder-banner {
        height: 250px;
    }

    [id^="offer-component-"] .offer-container {
        min-height: 250px;
        margin: 10px 0;
    }

    [id^="offer-component-"] .offer-content {
        padding: 20px 10px;
    }

    [id^="offer-component-"] .offer-title {
        font-size: 18px;
        margin-bottom: 15px;
    }

    [id^="offer-component-"] .countdown-timer {
        gap: 6px;
        margin: 15px 0;
    }

    [id^="offer-component-"] .countdown-box {
        min-width: 55px;
        padding: 8px 6px;
        border-radius: 8px;
    }

    [id^="offer-component-"] .countdown-value {
        font-size: 18px;
    }

    [id^="offer-component-"] .countdown-label {
        font-size: 10px;
        margin-top: 3px;
    }

    [id^="offer-component-"] .offer-cta-button {
        padding: 8px 20px;
        font-size: 13px;
        margin-top: 15px;
    }

    [id^="offer-component-"] .placeholder-content h3 {
        font-size: 1.5rem;
    }

    [id^="offer-component-"] .placeholder-content p {
        font-size: 0.9rem;
    }

    [id^="offer-component-"] .placeholder-icon {
        font-size: 3rem !important;
    }
}

@media (max-width: 400px) {
    [id^="offer-component-"] .countdown-timer {
        gap: 4px;
    }

    [id^="offer-component-"] .countdown-box {
        min-width: 50px;
        padding: 6px 4px;
    }

    [id^="offer-component-"] .countdown-value {
        font-size: 16px;
    }

    [id^="offer-component-"] .countdown-label {
        font-size: 9px;
    }
}

/* Accessibility - Reduced motion */
@media (prefers-reduced-motion: reduce) {
    [id^="offer-component-"] * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
