/* Second Gallery Component - Enhanced Gaming Theme */
:root {
    --gaming-primary: #00ff88;
    --gaming-secondary: #ff0080;
    --gaming-accent: #00d4ff;
    --gaming-primary-rgb: 0, 255, 136;
    --gaming-bg: #0a0a0a;
    --gaming-dark: #1a1a1a;
    --gaming-darker: #0d0d0d;
    --gaming-white: #ffffff;
    --gaming-gray: #666666;
    --gaming-light-gray: #cccccc;

    /* Enhanced alpha variations */
    --gaming-primary-alpha-05: rgba(0, 255, 136, 0.05);
    --gaming-primary-alpha-10: rgba(0, 255, 136, 0.1);
    --gaming-primary-alpha-15: rgba(0, 255, 136, 0.15);
    --gaming-primary-alpha-20: rgba(0, 255, 136, 0.2);
    --gaming-primary-alpha-30: rgba(0, 255, 136, 0.3);
    --gaming-primary-alpha-40: rgba(0, 255, 136, 0.4);
    --gaming-primary-alpha-50: rgba(0, 255, 136, 0.5);
    --gaming-primary-alpha-60: rgba(0, 255, 136, 0.6);
    --gaming-primary-alpha-80: rgba(0, 255, 136, 0.8);
    --gaming-primary-alpha-90: rgba(0, 255, 136, 0.9);

    /* Secondary colors */
    --gaming-secondary-alpha-20: rgba(255, 0, 128, 0.2);
    --gaming-secondary-alpha-30: rgba(255, 0, 128, 0.3);
    --gaming-accent-alpha-20: rgba(0, 212, 255, 0.2);
    --gaming-accent-alpha-30: rgba(0, 212, 255, 0.3);

    /* Enhanced shadows and effects */
    --gaming-shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.3);
    --gaming-shadow-md: 0 4px 20px rgba(0, 0, 0, 0.4);
    --gaming-shadow-lg: 0 8px 40px rgba(0, 0, 0, 0.5);
    --gaming-shadow-glow: 0 0 30px var(--gaming-primary-alpha-40);
    --gaming-shadow-glow-hover: 0 0 50px var(--gaming-primary-alpha-60);

    /* Enhanced transitions */
    --gaming-transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --gaming-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gaming-transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --gaming-transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Main section with enhanced gaming aesthetics */
.gaming-gallery-section {
    background:
        radial-gradient(circle at 20% 80%, var(--gaming-primary-alpha-10) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, var(--gaming-secondary-alpha-20) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, var(--gaming-accent-alpha-20) 0%, transparent 50%),
        linear-gradient(135deg, var(--gaming-bg) 0%, var(--gaming-darker) 50%, var(--gaming-dark) 100%);
    position: relative;
    overflow: hidden;
    padding: 60px 0;
    min-height: 100vh;
}

/* Enhanced animated background pattern */
.gaming-gallery-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 200' width='200' height='200'%3E%3Cdefs%3E%3Cfilter id='glow'%3E%3CfeGaussianBlur stdDeviation='3' result='coloredBlur'/%3E%3CfeMerge%3E%3CfeMergeNode in='coloredBlur'/%3E%3CfeMergeNode in='SourceGraphic'/%3E%3C/feMerge%3E%3C/filter%3E%3C/defs%3E%3Cg filter='url(%23glow)'%3E%3Ccircle cx='50' cy='50' r='2' fill='%2300ff88' opacity='0.6'%3E%3Canimate attributeName='opacity' values='0.3;0.8;0.3' dur='3s' repeatCount='indefinite'/%3E%3C/circle%3E%3Ccircle cx='150' cy='50' r='1.5' fill='%23ff0080' opacity='0.4'%3E%3Canimate attributeName='opacity' values='0.2;0.6;0.2' dur='4s' repeatCount='indefinite'/%3E%3C/circle%3E%3Ccircle cx='100' cy='100' r='1' fill='%2300d4ff' opacity='0.5'%3E%3Canimate attributeName='opacity' values='0.2;0.7;0.2' dur='5s' repeatCount='indefinite'/%3E%3C/circle%3E%3Ccircle cx='50' cy='150' r='1.5' fill='%2300ff88' opacity='0.3'%3E%3Canimate attributeName='opacity' values='0.1;0.5;0.1' dur='6s' repeatCount='indefinite'/%3E%3C/circle%3E%3Ccircle cx='150' cy='150' r='2' fill='%23ff0080' opacity='0.4'%3E%3Canimate attributeName='opacity' values='0.2;0.6;0.2' dur='3.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
    background-size: 200px 200px;
    opacity: 0.3;
    pointer-events: none;
    animation: backgroundFloat 20s ease-in-out infinite;
}

/* Floating animation for background */
@keyframes backgroundFloat {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(-10px, -10px) rotate(1deg); }
    50% { transform: translate(10px, -5px) rotate(-1deg); }
    75% { transform: translate(-5px, 10px) rotate(0.5deg); }
}

/* Additional animated elements */
.gaming-gallery-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, var(--gaming-primary-alpha-05) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, var(--gaming-secondary-alpha-20) 50%, transparent 70%);
    background-size: 300px 300px, 400px 400px;
    animation: gradientShift 15s ease-in-out infinite;
    pointer-events: none;
    opacity: 0.6;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 0%, 100% 100%; }
    50% { background-position: 100% 100%, 0% 0%; }
}

.gaming-gallery-container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
}

/* Enhanced title styling with gaming effects */
.gaming-gallery-title-wrapper {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
}

.gaming-gallery-title-wrapper::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, var(--gaming-primary-alpha-10) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
    animation: titleGlow 4s ease-in-out infinite;
}

@keyframes titleGlow {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
}

.gaming-gallery-title {
    font-size: 3.5rem;
    font-weight: 900;
    background: linear-gradient(45deg, var(--gaming-primary), var(--gaming-accent), var(--gaming-secondary));
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-transform: uppercase;
    letter-spacing: 4px;
    margin: 0 0 30px 0;
    position: relative;
    display: inline-block;
    animation: gradientText 3s ease-in-out infinite;
    filter: drop-shadow(0 0 20px var(--gaming-primary-alpha-50));
}

@keyframes gradientText {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.gaming-gallery-title::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--gaming-primary), var(--gaming-accent));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    z-index: -1;
    filter: blur(2px);
    opacity: 0.7;
}

.gaming-title-underline {
    width: 120px;
    height: 6px;
    background: linear-gradient(90deg,
        transparent,
        var(--gaming-secondary),
        var(--gaming-primary),
        var(--gaming-accent),
        transparent
    );
    background-size: 200% 100%;
    margin: 0 auto;
    border-radius: 3px;
    box-shadow:
        0 0 20px var(--gaming-primary-alpha-60),
        0 0 40px var(--gaming-primary-alpha-30);
    animation: underlineFlow 2s ease-in-out infinite;
}

@keyframes underlineFlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Enhanced gallery slider with gaming aesthetics */
.gaming-gallery-slider {
    position: relative;
    overflow: hidden;
    border-radius: 25px;
    background:
        linear-gradient(145deg,
            rgba(255, 255, 255, 0.03) 0%,
            rgba(255, 255, 255, 0.01) 50%,
            rgba(0, 0, 0, 0.1) 100%
        );
    backdrop-filter: blur(20px);
    border: 2px solid;
    border-image: linear-gradient(45deg,
        var(--gaming-primary-alpha-30),
        var(--gaming-accent-alpha-30),
        var(--gaming-secondary-alpha-30),
        var(--gaming-primary-alpha-30)
    ) 1;
    padding: 50px;
    box-shadow:
        var(--gaming-shadow-lg),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 50px var(--gaming-primary-alpha-15);
    position: relative;
}

.gaming-gallery-slider::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 23px;
    background: linear-gradient(45deg,
        var(--gaming-primary-alpha-05),
        transparent,
        var(--gaming-secondary-alpha-20),
        transparent,
        var(--gaming-accent-alpha-05)
    );
    background-size: 400% 400%;
    animation: borderGlow 6s ease-in-out infinite;
    z-index: -1;
}

@keyframes borderGlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.gaming-gallery-track {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 40px;
    transition: var(--gaming-transition-slow);
}

.gaming-gallery-slider.with-slider .gaming-gallery-track {
    display: flex;
    transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced gallery items with gaming effects */
.gaming-gallery-item {
    position: relative;
    text-align: center;
    transition: var(--gaming-transition);
    will-change: transform;
    transform-style: preserve-3d;
}

.gaming-gallery-item::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg,
        var(--gaming-primary-alpha-10),
        var(--gaming-accent-alpha-10),
        var(--gaming-secondary-alpha-10)
    );
    border-radius: 25px;
    opacity: 0;
    transition: var(--gaming-transition);
    z-index: -1;
    filter: blur(10px);
}

.gaming-gallery-item:hover::before {
    opacity: 1;
}

.gaming-gallery-slider.with-slider .gaming-gallery-item {
    flex: 0 0 calc(33.333% - 30px);
    margin: 0 15px;
}

.gaming-gallery-link {
    display: block;
    text-decoration: none;
    color: inherit;
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    transition: var(--gaming-transition-bounce);
    transform-style: preserve-3d;
}

.gaming-gallery-link:hover {
    transform: translateY(-15px) rotateX(5deg) rotateY(2deg);
}

.gaming-gallery-image-wrapper {
    position: relative;
    margin-bottom: 25px;
    border-radius: 20px;
    overflow: hidden;
    background:
        linear-gradient(145deg, var(--gaming-dark) 0%, var(--gaming-darker) 100%);
    box-shadow:
        var(--gaming-shadow-md),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: var(--gaming-transition);
    border: 2px solid var(--gaming-primary-alpha-20);
}

.gaming-gallery-image-wrapper:hover {
    box-shadow:
        var(--gaming-shadow-lg),
        var(--gaming-shadow-glow-hover),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: var(--gaming-primary-alpha-60);
    transform: scale(1.02);
}

.gaming-gallery-image-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle at 30% 30%, var(--gaming-primary-alpha-30) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, var(--gaming-secondary-alpha-30) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, var(--gaming-accent-alpha-20) 0%, transparent 60%);
    opacity: 0;
    transition: var(--gaming-transition-slow);
    z-index: 1;
    animation: glowPulse 4s ease-in-out infinite;
}

@keyframes glowPulse {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

.gaming-gallery-image-wrapper:hover .gaming-gallery-image-glow {
    opacity: 1;
}

.gaming-gallery-image-container {
    position: relative;
    aspect-ratio: 16/10;
    overflow: hidden;
    border-radius: 15px;
}

.gaming-gallery-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        var(--gaming-primary-alpha-05) 0%,
        transparent 50%,
        var(--gaming-secondary-alpha-05) 100%
    );
    z-index: 2;
    opacity: 0;
    transition: var(--gaming-transition);
}

.gaming-gallery-image-wrapper:hover .gaming-gallery-image-container::before {
    opacity: 1;
}

.gaming-gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--gaming-transition-slow);
    filter: brightness(0.85) contrast(1.15) saturate(1.1);
}

.gaming-gallery-image-wrapper:hover .gaming-gallery-image {
    transform: scale(1.08);
    filter: brightness(1) contrast(1.3) saturate(1.3);
}

/* Enhanced link overlay with gaming effects */
.gaming-gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at center,
            rgba(0, 0, 0, 0.8) 0%,
            rgba(0, 0, 0, 0.9) 100%
        );
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--gaming-transition);
    z-index: 3;
    border-radius: 15px;
}

.gaming-gallery-overlay::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, var(--gaming-primary-alpha-20) 0%, transparent 70%);
    border-radius: 50%;
    animation: overlayPulse 2s ease-in-out infinite;
}

@keyframes overlayPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
    50% { transform: translate(-50%, -50%) scale(1.5); opacity: 0.8; }
}

.gaming-gallery-link:hover .gaming-gallery-overlay {
    opacity: 1;
}

.gaming-gallery-link-icon {
    font-size: 2.5rem;
    background: linear-gradient(45deg, var(--gaming-primary), var(--gaming-accent));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 0 15px var(--gaming-primary));
    animation: iconFloat 3s ease-in-out infinite;
    position: relative;
    z-index: 1;
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0) scale(1);
        filter: drop-shadow(0 0 15px var(--gaming-primary));
    }
    50% {
        transform: translateY(-5px) scale(1.1);
        filter: drop-shadow(0 0 25px var(--gaming-primary));
    }
}

/* Enhanced item title with gaming typography */
.gaming-gallery-item-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--gaming-light-gray);
    margin: 0;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.7);
    transition: var(--gaming-transition);
    letter-spacing: 1px;
    text-transform: uppercase;
    position: relative;
    padding: 10px 0;
}

.gaming-gallery-item-title::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--gaming-primary), var(--gaming-accent));
    transition: var(--gaming-transition);
}

.gaming-gallery-link:hover .gaming-gallery-item-title {
    background: linear-gradient(45deg, var(--gaming-primary), var(--gaming-accent));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: none;
    filter: drop-shadow(0 0 10px var(--gaming-primary-alpha-50));
    transform: translateY(-2px);
}

.gaming-gallery-link:hover .gaming-gallery-item-title::before {
    width: 100%;
}

/* Enhanced navigation with gaming aesthetics */
.gaming-gallery-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 50px;
    gap: 30px;
    position: relative;
}

.gaming-gallery-nav::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 80px;
    background: radial-gradient(ellipse, var(--gaming-primary-alpha-10) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
}

.gaming-gallery-prev-btn,
.gaming-gallery-next-btn {
    background:
        linear-gradient(145deg,
            var(--gaming-primary-alpha-15) 0%,
            var(--gaming-primary-alpha-10) 100%
        );
    border: 2px solid var(--gaming-primary-alpha-40);
    color: var(--gaming-primary);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--gaming-transition-bounce);
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
    font-size: 1.2rem;
}

.gaming-gallery-prev-btn::before,
.gaming-gallery-next-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        var(--gaming-primary-alpha-20),
        var(--gaming-accent-alpha-20)
    );
    opacity: 0;
    transition: var(--gaming-transition);
    border-radius: 50%;
}

.gaming-gallery-prev-btn:hover,
.gaming-gallery-next-btn:hover {
    background: var(--gaming-primary);
    color: var(--gaming-dark);
    border-color: var(--gaming-primary);
    box-shadow:
        var(--gaming-shadow-glow),
        0 0 40px var(--gaming-primary-alpha-40);
    transform: scale(1.15) translateY(-2px);
}

.gaming-gallery-prev-btn:hover::before,
.gaming-gallery-next-btn:hover::before {
    opacity: 1;
}

.gaming-gallery-prev-btn:active,
.gaming-gallery-next-btn:active {
    transform: scale(1.05);
}

.gaming-gallery-prev-btn:disabled,
.gaming-gallery-next-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none;
    background: var(--gaming-primary-alpha-05);
    border-color: var(--gaming-primary-alpha-20);
}

/* Enhanced dots with gaming style */
.gaming-gallery-dots {
    display: flex;
    gap: 15px;
    align-items: center;
}

.gaming-gallery-dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 2px solid var(--gaming-primary-alpha-40);
    background: transparent;
    cursor: pointer;
    transition: var(--gaming-transition-bounce);
    position: relative;
    overflow: hidden;
}

.gaming-gallery-dot::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    background: radial-gradient(circle, var(--gaming-primary) 0%, var(--gaming-accent) 100%);
    border-radius: 50%;
    transition: var(--gaming-transition);
}

.gaming-gallery-dot.active,
.gaming-gallery-dot:hover {
    border-color: var(--gaming-primary);
    box-shadow:
        0 0 15px var(--gaming-primary-alpha-60),
        inset 0 0 10px var(--gaming-primary-alpha-20);
    transform: scale(1.2);
}

.gaming-gallery-dot.active::before,
.gaming-gallery-dot:hover::before {
    width: 100%;
    height: 100%;
}

.gaming-gallery-dot.active {
    animation: dotPulse 2s ease-in-out infinite;
}

@keyframes dotPulse {
    0%, 100% { box-shadow: 0 0 15px var(--gaming-primary-alpha-60); }
    50% { box-shadow: 0 0 25px var(--gaming-primary-alpha-80); }
}

/* Enhanced responsive design */
@media (max-width: 1200px) {
    .gaming-gallery-container {
        max-width: 1000px;
        padding: 0 25px;
    }

    .gaming-gallery-title {
        font-size: 3rem;
    }

    .gaming-gallery-slider {
        padding: 40px;
    }
}

@media (max-width: 768px) {
    .gaming-gallery-section {
        padding: 40px 0;
        min-height: auto;
    }

    .gaming-gallery-container {
        padding: 0 20px;
    }

    .gaming-gallery-title-wrapper {
        margin-bottom: 60px;
    }

    .gaming-gallery-title {
        font-size: 2.5rem;
        letter-spacing: 2px;
    }

    .gaming-gallery-slider {
        padding: 30px;
        border-radius: 20px;
    }

    .gaming-gallery-track {
        gap: 30px;
    }

    .gaming-gallery-slider.with-slider .gaming-gallery-item {
        flex: 0 0 calc(50% - 15px);
        margin: 0 7.5px;
    }

    .gaming-gallery-image-wrapper {
        margin-bottom: 20px;
    }

    .gaming-gallery-item-title {
        font-size: 1.2rem;
    }

    .gaming-gallery-nav {
        margin-top: 40px;
        gap: 25px;
    }

    .gaming-gallery-prev-btn,
    .gaming-gallery-next-btn {
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .gaming-gallery-section {
        padding: 30px 0;
    }

    .gaming-gallery-container {
        padding: 0 15px;
    }

    .gaming-gallery-title-wrapper {
        margin-bottom: 40px;
    }

    .gaming-gallery-title {
        font-size: 2rem;
        letter-spacing: 1px;
    }

    .gaming-title-underline {
        width: 80px;
        height: 4px;
    }

    .gaming-gallery-slider {
        padding: 20px;
        border-radius: 15px;
    }

    .gaming-gallery-track {
        gap: 20px;
    }

    .gaming-gallery-slider.with-slider .gaming-gallery-item {
        flex: 0 0 calc(100% - 10px);
        margin: 0 5px;
    }

    .gaming-gallery-image-wrapper {
        margin-bottom: 15px;
        border-radius: 15px;
    }

    .gaming-gallery-image-container {
        border-radius: 12px;
    }

    .gaming-gallery-item-title {
        font-size: 1.1rem;
        letter-spacing: 0.5px;
    }

    .gaming-gallery-nav {
        margin-top: 30px;
        gap: 20px;
    }

    .gaming-gallery-prev-btn,
    .gaming-gallery-next-btn {
        width: 45px;
        height: 45px;
        font-size: 0.9rem;
    }

    .gaming-gallery-dots {
        gap: 12px;
    }

    .gaming-gallery-dot {
        width: 12px;
        height: 12px;
    }

    .gaming-gallery-link-icon {
        font-size: 2rem;
    }
}

/* Enhanced accessibility and performance optimizations */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .gaming-gallery-section::before,
    .gaming-gallery-section::after,
    .gaming-gallery-title-wrapper::before,
    .gaming-gallery-slider::before,
    .gaming-gallery-image-glow,
    .gaming-gallery-overlay::before,
    .gaming-gallery-link-icon {
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gaming-gallery-title {
        background: var(--gaming-white);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .gaming-gallery-image-wrapper {
        border-color: var(--gaming-white);
    }

    .gaming-gallery-prev-btn,
    .gaming-gallery-next-btn,
    .gaming-gallery-dot {
        border-color: var(--gaming-white);
        color: var(--gaming-white);
    }
}

/* Focus styles for keyboard navigation */
.gaming-gallery-link:focus,
.gaming-gallery-prev-btn:focus,
.gaming-gallery-next-btn:focus,
.gaming-gallery-dot:focus {
    outline: 3px solid var(--gaming-primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 6px var(--gaming-primary-alpha-20);
}

/* Print styles */
@media print {
    .gaming-gallery-section {
        background: white !important;
        color: black !important;
    }

    .gaming-gallery-nav,
    .gaming-gallery-overlay {
        display: none !important;
    }

    .gaming-gallery-track {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

/* Performance optimizations */
.gaming-gallery-image,
.gaming-gallery-image-glow,
.gaming-gallery-overlay {
    will-change: transform, opacity;
}

.gaming-gallery-track {
    will-change: transform;
}

.gaming-gallery-item {
    contain: layout style paint;
}

/* Loading state */
.gaming-gallery-image[loading="lazy"] {
    background: linear-gradient(90deg,
        var(--gaming-primary-alpha-10) 25%,
        var(--gaming-primary-alpha-20) 50%,
        var(--gaming-primary-alpha-10) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.gaming-gallery-image[loading="lazy"]:not([src]) {
    min-height: 200px;
}
