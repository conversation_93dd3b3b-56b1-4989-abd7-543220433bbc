/**
 * إعدادات حجم اللوجو في شريط التنقل
 * Navbar Logo Size Settings
 */

(function() {
    'use strict';

    /**
     * تطبيق حجم اللوجو المحدد من الإعدادات
     * Apply the selected logo size from settings
     */
    function applyNavbarLogoSize() {
        // الحصول على قيمة الإعداد من salla.config
        const logoSize = salla.config.get('navbar_logo_size', 'medium');
        
        // إزالة جميع كلاسات الحجم السابقة
        const body = document.body;
        const logoSizeClasses = [
            'navbar-logo-small',
            'navbar-logo-medium', 
            'navbar-logo-large',
            'navbar-logo-extra-large'
        ];
        
        logoSizeClasses.forEach(className => {
            body.classList.remove(className);
        });
        
        // إضافة الكلاس المناسب للحجم المحدد
        const sizeClassMap = {
            'small': 'navbar-logo-small',
            'medium': 'navbar-logo-medium',
            'large': 'navbar-logo-large',
            'extra-large': 'navbar-logo-extra-large'
        };
        
        const targetClass = sizeClassMap[logoSize] || 'navbar-logo-medium';
        body.classList.add(targetClass);
        
        console.log(`تم تطبيق حجم اللوجو: ${logoSize} (${targetClass})`);
    }

    /**
     * تهيئة الإعدادات عند تحميل الصفحة
     * Initialize settings on page load
     */
    function initNavbarLogoSettings() {
        // تطبيق الحجم فور التحميل
        applyNavbarLogoSize();
        
        // مراقبة تغييرات الإعدادات (في حالة التحديث المباشر)
        if (typeof salla !== 'undefined' && salla.event) {
            salla.event.on('settings.updated', function(data) {
                if (data.key === 'navbar_logo_size') {
                    applyNavbarLogoSize();
                }
            });
        }
    }

    /**
     * تشغيل الإعدادات عند جاهزية DOM
     * Run settings when DOM is ready
     */
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initNavbarLogoSettings);
    } else {
        initNavbarLogoSettings();
    }

    /**
     * تشغيل الإعدادات عند جاهزية Salla
     * Run settings when Salla is ready
     */
    if (typeof salla !== 'undefined') {
        salla.onReady(function() {
            initNavbarLogoSettings();
        });
    }

    /**
     * إضافة دعم للتحديث المباشر في معاينة الثيم
     * Add support for live preview updates
     */
    window.updateNavbarLogoSize = function(size) {
        const body = document.body;
        const logoSizeClasses = [
            'navbar-logo-small',
            'navbar-logo-medium', 
            'navbar-logo-large',
            'navbar-logo-extra-large'
        ];
        
        logoSizeClasses.forEach(className => {
            body.classList.remove(className);
        });
        
        const sizeClassMap = {
            'small': 'navbar-logo-small',
            'medium': 'navbar-logo-medium',
            'large': 'navbar-logo-large',
            'extra-large': 'navbar-logo-extra-large'
        };
        
        const targetClass = sizeClassMap[size] || 'navbar-logo-medium';
        body.classList.add(targetClass);
    };

})();
