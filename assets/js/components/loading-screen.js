/**
 * Gaming Loading Screen - Optimized Performance
 * Handles loading screen animations and removal
 */

(function() {
    'use strict';
    
    // Cache DOM elements
    let loader = null;
    let isLoaded = false;
    
    // Performance optimized loader management
    const LoadingScreen = {
        
        init() {
            // Use requestAnimationFrame for better performance
            requestAnimationFrame(() => {
                loader = document.getElementById('gaming-loading-screen');
                if (!loader) return;
                
                this.setupEventListeners();
                this.startProgressAnimation();
            });
        },
        
        setupEventListeners() {
            // Use passive listeners for better performance
            window.addEventListener('load', this.handlePageLoad.bind(this), { passive: true });
            
            // Handle visibility change for performance
            document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this), { passive: true });
        },
        
        startProgressAnimation() {
            if (!loader) return;
            
            const progressBar = loader.querySelector('.gaming-loader-bar');
            if (!progressBar) return;
            
            // Use CSS animations instead of JavaScript for better performance
            progressBar.style.animationPlayState = 'running';
        },
        
        handlePageLoad() {
            if (isLoaded || !loader) return;
            
            isLoaded = true;
            
            // Small delay to ensure smooth transition
            setTimeout(() => {
                this.hideLoader();
            }, 300);
        },
        
        hideLoader() {
            if (!loader) return;
            
            // Use requestAnimationFrame for smooth animation
            requestAnimationFrame(() => {
                loader.classList.add('loaded');
                
                // Clean up after transition
                loader.addEventListener('transitionend', this.removeLoader.bind(this), { 
                    once: true,
                    passive: true 
                });
                
                // Fallback cleanup in case transitionend doesn't fire
                setTimeout(() => {
                    this.removeLoader();
                }, 1000);
            });
        },
        
        removeLoader() {
            if (!loader || !loader.parentNode) return;
            
            try {
                // Use modern remove() method
                loader.remove();
                loader = null;
                
                // Dispatch custom event for other scripts
                document.dispatchEvent(new CustomEvent('loadingScreenRemoved', {
                    detail: { timestamp: Date.now() }
                }));
                
            } catch (error) {
                // Fallback for older browsers
                if (loader.parentNode) {
                    loader.parentNode.removeChild(loader);
                    loader = null;
                }
            }
        },
        
        handleVisibilityChange() {
            if (!loader) return;
            
            // Pause animations when tab is not visible for better performance
            const isVisible = !document.hidden;
            const animatedElements = loader.querySelectorAll('[class*="animation"], [class*="gaming-"]');
            
            animatedElements.forEach(element => {
                if (element.style.animationPlayState !== undefined) {
                    element.style.animationPlayState = isVisible ? 'running' : 'paused';
                }
            });
        },
        
        // Public method to manually hide loader
        hide() {
            if (!isLoaded) {
                isLoaded = true;
                this.hideLoader();
            }
        },
        
        // Public method to check if loader is still visible
        isVisible() {
            return loader && !loader.classList.contains('loaded');
        }
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', LoadingScreen.init.bind(LoadingScreen), { 
            once: true,
            passive: true 
        });
    } else {
        // DOM is already ready
        LoadingScreen.init();
    }
    
    // Expose to global scope for external control
    window.GamingLoadingScreen = LoadingScreen;
    
    // Handle page navigation (for SPAs)
    window.addEventListener('beforeunload', () => {
        if (loader) {
            // Clean up before page unload
            loader.style.display = 'none';
        }
    }, { passive: true });
    
})();

// Performance monitoring (optional - can be removed in production)
if (window.performance && window.performance.mark) {
    window.performance.mark('loading-screen-script-loaded');
}
