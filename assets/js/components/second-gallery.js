/**
 * Second Gallery Component - Optimized Performance
 * Handles gallery slider functionality with links support
 */

(function() {
    'use strict';
    
    // Gallery manager with performance optimizations
    const SecondGallery = {
        
        // Cache for active galleries
        galleries: new Map(),
        
        // Intersection observer for lazy initialization
        observer: null,
        
        init() {
            // Setup intersection observer for performance
            this.setupIntersectionObserver();
            
            // Initialize all galleries
            this.initializeGalleries();
            
            // Setup reduced motion detection
            this.setupReducedMotionDetection();
            
            // Setup resize handler with debouncing
            this.setupResizeHandler();
        },
        
        setupIntersectionObserver() {
            if (!window.IntersectionObserver) {
                // Fallback for older browsers
                this.initializeGalleries();
                return;
            }
            
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const galleryId = entry.target.id;
                        this.initializeGallery(galleryId);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });
        },
        
        initializeGalleries() {
            const galleries = document.querySelectorAll('.gaming-gallery-section[id^="gallery-"]');
            
            galleries.forEach(gallery => {
                if (this.observer) {
                    this.observer.observe(gallery);
                } else {
                    this.initializeGallery(gallery.id);
                }
            });
        },
        
        initializeGallery(galleryId) {
            const gallery = document.getElementById(galleryId);
            if (!gallery || this.galleries.has(galleryId)) return;
            
            const slider = gallery.querySelector('.gaming-gallery-slider.with-slider');
            if (!slider) return;
            
            const track = slider.querySelector('.gaming-gallery-track');
            const items = track.querySelectorAll('.gaming-gallery-item');
            const prevBtn = gallery.querySelector('.gaming-gallery-prev-btn');
            const nextBtn = gallery.querySelector('.gaming-gallery-next-btn');
            const dots = gallery.querySelectorAll('.gaming-gallery-dot');
            
            if (!track || items.length === 0) return;
            
            // Calculate slides configuration
            const itemsPerSlide = this.getItemsPerSlide();
            const totalSlides = Math.ceil(items.length / itemsPerSlide);
            
            // Create gallery instance
            const galleryInstance = {
                gallery,
                slider,
                track,
                items: Array.from(items),
                prevBtn,
                nextBtn,
                dots: Array.from(dots),
                currentSlide: 0,
                totalSlides,
                itemsPerSlide,
                isAnimating: false
            };
            
            // Store gallery instance
            this.galleries.set(galleryId, galleryInstance);
            
            // Setup event listeners
            this.setupEventListeners(galleryInstance);
            
            // Initial update
            this.updateSlider(galleryInstance);
            
            // Setup touch/swipe support
            this.setupTouchSupport(galleryInstance);
        },
        
        getItemsPerSlide() {
            const width = window.innerWidth;
            if (width <= 480) return 1;
            if (width <= 768) return 2;
            return 3;
        },
        
        setupEventListeners(instance) {
            const { prevBtn, nextBtn, dots, gallery } = instance;
            const galleryId = gallery.id;
            
            // Previous button
            if (prevBtn) {
                prevBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.previousSlide(galleryId);
                }, { passive: false });
            }
            
            // Next button
            if (nextBtn) {
                nextBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.nextSlide(galleryId);
                }, { passive: false });
            }
            
            // Dots
            dots.forEach((dot, index) => {
                dot.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.goToSlide(galleryId, index);
                }, { passive: false });
            });
            
            // Keyboard navigation
            gallery.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    this.previousSlide(galleryId);
                } else if (e.key === 'ArrowRight') {
                    e.preventDefault();
                    this.nextSlide(galleryId);
                }
            });
        },
        
        setupTouchSupport(instance) {
            const { track } = instance;
            let startX = 0;
            let currentX = 0;
            let isDragging = false;
            
            // Touch start
            track.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                isDragging = true;
                track.style.transition = 'none';
            }, { passive: true });
            
            // Touch move
            track.addEventListener('touchmove', (e) => {
                if (!isDragging) return;
                
                currentX = e.touches[0].clientX;
                const diffX = currentX - startX;
                
                // Add some resistance
                const resistance = Math.abs(diffX) > 50 ? 0.5 : 1;
                track.style.transform = `translateX(calc(-${instance.currentSlide * 100}% + ${diffX * resistance}px))`;
            }, { passive: true });
            
            // Touch end
            track.addEventListener('touchend', () => {
                if (!isDragging) return;
                
                isDragging = false;
                track.style.transition = '';
                
                const diffX = currentX - startX;
                const threshold = 50;
                
                if (Math.abs(diffX) > threshold) {
                    if (diffX > 0) {
                        this.previousSlide(instance.gallery.id);
                    } else {
                        this.nextSlide(instance.gallery.id);
                    }
                } else {
                    this.updateSlider(instance);
                }
                
                startX = 0;
                currentX = 0;
            }, { passive: true });
        },
        
        previousSlide(galleryId) {
            const instance = this.galleries.get(galleryId);
            if (!instance || instance.isAnimating) return;
            
            instance.currentSlide = Math.max(0, instance.currentSlide - 1);
            this.updateSlider(instance);
        },
        
        nextSlide(galleryId) {
            const instance = this.galleries.get(galleryId);
            if (!instance || instance.isAnimating) return;
            
            instance.currentSlide = Math.min(instance.totalSlides - 1, instance.currentSlide + 1);
            this.updateSlider(instance);
        },
        
        goToSlide(galleryId, slideIndex) {
            const instance = this.galleries.get(galleryId);
            if (!instance || instance.isAnimating || slideIndex === instance.currentSlide) return;
            
            instance.currentSlide = Math.max(0, Math.min(instance.totalSlides - 1, slideIndex));
            this.updateSlider(instance);
        },
        
        updateSlider(instance) {
            if (instance.isAnimating) return;
            
            instance.isAnimating = true;
            
            // Update track position
            const translateX = -instance.currentSlide * 100;
            instance.track.style.transform = `translateX(${translateX}%)`;
            
            // Update navigation buttons
            this.updateNavigation(instance);
            
            // Update dots
            this.updateDots(instance);
            
            // Reset animation flag after transition
            setTimeout(() => {
                instance.isAnimating = false;
            }, 500);
        },
        
        updateNavigation(instance) {
            const { prevBtn, nextBtn, currentSlide, totalSlides } = instance;
            
            if (prevBtn) {
                prevBtn.disabled = currentSlide === 0;
                prevBtn.setAttribute('aria-disabled', currentSlide === 0);
            }
            
            if (nextBtn) {
                nextBtn.disabled = currentSlide === totalSlides - 1;
                nextBtn.setAttribute('aria-disabled', currentSlide === totalSlides - 1);
            }
        },
        
        updateDots(instance) {
            const { dots, currentSlide } = instance;
            
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
                dot.setAttribute('aria-pressed', index === currentSlide);
            });
        },
        
        setupResizeHandler() {
            let resizeTimeout;
            
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    this.handleResize();
                }, 250);
            }, { passive: true });
        },
        
        handleResize() {
            const newItemsPerSlide = this.getItemsPerSlide();
            
            this.galleries.forEach((instance) => {
                if (instance.itemsPerSlide !== newItemsPerSlide) {
                    instance.itemsPerSlide = newItemsPerSlide;
                    instance.totalSlides = Math.ceil(instance.items.length / newItemsPerSlide);
                    instance.currentSlide = Math.min(instance.currentSlide, instance.totalSlides - 1);
                    this.updateSlider(instance);
                }
            });
        },
        
        setupReducedMotionDetection() {
            if (this.prefersReducedMotion()) {
                document.documentElement.classList.add('reduced-motion');
            }
            
            // Listen for changes
            if (window.matchMedia) {
                const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
                mediaQuery.addEventListener('change', () => {
                    if (mediaQuery.matches) {
                        document.documentElement.classList.add('reduced-motion');
                    } else {
                        document.documentElement.classList.remove('reduced-motion');
                    }
                });
            }
        },
        
        prefersReducedMotion() {
            return window.matchMedia && 
                   window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        },
        
        // Public API methods
        getGallery(galleryId) {
            return this.galleries.get(galleryId);
        },
        
        // Cleanup method
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
            }
            
            this.galleries.clear();
        }
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            SecondGallery.init();
        }, { once: true });
    } else {
        SecondGallery.init();
    }
    
    // Expose to global scope
    window.SecondGallery = SecondGallery;
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        SecondGallery.destroy();
    });
    
})();

// Performance monitoring (optional)
if (window.performance && window.performance.mark) {
    window.performance.mark('second-gallery-script-loaded');
}
