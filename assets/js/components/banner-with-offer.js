/**
 * Banner with Offer Component - Optimized Performance
 * Handles countdown timer and offer banner interactions
 */

(function() {
    'use strict';

    // Performance optimized banner manager
    const BannerWithOffer = {

        // Cache for active timers
        activeTimers: new Map(),

        // Animation frame ID for cleanup
        animationFrameId: null,

        init() {
            // Use intersection observer for better performance
            this.setupIntersectionObserver();

            // Initialize all banner components
            this.initializeBanners();

            // Setup reduced motion detection
            this.setupReducedMotionDetection();
        },

        setupIntersectionObserver() {
            if (!window.IntersectionObserver) return;

            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    const bannerId = entry.target.id.replace('offer-component-', '');

                    if (entry.isIntersecting) {
                        this.startTimer(bannerId);
                    } else {
                        this.pauseTimer(bannerId);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });
        },

        initializeBanners() {
            // Find all banner components
            const banners = document.querySelectorAll('[id^="offer-component-"]');

            banners.forEach(banner => {
                const bannerId = banner.id.replace('offer-component-', '');
                let config = window.bannerOfferConfig?.[bannerId];

                // If no config found, try to read from data attributes
                if (!config) {
                    config = this.getConfigFromDataAttributes(banner);
                    if (config) {
                        window.bannerOfferConfig = window.bannerOfferConfig || {};
                        window.bannerOfferConfig[bannerId] = config;
                    }
                }

                if (!config) {
                    console.warn(`No configuration found for banner: ${bannerId}`);
                    return;
                }

                console.log(`🎯 Initializing banner: ${bannerId}`, config);

                // Setup banner
                this.setupBanner(bannerId, config, banner);

                // Observe banner for intersection
                if (this.observer) {
                    this.observer.observe(banner);
                }

                // Apply animation class if enabled
                if (config.offer_animation && config.offer_animation !== 'none') {
                    const container = banner.querySelector('.offer-container');
                    if (container) {
                        container.classList.add(config.offer_animation);
                    }
                }

                // Apply background colors
                if (config.offer_bg_color) {
                    banner.style.setProperty('--offer-bg-color', config.offer_bg_color);
                }

                if (config.timer_bg_color) {
                    banner.style.setProperty('--timer-bg-color', config.timer_bg_color);
                }

                if (config.timer_text_color) {
                    banner.style.setProperty('--timer-text-color', config.timer_text_color);
                }

                // Apply all settings immediately
                this.applyBannerSettings(banner, config);
            });
        },

        applyBannerSettings(banner, config) {
            console.log(`🔧 Applying settings for banner:`, config);

            // Handle offer button visibility and text
            const offerButton = banner.querySelector('.offer-cta-button');
            if (offerButton) {
                if (config.offer_button_enabled === false) {
                    offerButton.style.display = 'none';
                    console.log('🚫 Offer button disabled');
                } else {
                    offerButton.style.display = '';
                    if (config.offer_button_text || config.button_text) {
                        const buttonText = offerButton.querySelector('.button-text');
                        if (buttonText) {
                            const newText = config.offer_button_text || config.button_text;
                            buttonText.textContent = newText;
                            console.log(`✅ Button text updated to: ${newText}`);
                        }
                    }
                }
            }

            // Handle timer visibility and styling
            const timerSection = banner.querySelector('.countdown-timer');
            if (timerSection) {
                if (config.timer_enabled === false) {
                    timerSection.parentElement.style.display = 'none';
                    console.log('🚫 Timer disabled');
                } else {
                    timerSection.parentElement.style.display = '';

                    // Apply timer colors to countdown boxes
                    const countdownBoxes = banner.querySelectorAll('.countdown-box');
                    countdownBoxes.forEach((box, index) => {
                        if (config.timer_bg_color || config.bg_color) {
                            const bgColor = config.timer_bg_color || config.bg_color;
                            box.style.backgroundColor = bgColor;
                            box.style.borderColor = bgColor;
                            console.log(`🎨 Timer box ${index + 1} background: ${bgColor}`);
                        }
                        if (config.timer_text_color) {
                            box.style.color = config.timer_text_color;

                            // Update countdown values and labels color
                            const value = box.querySelector('.countdown-value');
                            const label = box.querySelector('.countdown-label');
                            if (value) value.style.color = config.timer_text_color;
                            if (label) label.style.color = config.timer_text_color;

                            console.log(`🎨 Timer box ${index + 1} text color: ${config.timer_text_color}`);
                        }
                    });

                    console.log('✅ Timer enabled and styled');
                }
            }

            // Apply CSS variables to the banner element
            if (config.timer_bg_color || config.bg_color) {
                const bgColor = config.timer_bg_color || config.bg_color;
                banner.style.setProperty('--timer-bg-color', bgColor);
                banner.style.setProperty('--gaming-primary', bgColor);
            }

            if (config.timer_text_color) {
                banner.style.setProperty('--timer-text-color', config.timer_text_color);
            }

            if (config.offer_bg_color) {
                banner.style.setProperty('--offer-bg-color', config.offer_bg_color);
            }
        },

        getConfigFromDataAttributes(banner) {
            const dataset = banner.dataset;

            return {
                timer_enabled: dataset.timerEnabled === 'true',
                timer_bg_color: dataset.timerBg || '#1DE9B6',
                timer_text_color: dataset.timerTextColor || '#ffffff',
                timer_days: parseInt(dataset.timerDays) || 5,
                offer_button_enabled: dataset.offerButtonEnabled === 'true',
                offer_button_text: dataset.offerButtonText || 'تسوق الآن',
                offer_bg_color: dataset.offerBgColor || '#f5f5f5',
                offer_animation: dataset.offerAnimation || 'fadeIn',
                unique_id: banner.id.replace('offer-component-', ''),
                bg_color: dataset.timerBg || '#1DE9B6', // fallback
                days: parseInt(dataset.timerDays) || 5, // fallback
                button_text: dataset.offerButtonText || 'تسوق الآن' // fallback
            };
        },

        setupBanner(bannerId, config, bannerElement) {
            // Cache DOM elements
            const elements = {
                days: document.getElementById(`days-${bannerId}`),
                hours: document.getElementById(`hours-${bannerId}`),
                minutes: document.getElementById(`minutes-${bannerId}`),
                seconds: document.getElementById(`seconds-${bannerId}`),
                badge: document.getElementById(`offer-ended-badge-${bannerId}`),
                countdown: document.getElementById(`countdown-timer-${bannerId}`),
                banner: bannerElement
            };

            // Validate required elements
            if (!elements.days || !elements.hours || !elements.minutes || !elements.seconds) {
                // Only warn if timer is enabled
                if (config.timer_enabled !== false) {
                    console.error(`Missing timer elements for banner: ${bannerId}`);
                }
                return;
            }

            // Calculate end date using timer_days from config
            const endDate = new Date();
            const timerDays = config.timer_days || config.days || 5;
            endDate.setDate(endDate.getDate() + timerDays);

            console.log(`⏰ Timer setup for ${bannerId}: ${timerDays} days, ends at:`, endDate);

            // Store timer data
            this.activeTimers.set(bannerId, {
                elements,
                config,
                endDate,
                isActive: false,
                previousValues: { days: null, hours: null, minutes: null, seconds: null },
                lastUpdateTime: 0
            });

            // Apply dynamic styles
            this.applyDynamicStyles(bannerElement, config);

            // Update timer immediately to show correct values
            this.updateTimer(bannerId);
        },

        applyDynamicStyles(bannerElement, config) {
            // Create dynamic CSS for this banner
            const style = document.createElement('style');
            const bannerId = bannerElement.id;

            // Use timer_bg_color for timer styling
            const timerBgColor = config.timer_bg_color || config.bg_color || '#1DE9B6';
            const timerTextColor = config.timer_text_color || '#ffffff';
            const offerBgColor = config.offer_bg_color || '#f5f5f5';

            style.textContent = `
                #${bannerId} {
                    --gaming-primary: ${timerBgColor};
                    --gaming-primary-alpha-10: ${timerBgColor}1a;
                    --gaming-primary-alpha-20: ${timerBgColor}33;
                    --gaming-primary-alpha-30: ${timerBgColor}4d;
                    --gaming-primary-alpha-40: ${timerBgColor}66;
                    --gaming-primary-alpha-60: ${timerBgColor}99;
                    --gaming-primary-alpha-80: ${timerBgColor}cc;
                    --gaming-primary-alpha-90: ${timerBgColor}e6;
                    --timer-bg-color: ${timerBgColor};
                    --timer-text-color: ${timerTextColor};
                    --offer-bg-color: ${offerBgColor};
                }
                #${bannerId} .overlay {
                    opacity: ${config.overlay_opacity || config.overlayOpacity || 0.7};
                }
                #${bannerId} .countdown-box {
                    background-color: ${timerBgColor} !important;
                    color: ${timerTextColor} !important;
                    border-color: ${timerBgColor} !important;
                }
                #${bannerId} .countdown-value {
                    color: ${timerTextColor} !important;
                    text-shadow: 0 0 10px ${timerBgColor}, 0 0 5px ${timerBgColor};
                }
                #${bannerId} .countdown-label {
                    color: ${timerTextColor} !important;
                }
            `;

            document.head.appendChild(style);
        },

        startTimer(bannerId) {
            const timerData = this.activeTimers.get(bannerId);
            if (!timerData || timerData.isActive) return;

            // Only start timer if enabled
            if (timerData.config.timer_enabled === false) return;

            timerData.isActive = true;
            this.updateTimer(bannerId);
        },

        pauseTimer(bannerId) {
            const timerData = this.activeTimers.get(bannerId);
            if (!timerData) return;

            timerData.isActive = false;
        },

        updateTimer(bannerId) {
            const timerData = this.activeTimers.get(bannerId);
            if (!timerData || !timerData.isActive) return;

            const now = Date.now();
            const elapsed = now - timerData.lastUpdateTime;

            // Update only every second for performance
            if (elapsed >= 1000) {
                timerData.lastUpdateTime = now;

                const distance = timerData.endDate - now;

                if (distance < 0) {
                    this.handleCountdownEnd(bannerId);
                    return;
                }

                // Calculate time values
                const timeValues = {
                    days: Math.floor(distance / (1000 * 60 * 60 * 24)),
                    hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
                    minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
                    seconds: Math.floor((distance % (1000 * 60)) / 1000)
                };

                // Update only changed values
                this.updateTimeValues(bannerId, timeValues);
            }

            // Continue animation loop
            requestAnimationFrame(() => this.updateTimer(bannerId));
        },

        updateTimeValues(bannerId, timeValues) {
            const timerData = this.activeTimers.get(bannerId);
            if (!timerData) return;

            Object.keys(timeValues).forEach(key => {
                if (timerData.previousValues[key] !== timeValues[key]) {
                    timerData.elements[key].textContent = timeValues[key];
                    timerData.previousValues[key] = timeValues[key];

                    // Animate value change
                    this.animateValueChange(timerData.elements[key], timerData.config);
                }
            });
        },

        animateValueChange(element, config) {
            // Check for reduced motion preference
            if (this.prefersReducedMotion()) return;

            // Use Web Animation API for better performance
            element.animate([
                {
                    transform: 'scale(1)',
                    textShadow: `0 0 10px ${config.bg_color || config.bgColor}, 0 0 5px ${config.bg_color || config.bgColor}`
                },
                {
                    transform: 'scale(1.1)',
                    textShadow: `0 0 20px ${config.bg_color || config.bgColor}, 0 0 10px ${config.bg_color || config.bgColor}`
                },
                {
                    transform: 'scale(1)',
                    textShadow: `0 0 10px ${config.bg_color || config.bgColor}, 0 0 5px ${config.bg_color || config.bgColor}`
                }
            ], {
                duration: 300,
                easing: 'cubic-bezier(0.2, 0, 0.2, 1)'
            });
        },

        handleCountdownEnd(bannerId) {
            const timerData = this.activeTimers.get(bannerId);
            if (!timerData) return;

            // Stop timer
            timerData.isActive = false;

            // Set all values to zero
            Object.keys(timerData.elements).forEach(key => {
                if (key !== 'badge' && key !== 'countdown' && key !== 'banner') {
                    timerData.elements[key].textContent = "0";
                }
            });

            // Show ended badge
            this.showEndedBadge(timerData.elements.badge, timerData.config);

            // Dispatch custom event
            timerData.elements.banner.dispatchEvent(new CustomEvent('offerEnded', {
                detail: { bannerId, timestamp: Date.now() }
            }));
        },

        showEndedBadge(badgeElement, config) {
            if (!badgeElement) return;

            badgeElement.style.display = 'flex';

            if (this.prefersReducedMotion()) return;

            // Animate badge appearance
            badgeElement.animate([
                {
                    transform: 'scale(0) rotate(0deg)',
                    opacity: 0,
                    boxShadow: `0 0 0 ${(config.bg_color || config.bgColor) || '#1DE9B6'}00`
                },
                {
                    transform: 'scale(1.2) rotate(15deg)',
                    opacity: 1,
                    boxShadow: `0 0 30px ${(config.bg_color || config.bgColor) || '#1DE9B6'}80`
                },
                {
                    transform: 'scale(1) rotate(15deg)',
                    opacity: 1,
                    boxShadow: `0 0 15px ${(config.bg_color || config.bgColor) || '#1DE9B6'}60`
                }
            ], {
                duration: 800,
                easing: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                fill: 'forwards'
            });
        },

        setupReducedMotionDetection() {
            // Check for reduced motion preference
            if (this.prefersReducedMotion()) {
                document.documentElement.classList.add('reduced-motion');
            }

            // Listen for changes
            if (window.matchMedia) {
                const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
                mediaQuery.addEventListener('change', () => {
                    if (mediaQuery.matches) {
                        document.documentElement.classList.add('reduced-motion');
                    } else {
                        document.documentElement.classList.remove('reduced-motion');
                    }
                });
            }
        },

        prefersReducedMotion() {
            return window.matchMedia &&
                   window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        },

        // Cleanup method
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
            }

            this.activeTimers.clear();

            if (this.animationFrameId) {
                cancelAnimationFrame(this.animationFrameId);
            }
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            BannerWithOffer.init();
        }, { once: true });
    } else {
        BannerWithOffer.init();
    }

    // Also initialize on window load to catch any late-loaded configurations
    window.addEventListener('load', () => {
        // Re-initialize any banners that might have been missed
        setTimeout(() => {
            BannerWithOffer.initializeBanners();
        }, 100);
    });

    // Expose to global scope for external control
    window.BannerWithOffer = BannerWithOffer;

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        BannerWithOffer.destroy();
    });

})();

// Performance monitoring (optional)
if (window.performance && window.performance.mark) {
    window.performance.mark('banner-with-offer-script-loaded');
}
